# 叶片检测任务管理系统

![img.png](img.png)

## 项目简介

本项目是一个基于 Python 和 PyQt5 的叶片检测任务管理系统，支持配方导入、程序编辑、任务管理、设备校准等功能，适用于自动化检测场景。

## 用户角色
### 1. 操作员
**画像**：负责日常系统操作和质量验证的一线用户
**职责**：
- 任务执行和监控
- 结果验证和确认
- 报告生成和审查
- 基本故障排除和错误处理

### 2. 技术员/维护工程师
**画像**：负责系统配置、校准和维护的技术专家
**职责**：
- 工艺导入和程序编辑
- 设备校准和验证
- 系统诊断和维护
- 硬件故障排除和维修

### 3. 质量检验员
**画像**：负责最终质量决策和合规性的质量保证专家
**职责**：
- 最终质量验证和批准
- 缺陷分类和评估
- 遵守行业标准
- 质量趋势分析和报告

## 主要功能

### 1. 配方导入与管理：
- 支持从本地文件导入工艺文件，自动解析并管理工艺数据。
- 导入后通过表单展示任务信息：任务名、产品名称、批号等信息，便于用户选择对应工艺文件。
- 支持对导入后的配方文件进行编辑和删除。
### 2.程序编辑与保存：
- 提供直观的程序编辑界面。
- 用户可以选择对应的工艺文件，加载其指向的机器人程序。
- 支持机器人程序的执行步骤展示。
- 支持对每一个步骤进行修改配置，可以通过单步修改配置抓取放置步骤、料框的抓取策略，传送带和吹气的速度，修改视觉模块的相机曝光等等。
- 用户可对任意程序进行删除或者新增。
### 3.检测任务队列管理：
- 通过选择配方库的文件夹，自动生成任务号，并自动展示任务详情信息。
- 支持输入任务数量，自定义检测数量。
- 通过任务队列进行任务管理，支持任务查看、删除等功能。
- 支持任务详情查看，展示每一个叶片缺陷的图片，可手动决策和添加备注信息。
- 支持检测结果导出。
### 4.设备校准模块：
- 提供传感器、相机、机器人等设备的单个校准功能，也可批量进行全自动设备校准。
### 5.硬件配置模块：
- 支持对机器人的定位速度、加速度，相机的增益、亮度及曝光，传感器的端口，速度进行设置。
### 6.操作人员模块：
- 用户可根据自己对应的职能，选择对应的登录角色。
### 7.报警信息显示：
- 实时显示设备状态，若检测到异常，指示灯会示警，支持单个设备的屏蔽。
### 8.实时监控模块：
- 支持对于设备内部进行监控，便于查看设备运行状态，各个仪器是否正常。
### 9.动态标签页管理：
- 支持多任务标签页切换，方便用户快速操作。
- 数据持久化，自动保存配方和程序数据（检测结果、缺陷标记、报告导出等）。
### 10.其他支持：
- 多尺度AI缺陷识别（2D图像+3D点云，支持划痕、凹坑等微缺陷自动检测与分类）
- 叶片自动上料与精密定位（机械臂协作，夹具自适应，支持批量处理）
- 检测结果可视化（缺陷热图、2D/3D交互展示、零件号识别与人工确认）
- 历史任务与数据检索（按时间/批次/工单号检索，支持编辑和审计跟踪）
- 多格式报告导出（PDF、Excel、CSV、HTML，支持模板和实时预览）
- 系统日志与审计（启动/停止/异常日志，关键操作记录，支持查询和导出）
- 响应式UI与多设备适配（桌面、平板、移动端，触控优化，信息亭模式）
- 错误处理与用户反馈（清晰错误提示，进度指示，关键操作二次确认，支持撤销/重做）
- 国际化与本地化（多语言界面，日期/时间格式适配，用户持久语言选择）
- 系统可扩展性与合规性（模块化架构，支持新设备类型，符合ISO 9001等行业标准）

## 文件结构

- `blade_inspection/main.py`：主程序入口
- `templates/`：UI 文件及功能模块
- `recipes/`：配方文件存储目录
- `programs_data.json`、`recipes_data.json`：数据持久化文件

## 环境要求

- Python 3.7 及以上
- pip
- PyQt5
- 其他依赖见 `requirements.txt`

## 安装依赖

在项目根目录下运行：
pip install -r requirements.txt

## 运行方法

在项目根目录下，执行：python main.py

首次运行会自动创建所需的数据文件和文件夹。

## 注意事项

- 请确保 `templates/blade.ui` 等 UI 文件存在，否则程序无法启动。
- 推荐在 Windows 环境下运行，部分路径写死为 Windows 格式。

