{"name": "S88.88.8888", "validated": true, "process": [{"tag": "robot safety position", "name": "Robot_1", "kind": "fake_kuka_robot", "commands_list": [{"command": "joint_move", "kwargs": {"joint_pos": [-132.955902099609, -30.9456787109375, 53.33203125, 49.2403564453125, 83.480224609375, 144.491455078125], "is_block": false, "move_mode": 0, "speed": 100.0, "radian": false}}]}, {"tag": "check blade unload", "name": "PLC_1", "kind": "fake_blade_plc", "commands_list": [{"command": "parse_plc_command", "kwargs": {"command_name": "check blade unload"}}]}, {"tag": "ref linear", "name": "Robot_1", "kind": "general_kuka_robot", "commands_list": [{"command": "linear_move", "kwargs": {"end_pos": [0.0, 0.0, 100.0, 0.0, 0.0, 0.0], "is_block": false, "move_mode": 1, "speed": 30.0}}]}, {"tag": "get process", "name": "Gripper_1", "kind": "fake_gripper_ops", "commands_list": [{"command": "get_process", "kwargs": {"speed": 20}}]}, {"tag": "ref linear", "name": "Robot_1", "kind": "general_kuka_robot", "commands_list": [{"command": "linear_move", "kwargs": {"end_pos": [0.0, 0.0, 100.0, 0.0, 0.0, 0.0], "is_block": false, "move_mode": 1, "speed": 30.0}}]}, {"tag": "safety position above the tray", "name": "Robot_1", "kind": "fake_kuka_robot", "commands_list": [{"command": "joint_move", "kwargs": {"joint_pos": [-132.955902099609, -30.9456787109375, 53.33203125, 49.2403564453125, 83.480224609375, 144.491455078125], "is_block": false, "move_mode": 0, "speed": 100.0, "radian": false}}]}, {"tag": "blowing safety position", "name": "Robot_1", "kind": "fake_kuka_robot", "commands_list": [{"command": "joint_move", "kwargs": {"joint_pos": [-132.955902099609, -30.9456787109375, 53.33203125, 49.2403564453125, 83.480224609375, 144.491455078125], "is_block": false, "move_mode": 0, "speed": 100.0, "radian": false}}]}, {"tag": "ref linear", "name": "Robot_1", "kind": "general_kuka_robot", "commands_list": [{"command": "linear_move", "kwargs": {"end_pos": [0.0, 0.0, 100.0, 0.0, 0.0, 0.0], "is_block": false, "move_mode": 1, "speed": 30.0}}]}, {"tag": "blowing position", "name": "Robot_1", "kind": "fake_kuka_robot", "commands_list": [{"command": "joint_move", "kwargs": {"joint_pos": [-132.955902099609, -30.9456787109375, 53.33203125, 49.2403564453125, 83.480224609375, 144.491455078125], "is_block": false, "move_mode": 0, "speed": 100.0, "radian": false}}]}, {"tag": "start blowing air", "name": "PLC_1", "kind": "fake_blade_plc", "commands_list": [{"command": "parse_plc_command", "kwargs": {"command_name": "start blowing air"}}]}, {"tag": "check blow air completed", "name": "PLC_1", "kind": "fake_blade_plc", "commands_list": [{"command": "parse_plc_command", "kwargs": {"command_name": "check blow air completed"}}]}, {"tag": "ref linear", "name": "Robot_1", "kind": "general_kuka_robot", "commands_list": [{"command": "linear_move", "kwargs": {"end_pos": [0.0, 0.0, 100.0, 0.0, 0.0, 0.0], "is_block": false, "move_mode": 1, "speed": 30.0}}]}, {"tag": "blowing safety position", "name": "Robot_1", "kind": "fake_kuka_robot", "commands_list": [{"command": "joint_move", "kwargs": {"joint_pos": [-132.955902099609, -30.9456787109375, 53.33203125, 49.2403564453125, 83.480224609375, 144.491455078125], "is_block": false, "move_mode": 0, "speed": 100.0, "radian": false}}]}, {"tag": "visual inspection safety position", "name": "Robot_1", "kind": "fake_kuka_robot", "commands_list": [{"command": "joint_move", "kwargs": {"joint_pos": [-132.955902099609, -30.9456787109375, 53.33203125, 49.2403564453125, 83.480224609375, 144.491455078125], "is_block": false, "move_mode": 0, "speed": 100.0, "radian": false}}]}, {"tag": "robot to identify furnace batch number", "name": "Robot_1", "kind": "fake_kuka_robot", "commands_list": [{"command": "joint_move", "kwargs": {"joint_pos": [-132.955902099609, -30.9456787109375, 53.33203125, 49.2403564453125, 83.480224609375, 144.491455078125], "is_block": false, "move_mode": 0, "speed": 100.0, "radian": false}}]}, {"tag": "identify furnace batch number", "name": "VISUAL_1", "kind": "fake_visual_control", "commands_list": [{"command": "identify_furnace_batch_number", "kwargs": {}}]}, {"tag": "robot to visual recognition position 1", "name": "Robot_1", "kind": "fake_kuka_robot", "commands_list": [{"command": "joint_move", "kwargs": {"joint_pos": [-132.955902099609, -30.9456787109375, 53.33203125, 49.2403564453125, 83.480224609375, 144.491455078125], "is_block": false, "move_mode": 0, "speed": 100.0, "radian": false}}]}, {"tag": "identify furnace batch number", "name": "VISUAL_1", "kind": "fake_visual_control", "commands_list": [{"command": "visual_inspection_one", "kwargs": {}}]}, {"tag": "robot to visual recognition position 2", "name": "Robot_1", "kind": "fake_kuka_robot", "commands_list": [{"command": "joint_move", "kwargs": {"joint_pos": [-132.955902099609, -30.9456787109375, 53.33203125, 49.2403564453125, 83.480224609375, 144.491455078125], "is_block": false, "move_mode": 0, "speed": 100.0, "radian": false}}]}, {"tag": "identify furnace batch number", "name": "VISUAL_1", "kind": "fake_visual_control", "commands_list": [{"command": "visual_inspection_two", "kwargs": {}}]}, {"tag": "robot to visual recognition position 3", "name": "Robot_1", "kind": "fake_kuka_robot", "commands_list": [{"command": "joint_move", "kwargs": {"joint_pos": [-132.955902099609, -30.9456787109375, 53.33203125, 49.2403564453125, 83.480224609375, 144.491455078125], "is_block": false, "move_mode": 0, "speed": 100.0, "radian": false}}]}, {"tag": "identify furnace batch number", "name": "VISUAL_1", "kind": "fake_visual_control", "commands_list": [{"command": "visual_inspection_three", "kwargs": {}}]}, {"tag": "visual inspection safety position", "name": "Robot_1", "kind": "fake_kuka_robot", "commands_list": [{"command": "joint_move", "kwargs": {"joint_pos": [-132.955902099609, -30.9456787109375, 53.33203125, 49.2403564453125, 83.480224609375, 144.491455078125], "is_block": false, "move_mode": 0, "speed": 100.0, "radian": false}}]}, {"tag": "safety position above the tray", "name": "Robot_1", "kind": "fake_kuka_robot", "commands_list": [{"command": "joint_move", "kwargs": {"joint_pos": [-132.955902099609, -30.9456787109375, 53.33203125, 49.2403564453125, 83.480224609375, 144.491455078125], "is_block": false, "move_mode": 0, "speed": 100.0, "radian": false}}]}, {"tag": "ref linear", "name": "Robot_1", "kind": "general_kuka_robot", "commands_list": [{"command": "linear_move", "kwargs": {"end_pos": [0.0, 0.0, 100.0, 0.0, 0.0, 0.0], "is_block": false, "move_mode": 1, "speed": 30.0}}]}, {"tag": "put process", "name": "Gripper_1", "kind": "fake_gripper_ops", "commands_list": [{"command": "put_process", "kwargs": {"speed": 20}}]}, {"tag": "ref linear", "name": "Robot_1", "kind": "general_kuka_robot", "commands_list": [{"command": "linear_move", "kwargs": {"end_pos": [0.0, 0.0, 100.0, 0.0, 0.0, 0.0], "is_block": false, "move_mode": 1, "speed": 30.0}}]}, {"tag": "safety position above the tray", "name": "Robot_1", "kind": "fake_kuka_robot", "commands_list": [{"command": "joint_move", "kwargs": {"joint_pos": [-132.955902099609, -30.9456787109375, 53.33203125, 49.2403564453125, 83.480224609375, 144.491455078125], "is_block": false, "move_mode": 0, "speed": 100.0, "radian": false}}]}, {"tag": "whether tray flowed", "name": "PLC_1", "kind": "fake_blade_plc", "commands_list": [{"command": "whether_tray_flowed", "kwargs": {}}]}, {"tag": "robot safety position", "name": "Robot_1", "kind": "fake_kuka_robot", "commands_list": [{"command": "joint_move", "kwargs": {"joint_pos": [-132.955902099609, -30.9456787109375, 53.33203125, 49.2403564453125, 83.480224609375, 144.491455078125], "is_block": false, "move_mode": 0, "speed": 100.0, "radian": false}}]}]}