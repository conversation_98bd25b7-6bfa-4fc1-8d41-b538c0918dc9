#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查Python文件语法
"""

import ast
import sys

def check_syntax(file_path):
    """检查Python文件语法"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            source_code = f.read()
        
        # 尝试解析AST
        ast.parse(source_code)
        print(f"✅ {file_path} 语法正确")
        return True
        
    except SyntaxError as e:
        print(f"❌ {file_path} 语法错误:")
        print(f"   行 {e.lineno}: {e.text}")
        print(f"   错误: {e.msg}")
        return False
    except Exception as e:
        print(f"❌ {file_path} 检查失败: {e}")
        return False

def main():
    """主函数"""
    file_path = "blade_inspection/templates/task_management_module.py"
    
    print(f"🔍 检查文件语法: {file_path}")
    
    if check_syntax(file_path):
        print("🎉 语法检查通过！")
        return 0
    else:
        print("💥 语法检查失败！")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
