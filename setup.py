#!/usr/bin/python
# -*- coding: utf-8 -*-

import typing as t
from setuptools import setup, find_packages
from version import VERSION_TAG

# with open("README.md") as f:
#     readme = f.read()

with open("LICENSE") as f:
    license = f.read()

with open("requirements.txt") as f:
    requirements = f.read().split("\n")
    
pyd_directories: t.List[str] = [
    "*.pyd",
    "core/*.pyd",
    "actions/*.pyd",
    "devices/*.pyd",
    "nodes/*/*.pyd",
    "utils/*.pyd",
    "templates/*.pyd"
]

setup(
    name="blade_inspection",
    version=VERSION_TAG,
    description="Handao blade inspection.",
    long_description='',
    author="Shuai",
    author_email="<EMAIL>",
    url="",
    license=license,
    classifiers=["Programming Language :: Python :: >=3.10.11",
                 "Programming Language :: Python :: Implementation :: CPython", ],
    packages=find_packages(exclude=("docs")),
    include_package_data=True,
    python_requires=">=3.7.6",
    install_requires=requirements,
    package_data={"blade_inspection": ["configs/*", "templates/*.ui"] + pyd_directories},
    entry_points={
        "console_scripts": ["blade_inspection=blade_inspection.main:main"],
    },
    test_suits="nose.collector",
    tests_require=["nose"],
)
