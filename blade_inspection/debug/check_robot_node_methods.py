#!/usr/bin/python
# -*- coding: utf-8 -*-

"""
调试脚本：检查RobotNode的实际方法名称

这个脚本用于调试RobotNode类的方法，特别是TCP位置相关的方法。
"""

import sys
import os
from blade_inspection.utils.node_utils import get_robot_node


def check_robot_node_methods():
    """检查RobotNode的所有方法"""
    print("🔍 检查RobotNode的方法...")
    
    try:
        # 获取robot_node实例
        robot_node = get_robot_node("robot_1")
        
        if not robot_node:
            print("❌ 无法获取robot_node实例")
            return
        
        print(f"✅ 成功获取robot_node: {type(robot_node).__name__}")
        print(f"   节点ID: {id(robot_node)}")
        
        # 获取所有方法
        all_methods = []
        all_attributes = []
        
        for attr_name in dir(robot_node):
            attr = getattr(robot_node, attr_name, None)
            if callable(attr):
                all_methods.append(attr_name)
            else:
                all_attributes.append(attr_name)
        
        print(f"\n📋 总共找到 {len(all_methods)} 个方法")
        print(f"📋 总共找到 {len(all_attributes)} 个属性")
        
        # 查找TCP相关方法
        tcp_methods = []
        position_methods = []
        joint_methods = []
        
        for method in all_methods:
            if 'tcp' in method.lower():
                tcp_methods.append(method)
            if 'position' in method.lower():
                position_methods.append(method)
            if 'joint' in method.lower():
                joint_methods.append(method)
        
        print(f"\n🎯 TCP相关方法 ({len(tcp_methods)} 个):")
        for method in tcp_methods:
            print(f"   - {method}")
        
        print(f"\n🎯 Position相关方法 ({len(position_methods)} 个):")
        for method in position_methods:
            print(f"   - {method}")
        
        print(f"\n🎯 Joint相关方法 ({len(joint_methods)} 个):")
        for method in joint_methods:
            print(f"   - {method}")
        
        # 检查常见的方法名
        common_methods = [
            'get_tcp_position', 'get_tcp_pos', 'get_position',
            'get_joint_position', 'get_joint_pos',
            'joint_move', 'linear_move',
            'move_joint', 'move_linear',
            'get_current_position', 'get_current_tcp',
            'read_tcp_position', 'read_position'
        ]
        
        print(f"\n🔍 检查常见方法名:")
        existing_methods = []
        missing_methods = []
        
        for method in common_methods:
            if hasattr(robot_node, method):
                existing_methods.append(method)
                print(f"   ✅ {method}")
            else:
                missing_methods.append(method)
                print(f"   ❌ {method}")
        
        print(f"\n📊 存在的方法: {existing_methods}")
        print(f"📊 缺失的方法: {missing_methods}")
        
        # 检查_robot属性
        if hasattr(robot_node, '_robot'):
            robot_device = robot_node._robot
            print(f"\n🤖 检查_robot属性: {type(robot_device).__name__}")
            
            # 检查_robot的方法
            robot_methods = []
            for attr_name in dir(robot_device):
                attr = getattr(robot_device, attr_name, None)
                if callable(attr) and not attr_name.startswith('_'):
                    robot_methods.append(attr_name)
            
            print(f"🤖 _robot对象的方法 ({len(robot_methods)} 个):")
            for method in robot_methods[:20]:  # 只显示前20个
                print(f"   - {method}")
            if len(robot_methods) > 20:
                print(f"   ... 还有 {len(robot_methods) - 20} 个方法")
            
            # 检查_robot的TCP相关方法
            robot_tcp_methods = []
            robot_position_methods = []
            
            for method in robot_methods:
                if 'tcp' in method.lower():
                    robot_tcp_methods.append(method)
                if 'position' in method.lower():
                    robot_position_methods.append(method)
            
            print(f"\n🤖 _robot的TCP相关方法:")
            for method in robot_tcp_methods:
                print(f"   - {method}")
            
            print(f"\n🤖 _robot的Position相关方法:")
            for method in robot_position_methods:
                print(f"   - {method}")
        
        return robot_node
        
    except Exception as e:
        print(f"❌ 检查方法失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_tcp_methods(robot_node):
    """测试TCP相关方法"""
    print("\n🧪 测试TCP相关方法...")
    
    if not robot_node:
        print("❌ robot_node为空，无法测试")
        return
    
    from hdmtv.core.node import Request
    
    # 测试可能的方法名
    possible_methods = [
        'get_tcp_position',
        'get_tcp_pos', 
        'get_position',
        'get_current_position',
        'read_tcp_position'
    ]
    
    for method_name in possible_methods:
        if hasattr(robot_node, method_name):
            print(f"\n🔄 测试方法: {method_name}")
            try:
                method = getattr(robot_node, method_name)
                
                # 尝试不同的调用方式
                try:
                    # 方式1: 无参数调用
                    result = method()
                    print(f"   ✅ 无参数调用成功: {result}")
                    continue
                except Exception as e1:
                    print(f"   ⚠️ 无参数调用失败: {e1}")
                
                try:
                    # 方式2: 传入Request对象
                    request = Request(data={})
                    result = method(request)
                    print(f"   ✅ Request调用成功: {result}")
                    continue
                except Exception as e2:
                    print(f"   ⚠️ Request调用失败: {e2}")
                
                try:
                    # 方式3: 传入空字典
                    result = method({})
                    print(f"   ✅ 空字典调用成功: {result}")
                    continue
                except Exception as e3:
                    print(f"   ⚠️ 空字典调用失败: {e3}")
                
            except Exception as e:
                print(f"   ❌ 方法调用失败: {e}")
        else:
            print(f"   ❌ 方法不存在: {method_name}")
    
    # 测试_robot对象的方法
    if hasattr(robot_node, '_robot') and robot_node._robot:
        robot_device = robot_node._robot
        print(f"\n🤖 测试_robot对象的方法...")
        
        for method_name in possible_methods:
            if hasattr(robot_device, method_name):
                print(f"\n🔄 测试_robot.{method_name}")
                try:
                    method = getattr(robot_device, method_name)
                    
                    # 尝试调用
                    try:
                        result = method()
                        print(f"   ✅ _robot.{method_name}()成功: {result}")
                    except Exception as e:
                        print(f"   ⚠️ _robot.{method_name}()失败: {e}")
                        
                except Exception as e:
                    print(f"   ❌ 获取_robot.{method_name}失败: {e}")


def main():
    """主函数"""
    print("🚀 RobotNode方法检查工具")
    print("=" * 60)
    
    try:
        # 检查方法
        robot_node = check_robot_node_methods()
        
        # 测试TCP方法
        test_tcp_methods(robot_node)
        
        print("\n" + "=" * 60)
        print("✅ 检查完成")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
