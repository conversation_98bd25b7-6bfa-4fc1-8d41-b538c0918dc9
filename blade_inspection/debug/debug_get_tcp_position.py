#!/usr/bin/python
# -*- coding: utf-8 -*-

"""
调试get_tcp_position方法的问题

这个脚本用于检查robot_node的类型和可用方法，特别是get_tcp_position方法
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from blade_inspection.utils import debug_robot_node, get_robot_node
from hdmtv.core.node import Request


def main_debug():
    """主调试函数"""
    print("🔍 开始调试get_tcp_position问题")
    
    # 1. 调试robot_node基本信息
    print("\n=== 1. 调试robot_node基本信息 ===")
    debug_info = debug_robot_node("robot_1")
    
    if not debug_info["node_exists"]:
        print(f"❌ 节点不存在: {debug_info['error']}")
        return
    
    print(f"✅ 节点存在")
    print(f"📊 节点类型: {debug_info['node_type']}")
    print(f"📊 节点类: {debug_info['node_class']}")
    print(f"📊 有get_tcp_position方法: {debug_info['has_get_tcp_position']}")
    
    # 2. 详细检查可用方法
    print(f"\n=== 2. 可用方法列表 ===")
    methods = debug_info["available_methods"]
    print(f"总共 {len(methods)} 个可用方法:")
    for i, method in enumerate(methods, 1):
        print(f"  {i:2d}. {method}")
    
    # 3. 检查常见机器人方法
    print(f"\n=== 3. 常见机器人方法检查 ===")
    common_methods = debug_info["common_methods"]
    for method, exists in common_methods.items():
        status = "✅" if exists else "❌"
        print(f"  {status} {method}")
    
    # 4. 尝试直接调用get_tcp_position
    print(f"\n=== 4. 尝试直接调用get_tcp_position ===")
    robot_node = get_robot_node("robot_1")
    
    if robot_node:
        # 检查方法是否存在
        if hasattr(robot_node, 'get_tcp_position'):
            print("✅ get_tcp_position方法存在")
            
            try:
                # 尝试调用
                request = Request(data={})
                print(f"🔄 调用robot_node.get_tcp_position(request)...")
                response = robot_node.get_tcp_position(request)
                print(f"✅ 调用成功!")
                print(f"📊 响应类型: {type(response)}")
                print(f"📊 响应内容: {response}")
                
                # 检查响应结构
                if response:
                    if hasattr(response, 'code'):
                        print(f"📊 响应码: {response.code}")
                    if hasattr(response, 'message'):
                        print(f"📊 响应消息: {response.message}")
                    if hasattr(response, 'data'):
                        print(f"📊 响应数据: {response.data}")
                        
            except Exception as e:
                print(f"❌ 调用get_tcp_position失败: {e}")
                import traceback
                traceback.print_exc()
        else:
            print("❌ get_tcp_position方法不存在")
            
            # 查找类似的方法
            print("\n🔍 查找类似的方法:")
            all_methods = debug_info["available_methods"]
            tcp_related = [m for m in all_methods if 'tcp' in m.lower() or 'position' in m.lower()]
            if tcp_related:
                print("发现相关方法:")
                for method in tcp_related:
                    print(f"  - {method}")
            else:
                print("未发现相关方法")
    
    # 5. 检查robot_node的内部结构
    print(f"\n=== 5. 检查robot_node内部结构 ===")
    if robot_node:
        print(f"robot_node的所有属性:")
        all_attrs = debug_info["all_attributes"]
        for attr in all_attrs:
            try:
                value = getattr(robot_node, attr)
                attr_type = type(value).__name__
                print(f"  - {attr}: {attr_type}")
                
                # 如果是_robot属性，进一步检查
                if attr == '_robot' or 'robot' in attr.lower():
                    print(f"    🔍 检查 {attr}:")
                    if hasattr(value, 'get_tcp_position'):
                        print(f"      ✅ {attr} 有 get_tcp_position 方法")
                    else:
                        print(f"      ❌ {attr} 没有 get_tcp_position 方法")
                        
            except Exception as e:
                print(f"  - {attr}: 无法访问 ({e})")
    
    # 6. 尝试其他可能的方法名
    print(f"\n=== 6. 尝试其他可能的方法名 ===")
    possible_methods = [
        'get_tcp_position',
        'getTcpPosition', 
        'get_position',
        'getPosition',
        'current_position',
        'getCurrentPosition',
        'tcp_position',
        'tcpPosition'
    ]
    
    for method_name in possible_methods:
        if hasattr(robot_node, method_name):
            print(f"✅ 找到方法: {method_name}")
            try:
                method = getattr(robot_node, method_name)
                if callable(method):
                    print(f"  📞 {method_name} 是可调用的")
                else:
                    print(f"  📊 {method_name} 不是方法，是属性: {type(method)}")
            except Exception as e:
                print(f"  ❌ 访问 {method_name} 失败: {e}")
        else:
            print(f"❌ 没有方法: {method_name}")


def check_robot_class():
    """检查robot类的结构"""
    print(f"\n=== 7. 检查robot类的结构 ===")
    
    try:
        robot_node = get_robot_node("robot_1")
        if robot_node and hasattr(robot_node, '_robot'):
            robot = robot_node._robot
            print(f"✅ 找到_robot属性: {type(robot)}")
            
            # 检查robot的方法
            robot_methods = [attr for attr in dir(robot) if not attr.startswith('_') and callable(getattr(robot, attr, None))]
            print(f"📊 robot对象的方法 ({len(robot_methods)} 个):")
            for method in robot_methods:
                print(f"  - {method}")
                
            # 检查是否有get_tcp_position
            if hasattr(robot, 'get_tcp_position'):
                print(f"✅ robot对象有get_tcp_position方法")
                
                # 尝试调用
                try:
                    result = robot.get_tcp_position()
                    print(f"✅ 直接调用robot.get_tcp_position()成功: {result}")
                except Exception as e:
                    print(f"❌ 直接调用robot.get_tcp_position()失败: {e}")
            else:
                print(f"❌ robot对象没有get_tcp_position方法")
        else:
            print(f"❌ robot_node没有_robot属性")
            
    except Exception as e:
        print(f"❌ 检查robot类失败: {e}")


def main():
    """主函数"""
    print("🚀 get_tcp_position调试脚本开始")
    
    try:
        main_debug()
        check_robot_class()
        
    except Exception as e:
        print(f"❌ 调试脚本执行失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n✅ get_tcp_position调试脚本完成")


if __name__ == "__main__":
    main()
