#!/usr/bin/python
# -*- coding: utf-8 -*-

from hdmtv.core.flags import Codes as BaseCodes


class Codes(BaseCodes):
    """ Pre-defined codes. """

    # General Codes
    SUCCESS: int = 0
    ERROR: int = -1
    LICENSE_EXPIRED: int = -2
    EMERGENCY_STOP: int = 1
    TIMEOUT: int = 2
    ABORT_ACTIVITY: int = 3
    TASK_NOT_FOUND: int = 4
    TASK_SET_ERROR: int = 5
    PARSE_ERROR: int = 6
    PROCESS_NOT_STARTED: int = 7
    SIMULATION_NOT_VALIDATED: int = 8
    UNSUPPORTED_STATE: int = 9
    PROCESS_RUNNING: int = 10
    ROBOTICS_VALIDATE_ERROR: int = 11
    ROBOTICS_EXEC_ERROR: int = 12
    ROBOTICS_RESET_ERROR: int = 13
    PREPROCESSING_ERROR: int = 14
    POSTPROCESSING_ERROR: int = 15
    UTIL_CONFIG_ERROR: int = 16
    CONDITION_CONFIG_ERROR: int = 17
    VALIDATION_ERROR: int = 18
    EXECUTION_ERROR: int = 19
    ABORT_ERROR: int = 20
    LOCAL_DATA_LOAD_ERROR: int = 21
    LOCAL_DATA_MODIFY_ERROR: int = 22
    SIMULATION_LOAD_ERROR: int = 23
    SIMULATION_INIT_ERROR: int = 24
    SIMULATION_UPDATE_ERROR: int = 25
    MARK_BASE_ERROR: int = 26
    ROBOT_OP_ERROR: int = 27
    UTIL_COMMAND_SEND_ERROR: int = 28
    UTIL_INFO_GET_ERROR: int = 29
    MODIFY_STEP_ERROR: int = 30
    ADD_STEP_ERROR: int = 31
    SUSPEND_STEP_ERROR: int = 32
    SIMULATION_SAVE_ERROR: int = 33
    STEP_DELETE_ERROR: int = 34
    ROLLBACK_STEP_ERROR: int = 35
    FORWARD_STEP_ERROR: int = 36
    JUMP_STEP_ERROR: int = 37
    PROCESS_NEXT_STEP_ERROR: int = 38
    FULL_UNLOAD_SLOT: int = 39
    EMPTY_JOINTS_LOADING: int = 40
    SET_DIAMETER_ERROR: int = 41

    # Robot codes
    ROBOT_ERROR: int = 100
    ROBOT_DISCONNECTED: int = 101
    ROBOT_INITIALIZE_ERROR: int = 102
    ROBOT_COMMAND_NOT_SUPPORTED: int = 103
    ROBOT_WRONG_TCP_TOOL: int = 104
    ROBOT_NOT_AT_SAFE_POS: int = 105
    ROBOT_NOT_AUTO: int = 106
    ROBOT_DIGITAL_OUTPUT_ERROR: int = 107
    ROBOT_JOINT_MOVE_ERROR: int = 108
    ROBOT_LINEAR_MOVE_ERROR: int = 109
    ROBOT_GRIPPER_ERROR: int = 110
    ROBOT_FITTING_GRIPPER_ERROR: int = 111
    ROBOT_ALT_GRIPPER_ERROR: int = 112
    ROBOT_NOT_IN_HOME: int = 113
    ROBOT_DOWNLOAD_FAILED: int = 114
    ROBOT_UPLOAD_FAILED: int = 115
    ROBOT_CHECK_PROGRAM_FAILED: int = 116
    ROBOT_RESET_GRIPPER_FAILED: int = 117

    # Gripper operation codes
    GRIPPER_ERROR: int = 200
    GRIPPER_DISCONNECTED: int = 201
    GRIPPER_INITIALIZE_ERROR: int = 202
    GRIPPER_TOOL_SET_ERROR: int = 203
    GRIPPER_POS_GET_ERROR: int = 204
    GRIPPER_POS_TRANS_ERROR: int = 205
    GRIPPER_POS_NOT_VALID: int = 206
    GRIPPER_PARAM_SET_ERROR: int = 207
    GRIPPER_EYE_ON_HAND_SEND_ERROR: int = 208
    GRIPPER_PROCESS_ERROR: int = 209
    GRIPPER_TRIGGER_ERROR: int = 210
    GRIPPER_RELEASE_ERROR: int = 211
    GRIPPER_PUT_PROCESS_ERROR: int = 212
    GRIPPER_PUT_PROCESS_NOT_SET: int = 213
    GRIPPER_INVALID_UNLOAD_STATE: int = 214
    GRIPPER_CACHE_NOT_INIT: int = 215
    GRIPPER_CACHE_POS_SET_ERROR: int = 216
    GRIPPER_SERIAL_NUMBER_MAP_ERROR: int = 217

    # PLC codes
    PLC_ERROR: int = 300
    PLC_DISCONNECTED: int = 301
    PLC_INITIALIZE_ERROR: int = 302
    PLC_NOT_AUTO: int = 303
    PLC_RESET_ERROR: int = 304
    PLC_CHECK_ERROR: int = 305
    PLC_COMMAND_PARSE_ERROR: int = 306
    PLC_READ_ERROR: int = 307
    PLC_WRITE_ERROR: int = 308
    PLC_COMPARE_ERROR: int = 309
    PLC_ABORT_WAITING: int = 310
    PLC_WELD_ERROR: int = 311
    PLC_SECURITY_DOOR_OPEN: int = 312
    PLC_NOT_READY: int = 313
    PLC_BENDER_NOT_READY: int = 314
    PLC_ROBOT1_NOT_READY: int = 315
    PLC_ROBOT2_NOT_READY: int = 316
    PLC_FLAT_NOT_READY: int = 317
    PLC_START_ERROR: int = 318
    PLC_PIN_USE_ERROR: int = 319
    PLC_PIN_MANY_TIMES_ERROR: int = 320

    # KEYENCE Localize operation codes
    LOCALIZE_ERROR: int = 400
    LOCALIZE_DISCONNECTED: int = 401
    LOCALIZE_INITIALIZE_ERROR: int = 402
    LOCALIZE_TUBE_CONFIG_ERROR: int = 403
    LOCALIZE_START_ERROR: int = 404
    LOCALIZE_ROBOT_ERROR: int = 405
    LOCALIZE_ANGLE_ADJUST_ERROR: int = 406
    LOCALIZE_HORIZONTAL_ADJUST_ERROR: int = 407
    LOCALIZE_VERTICAL_ADJUST_ERROR: int = 408
    LOCALIZE_MATE_ERROR: int = 409
    LOCALIZE_FITTING_NOT_EXIST: int = 410
    LOCALIZE_HEIGHT_ADJUST_FAILED: int = 411

    # Seam track operation codes
    SEAM_TRACK_ERROR: int = 500
    SEAM_TRACK_DISCONNECTED: int = 501
    SEAM_TRACK_INITIALIZE_ERROR: int = 502
    SEAM_TRACK_ROBOT_ERROR: int = 503
    SEAM_TRACK_X_NOT_SATISFIED: int = 504
    SEAM_TRACK_RY_NOT_SATISFIED: int = 505
    SEAM_TRACK_RX_OR_Y_NOT_SATISFIED: int = 506
    SEAM_TRACK_Z_NOT_SATISFIED: int = 507

    # Welding operation codes
    WELDER_ERROR: int = 600
    WELDER_DISCONNECTED: int = 601
    WELDER_INITIALIZE_ERROR: int = 602
    WELDER_FILE_SET_ERROR: int = 603
    WELDER_MODE_SET_ERROR: int = 604

    # Laser printer device codes
    LASER_ERROR: int = 700
    LASER_DISCONNECTED: int = 701
    LASER_INITIALIZE_ERROR: int = 702
    LASER_DATA_RECEIVED_ERROR: int = 703
    LASER_STRING_CHECK_ERROR: int = 704
    LASER_DATA_LIMITED: int = 705
    LASER_RESPONSE_NOT_VALID: int = 706
    LASER_ENABLED_ERROR: int = 707
    LASER_START_ERROR: int = 708
    LASER_STOP_ERROR: int = 709
    LASER_SERIAL_NUMBER_SET_ERROR: int = 710
    LASER_SERIAL_NUMBER_OUT_OF_RANGE: int = 711
    LASER_MARKING_ERROR: int = 712

    # DR device codes
    DR_ERROR: int = 800
    DR_DISCONNECTED: int = 801
    DR_INITIALIZE_ERROR: int = 802
    DR_SN_SET_ERROR: int = 803
    DR_NOT_AT_HOME: int = 804
    DR_WARMING_UP: int = 805
    DR_RESET_ERROR: int = 806
    DR_JOB_NUM_SET_ERROR: int = 807
    DR_WELD_NUM_SET_ERROR: int = 808
    DR_MOVE_ERROR: int = 809
    DR_IMAGE_ACQUIRE_ERROR: int = 810
    DR_TASK_FINISH_ERROR: int = 811
    DR_TIME_OUT: int = 812
    DR_SET_VISUAL_CONTROL_ERROR: int = 813

    # White light device codes
    WHITE_LIGHT_ERROR: int = 900
    WHITE_LIGHT_DISCONNECTED: int = 901
    WHITE_LIGHT_INITIALIZE_ERROR: int = 902
    WHITE_LIGHT_INSPECT_ERROR: int = 903
    WHITE_LIGHT_CLIENT_CONNECT_ERROR: int = 904
    WHITE_LIGHT_REPORT_ERROR: int = 905
    WHITE_LIGHT_PATH_FIND_ERROR: int = 906

    # General codes
    BLADE_INSPECT_ERROR: int = 1000
    TASK_CREATE_ERROR: int = 1001
    PROCESS_LOAD_ERROR: int = 1002
    NO_TASK_INFO_ERROR: int = 1003
    TASK_START_ERROR: int = 1004
    TASK_STOP_ERROR: int = 1005
    TASK_RESTART_ERROR: int = 1006
    TASK_STATUS_ERROR: int = 1007
    TASK_LOG_ERROR: int = 1008
    TASK_LOGS_ERROR: int = 1009

    # OPCUA client codes
    OPCUA_ERROR: int = 1100
    OPCUA_SERVER_DISCONNECTED: int = 1101
    OPCUA_CLIENT_DISCONNECTED: int = 1102
    OPCUA_INITIALIZE_ERROR: int = 1103
    OPCUA_NODE_NOT_FOUND: int = 1104
    OPCUA_VARIABLE_NOT_FOUND: int = 1105
    OPCUA_VARIABLE_SET_ERROR: int = 1106
    OPCUA_ABORT_WAITING: int = 1107

    # Tube bender module codes
    BENDER_ERROR: int = 1200
    BENDER_ROBOT_DISCONNECTED: int = 1201
    BENDER_ROBOT_INITIALIZE_ERROR: int = 1202
    BENDER_PLC_DISCONNECTED: int = 1203
    BENDER_PLC_INITIALIZE_ERROR: int = 1204
    BENDER_PLC_TRIGGER_BENDING_ERROR: int = 1205
    BENDER_PLC_TRIGGER_FLAT_END_ERROR: int = 1206
    BENDER_WHITE_LIGHT_DISCONNECTED: int = 1207
    BENDER_WHITE_LIGHT_INITIALIZE_ERROR: int = 1208
    BENDER_PARALLEL_INITIALIZE_ERROR: int = 1209
    BENDER_PARALLEL_STEP_REGISTER_ERROR: int = 1210
    BENDER_PARALLEL_TRIGGER_ERROR: int = 1211
    BENDER_PARALLEL_RUNNING_ERROR: int = 1212
    BENDER_CHECK_PROGRAM_FAILED: int = 1213

class StateFlags(object):
    STATE_READY: int = 0
    STATE_VALIDATE: int = 1
    STATE_EXECUTE: int = 2
    STATE_ERROR: int = -1


class LicenseFlags(object):
    LICENSE_ERROR: int = -2
    LICENSE_SUCCESS: int = 0
    LICENSE_EXPIRED: int = -1
    LICENSE_LAST_DAYS: int = 1
