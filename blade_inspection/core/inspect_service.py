#!/usr/bin/python
# -*- coding: utf-8 -*-

import os
import typing as t
from injector import singleton
from PyQt5.QtCore import QObject, pyqtSignal, QTimer
from hdmtv.core.config import Config
from hdmtv.core.cdi import get_instance
from hdmtv.node import Client
from hdmtv.core.node import Request, Response
from hdmtv.node import Node, Subscription, Client, Future
from hd_robotics.robotics import Robotics
from .flags import Codes


@singleton
class InspectService(QObject):
    """ Inspect service class, which serves as the backend service for the main window. """

    # 信号定义
    current_num_changed = pyqtSignal(int, int)  # 信号：当前数量变化 (current_num, batch_num)
    batch_completed = pyqtSignal()  # 信号：批次完成
    task_progress_updated = pyqtSignal(int)  # 信号：任务进度更新 (百分比)

    _cache_path: str = os.path.join(os.path.expanduser('~'), 'Documents/.blade_inspection')
    _root_path: str = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
    _config_file: str = os.path.join(_root_path, "configs/config.yaml")
    _device_path: str = os.path.join(_root_path, "devices")
    _node_path: str = os.path.join(_root_path, "nodes")
    _action_path: str = os.path.join(_root_path, "actions")

    def __init__(self, **options) -> None:
        try:
            # 初始化QObject
            super().__init__()

            print("🔄 InspectService初始化开始...")

            # 先初始化基本属性
            self._task_info: t.Optional[t.Dict[str, t.Any]] = None
            self._last_current_num = 0  # 记录上次的current_num，用于检测变化

            # 创建定时器用于监控current_num变化
            self._monitor_timer = QTimer()
            self._monitor_timer.timeout.connect(self._check_current_num_change)
            self._monitor_timer.setInterval(500)  # 每500ms检查一次

            # 使用默认路径进行初始化
            default_cache_path = "D:/blade_inspect"
            cache_path = default_cache_path

            print(f"📄 配置文件存在: {os.path.isfile(InspectService._config_file)}")
            print(f"📄 配置文件路径: {InspectService._config_file}")

            # 先用默认路径创建必要的目录
            log_path: str = os.path.join(cache_path, "log")
            task_path: str = os.path.join(cache_path, "tasks")
            process_path: str = os.path.join(cache_path, "processes")

            for path_name, path in [("log", log_path), ("tasks", task_path), ("processes", process_path)]:
                if not os.path.exists(path):
                    os.makedirs(path, exist_ok=True)
                    print(f"📁 创建目录: {path}")

            # 先初始化Robotics，这会加载配置文件
            print("🔄 初始化Robotics...")
            Robotics.initialize(
                log_path=log_path,
                config_file=InspectService._config_file,
                node_paths=[InspectService._node_path],
                action_paths=[InspectService._action_path],
                device_paths=[InspectService._device_path]
            )
            print("✅ Robotics初始化完成")

            # 现在获取Config实例，此时配置应该已经加载
            try:
                self._config: Config = get_instance(clazz=Config)
                print("✅ Config实例获取完成")

                # 尝试获取配置并更新路径
                try:
                    inspect_config: t.Dict[str, t.Any] = self._config.settings.to_dict().get("INSPECT", dict())
                    print(8888888888, self._config.settings.to_dict())
                    config_cache_path: str = inspect_config.get("cache_path", default_cache_path)
                    if config_cache_path and config_cache_path != cache_path:
                        cache_path = config_cache_path
                        print(f"✅ 从配置更新cache_path: {cache_path}")

                        # 重新创建目录
                        log_path = os.path.join(cache_path, "log")
                        task_path = os.path.join(cache_path, "tasks")
                        process_path = os.path.join(cache_path, "processes")

                        for path_name, path in [("log", log_path), ("tasks", task_path), ("processes", process_path)]:
                            if not os.path.exists(path):
                                os.makedirs(path, exist_ok=True)
                                print(f"📁 创建配置目录: {path}")
                    else:
                        print(f"✅ 使用默认cache_path: {cache_path}")

                except Exception as e:
                    print(f"⚠️ 获取INSPECT配置失败: {e}，继续使用默认配置")

            except Exception as e:
                print(f"⚠️ 获取Config实例失败: {e}，将在load_request中处理")
                self._config = None

            print(f"📁 最终cache_path: {cache_path}")

            self._robotics: Robotics = Robotics(name="blade_inspect")
            print("✅ Robotics实例创建完成")

            self._options = options

            # 从配置中获取robot_1节点的endpoint和namespace
            try:
                config_dict = self._config.settings.to_dict()
                nodes_config = config_dict.get("NODES", [])
                robot_config = next((node for node in nodes_config
                                   if node.get("node_name") == "robot_1"), None)
                endpoint = robot_config.get("endpoint", "localhost")
                namespace = robot_config.get("namespace", "ns")
            except Exception as e:
                print(f"⚠️ 获取配置失败: {e}，使用默认值")
                endpoint = "localhost"
                namespace = "ns"

            self._prefix = f"/{endpoint}/{namespace}"
            self.robot_tcp_position_client: Client = Client(srv_name=self._prefix + "/robot_1/get_tcp_position")
            self.robot_joint_position_client: Client = Client(srv_name=self._prefix + "/robot_1/get_joint_position")
            self.json_file_path = None
            print("✅ InspectService初始化完成")

        except Exception as e:
            print(f"❌ InspectService初始化失败: {e}")
            import traceback
            traceback.print_exc()
            raise

    def _get_cache_path_safely(self) -> str:
        """
        安全地获取cache_path配置

        Returns:
            str: cache_path路径
        """
        default_cache_path = "D:/blade_inspect"

        # 如果Config实例不可用，使用默认路径
        if self._config is None:
            print(f"⚠️ Config实例不可用，使用默认cache_path: {default_cache_path}")
            return default_cache_path

        try:
            # 尝试从配置获取cache_path
            inspect_config: t.Dict[str, t.Any] = self._config.settings.to_dict().get("INSPECT", dict())
            cache_path: str = inspect_config.get("cache_path", default_cache_path)

            if cache_path and os.path.exists(cache_path):
                print(f"✅ 从配置获取cache_path: {cache_path}")
                return cache_path
            else:
                print(f"⚠️ 配置中的cache_path无效: {cache_path}，使用默认路径")
                return default_cache_path

        except Exception as e:
            print(f"⚠️ 获取配置失败: {e}，使用默认cache_path: {default_cache_path}")
            return default_cache_path

    @property
    def is_running(self) -> bool:
        """
        返回行为树是否正在运行。

        Returns:
            bool: 如果行为树正在运行返回 True，否则返回 False
        """
        return self._robotics.is_running

    @property
    def is_paused(self) -> bool:
        """
        返回行为树是否已经暂停。

        Returns:
            bool: 如果行为树已经暂停返回 True，否则返回 False
        """
        return self._robotics.is_paused

    @property
    def running_status(self) -> t.Dict[str, t.Dict[str, t.Any]]:
        """
        输出当前行为树中每一个节点（除了root以及selector节点）的状态。

        Returns:
            t.Dict[str, t.Dict[str, t.Any]]: 状态字典，格式如下：
            {
                "task_id": {
                    "status": "RUNNING",          # py_trees.common.Status 枚举值的字符串表示
                    "current_command_index": 0    # 当前执行到第几个command（仅对Action节点有效）
                },
                ...
            }
        """
        return self._robotics.running_status

    @property
    def process_info(self) -> t.Optional[t.List[t.Dict[str, t.Any]]]:
        """ Returns the robotics process information. """
        return self._robotics.process_info

    @property
    def batch_num(self) -> int:
        """ 当前的批次数量。 """
        batch_num = self._robotics.batch_num
        print(f"🔍 batch_num: {batch_num}")
        return batch_num

    @property
    def current_num(self) -> int:
        """ 当前正在生产的零件是第几件。 """
        current = self._robotics.current_num
        print(f"🔍 current_num: {current}, last_current_num: {self._last_current_num}")

        # 检查current_num是否发生变化
        if current != self._last_current_num:
            print(f"🔄 current_num发生变化: {self._last_current_num} -> {current}")
            self._last_current_num = current
            # 发出信号
            self.current_num_changed.emit(current, self.batch_num)
            print(f"📡 发出信号 current_num_changed: current={current}, batch={self.batch_num}")

            # 计算并发出进度信号
            if self.batch_num > 0:
                progress = int((current / self.batch_num) * 100)
                self.task_progress_updated.emit(progress)
                print(f"📡 发出信号 task_progress_updated: {progress}%")

            # 检查是否完成
            if current >= self.batch_num and self.batch_num > 0:
                self.batch_completed.emit()
                print(f"📡 发出信号 batch_completed")
        return current

    def create_task(self, task_info: t.Dict[str, t.Any], save_name: str, is_load: bool = True) -> int:
        """ 创建或修改任务，输入task_info，保存的文件名称以及是否马上加载"""
        # Todo: 添加创建任务的函数
        pass

    def run_task(self) -> int:
        """ 运行任务 """
        if self._task_info is None:
            return Codes.NO_TASK_INFO_ERROR
        # Todo: 开始执行
        return Codes.SUCCESS

    def load(self, process_file: str) -> int:
        """ Function for loading the input process file. """
        return self._robotics.load(process_file=process_file)

    def load_request(self, task_name: str) -> t.Tuple[int, t.Optional[t.Dict[str, t.Any]]]:
        """
        根据任务名称加载对应的JSON文件
        参考hdmtv_robotics项目中的load_request实现

        这个方法的主要功能：
        1. 根据任务名称构建文件路径
        2. 加载对应的JSON配置文件
        3. 调用robotics的load方法加载配置
        4. 返回加载结果和JSON数据

        Args:
            task_name: 任务名称，例如 "任务-S21.08.5440"

        Returns:
            Tuple[int, Optional[Dict]]: (状态码, JSON数据)
            - 状态码: 0表示成功，其他值表示错误
            - JSON数据: 加载的任务配置数据
        """
        try:
            print(f"🔄 load_request开始，任务名称: {task_name}")

            # 安全地从配置中获取cache_path
            cache_path = self._get_cache_path_safely()
            print(f"📁 使用cache_path: {cache_path}")

            # 构建任务文件路径
            task_folder = os.path.join(cache_path, "task", task_name)
            print(f"📁 task_folder: {task_folder}")

            # 提取产品名称（去掉"任务-"前缀）
            if task_name.startswith("任务-"):
                product_name = task_name[3:]  # 去掉"任务-"前缀
            else:
                product_name = task_name
            print(f"📄 product_name: {product_name}")

            # 构建JSON文件路径
            json_file_path = os.path.join(task_folder, f"{product_name}.json")
            print(f"🔍 尝试加载任务JSON文件: {json_file_path}")

            # 检查任务文件夹是否存在
            if not os.path.exists(task_folder):
                print(f"❌ 任务文件夹不存在: {task_folder}")

                # 列出task基础文件夹的内容
                task_base_folder = os.path.join(cache_path, "task")
                if os.path.exists(task_base_folder):
                    available_folders = os.listdir(task_base_folder)
                    print(f"📂 可用的任务文件夹: {available_folders}")
                else:
                    print(f"❌ task基础文件夹不存在: {task_base_folder}")

                return Codes.NO_TASK_INFO_ERROR, None

            # 检查JSON文件是否存在
            if not os.path.exists(json_file_path):
                print(f"❌ JSON文件不存在: {json_file_path}")

                # 列出任务文件夹的内容
                folder_contents = os.listdir(task_folder)
                print(f"📂 任务文件夹内容: {folder_contents}")

                # 尝试查找其他可能的JSON文件
                json_files = [f for f in folder_contents if f.endswith('.json')]
                if json_files:
                    print(f"📄 找到的JSON文件: {json_files}")
                    # 尝试使用第一个找到的JSON文件
                    json_file_path = os.path.join(task_folder, json_files[0])
                    print(f"🔄 尝试使用: {json_file_path}")
                else:
                    print(f"❌ 文件夹中没有JSON文件")
                    return Codes.NO_TASK_INFO_ERROR, None

            # 读取JSON文件
            import json
            with open(json_file_path, 'r', encoding='utf-8') as f:
                json_data = json.load(f)

            print(f"✅ 成功加载JSON文件: {json_file_path}")
            print(f"📄 JSON数据包含 {len(json_data.get('process', []))} 个处理步骤")

            # 保存任务信息
            self._task_info = json_data

            # 调用robotics的load方法加载文件
            load_result = self._robotics.load(process_file=json_file_path)
            self.json_file_path = json_file_path

            return load_result, json_data

        except Exception as e:
            print(f"❌ 加载任务JSON文件失败: {e}")
            import traceback
            traceback.print_exc()
            return Codes.NO_TASK_INFO_ERROR, None

    def save(self, save_file_name: t.Optional[str] = None) -> int:
        """ Function for saving the modified process file. """
        return self._robotics.save(save_file_name=save_file_name)

    def modify(self, task: t.Dict[str, t.Any]) -> int:
        """ 对当前步骤中的commands_list中参数，或x, y位置值进行修改，其他一改不能修改。"""
        return self._robotics.modify(task=task)

    def update(self, process: t.List[t.Dict[str, t.Any]]) -> int:
        """ 修改整个process流程，若整个任务的上下文拓扑关系发生改变，比如增加、删除某个task节点，
            或者task节点的pre_task，post_task关系发生改变，则调用本函数。
        """
        return self._robotics.update(process=process)

    def validate(self, is_validated: bool) -> int:
        """ Function for setting the current process has been validated or not. """
        return self._robotics.validate(is_validated=is_validated)

    def run(self, renew: bool = True) -> int:
        """ Function for start running the process. """
        return self._robotics.run(renew=renew)

    def run_batch(self, batch_num: int, current_num: int = 0, renew: bool = True) -> int:
        """ Function for start batch running the process. """
        print(f"🚀 开始批次运行 - batch_num: {batch_num}, current_num: {current_num}")
        result = self._robotics.run_batch(batch_num=batch_num, current_num=current_num, renew=renew)
        print(f"🚀 run_batch结果: {result}")

        if result == 0:  # 成功启动
            print(f"✅ 批次启动成功，开始监控")
            self.start_monitoring()  # 开始监控
            # 立即检查一次当前状态
            print(f"🔍 初始状态 - current_num: {self._robotics.current_num}, batch_num: {self._robotics.batch_num}")
        else:
            print(f"❌ 批次启动失败，错误码: {result}")
        return result

    def jog(self, tasks: t.List[t.Dict[str, t.Any]]) -> int:
        """ Function for jogging tasks. """
        return self._robotics.jog(tasks=tasks)

    def pause(self) -> int:
        """ Function for pausing the current process. """
        return self._robotics.pause()

    def resume(self) -> int:
        """ Function for resuming the current process. """
        return self._robotics.resume()

    def set_blackboard(self, variable_name: str, value: t.Any) -> int:
        """ Function for setting the value into behavior tree's blackboard. """
        return self._robotics.set_blackboard(variable_name=variable_name, value=value)

    def get_blackboard(self, variable_name: str) -> t.Any:
        """ Function for getting the value from behavior tree's blackboard. """
        return self._robotics.get_blackboard(variable_name=variable_name)

    def stop(self) -> int:
        """ Function for stopping the current process. """
        return self._robotics.stop()

    def clear(self) -> int:
        """ Function for clearing the current process information. """
        self._task_info = None
        return self._robotics.clear()

    def destroy(self) -> int:
        """ Function for destroying the current robotics class. """
        return self._robotics.destroy()

    def _check_current_num_change(self):
        """检查current_num是否发生变化的内部方法"""
        try:
            # 通过访问current_num属性来触发变化检测
            current = self.current_num
            print(f"⏰ 定时检查 - current_num: {current}, batch_num: {self.batch_num}")
            if current >= self.batch_num:
                self.stop_monitoring()
        except Exception as e:
            print(f"❌ 检查current_num变化失败: {e}")

    def start_monitoring(self):
        """开始监控current_num变化"""
        if not self._monitor_timer.isActive():
            self._monitor_timer.start()
            print("✅ 开始监控current_num变化")

    def stop_monitoring(self):
        """停止监控current_num变化"""
        if self._monitor_timer.isActive():
            self._monitor_timer.stop()
            print("✅ 停止监控current_num变化")