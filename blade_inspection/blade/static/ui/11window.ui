<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>2101</width>
    <height>1107</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <property name="styleSheet">
   <string notr="true">        background-color: rgb(0, 0, 0);</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QGridLayout" name="gridLayout_22">
    <item row="0" column="0">
     <layout class="QVBoxLayout" name="verticalLayout_31">
      <property name="spacing">
       <number>0</number>
      </property>
      <item>
       <layout class="QVBoxLayout" name="verticalLayout_30">
        <property name="spacing">
         <number>0</number>
        </property>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_2">
          <item>
           <layout class="QHBoxLayout" name="control_buttons_layout">
            <property name="spacing">
             <number>10</number>
            </property>
            <property name="leftMargin">
             <number>5</number>
            </property>
            <property name="topMargin">
             <number>5</number>
            </property>
            <property name="rightMargin">
             <number>5</number>
            </property>
            <property name="bottomMargin">
             <number>5</number>
            </property>
            <item>
             <widget class="QPushButton" name="task_back_button">
              <property name="minimumSize">
               <size>
                <width>80</width>
                <height>35</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>100</width>
                <height>35</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">
              QPushButton {
                  background-color: #3498db;
                  color: white;
                  border: none;
                  border-radius: 5px;
                  padding: 8px 16px;
                  font-size: 12px;
                  font-weight: bold;
              }
              QPushButton:hover {
                  background-color: #2980b9;
              }
             </string>
              </property>
              <property name="text">
               <string>返回</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="task_program_label">
              <property name="styleSheet">
               <string notr="true">
              QLabel {
                  font-size: 16px;
                  font-weight: bold;
                  color: #2c3e50;
                  padding: 8px;
              }
             </string>
              </property>
              <property name="text">
               <string>任务详情 - 程序: 未知程序</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="task_folder_label">
              <property name="styleSheet">
               <string notr="true">
              QLabel {
                  font-size: 12px;
                  color: #6c757d;
                  padding: 4px 8px;
              }
             </string>
              </property>
              <property name="text">
               <string>文件夹: 未知路径</string>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="control_buttons_spacer">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <widget class="QPushButton" name="task_stop_button">
              <property name="minimumSize">
               <size>
                <width>80</width>
                <height>35</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>80</width>
                <height>35</height>
               </size>
              </property>
              <property name="text">
               <string>停止任务</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="task_pause_button">
              <property name="minimumSize">
               <size>
                <width>80</width>
                <height>35</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>80</width>
                <height>35</height>
               </size>
              </property>
              <property name="text">
               <string>暂停任务</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="task_resume_button">
              <property name="enabled">
               <bool>false</bool>
              </property>
              <property name="minimumSize">
               <size>
                <width>80</width>
                <height>35</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>80</width>
                <height>35</height>
               </size>
              </property>
              <property name="text">
               <string>继续任务</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <widget class="QLabel" name="logo">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>40</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>140</width>
              <height>40</height>
             </size>
            </property>
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="titile">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>40</height>
             </size>
            </property>
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <widget class="QLabel" name="label_43">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>10</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>10</height>
           </size>
          </property>
          <property name="sizeIncrement">
           <size>
            <width>0</width>
            <height>0</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">
    QLabel {
        background-color: rgb(27, 28, 30);
        color: white;
        font-weight: bold;
    }
</string>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_23" stretch="20,1,15,1">
        <property name="spacing">
         <number>0</number>
        </property>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_9" stretch="1,3">
          <property name="spacing">
           <number>0</number>
          </property>
          <item>
           <layout class="QVBoxLayout" name="verticalLayout_8">
            <property name="spacing">
             <number>0</number>
            </property>
            <item>
             <layout class="QVBoxLayout" name="verticalLayout">
              <property name="spacing">
               <number>0</number>
              </property>
              <item>
               <widget class="QLabel" name="label">
                <property name="minimumSize">
                 <size>
                  <width>0</width>
                  <height>30</height>
                 </size>
                </property>
                <property name="styleSheet">
                 <string notr="true">
    QLabel {
        background-color: rgb(20, 47, 76);
        color: white;
        font-weight: bold;
    }
</string>
                </property>
                <property name="text">
                 <string>    基础信息</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="label_3">
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>5</height>
                 </size>
                </property>
                <property name="styleSheet">
                 <string notr="true"> QLabel {
                background-color:rgb(27, 28, 30);
            }</string>
                </property>
                <property name="text">
                 <string/>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QWidget" name="widget" native="true">
                <property name="minimumSize">
                 <size>
                  <width>0</width>
                  <height>30</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="styleSheet">
                 <string notr="true">
    QWidget {
        background-color: rgb(52, 53, 55);
        color: white;
        font-weight: bold;
    }

</string>
                </property>
                <layout class="QGridLayout" name="gridLayout_2">
                 <item row="0" column="1">
                  <widget class="QLabel" name="label_2">
                   <property name="styleSheet">
                    <string notr="true">
    QLabel {
        background-color:rgb(52, 53, 55);
        color: white;
        font-weight: bold;
    }
</string>
                   </property>
                   <property name="text">
                    <string>操作员：</string>
                   </property>
                  </widget>
                 </item>
                 <item row="0" column="2">
                  <widget class="QLineEdit" name="operator_2">
                   <property name="styleSheet">
                    <string notr="true">QLineEdit {
    border: none;  /* 隐藏边框 */
    font-size: 14px;
}</string>
                   </property>
                  </widget>
                 </item>
                 <item row="0" column="0">
                  <spacer name="horizontalSpacer_3">
                   <property name="orientation">
                    <enum>Qt::Horizontal</enum>
                   </property>
                   <property name="sizeType">
                    <enum>QSizePolicy::Preferred</enum>
                   </property>
                   <property name="sizeHint" stdset="0">
                    <size>
                     <width>40</width>
                     <height>20</height>
                    </size>
                   </property>
                  </spacer>
                 </item>
                </layout>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="label_4">
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>5</height>
                 </size>
                </property>
                <property name="styleSheet">
                 <string notr="true">QLabel {
        background-color:rgb(65, 66, 68);
    }
</string>
                </property>
                <property name="text">
                 <string/>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QWidget" name="widget_2" native="true">
                <property name="minimumSize">
                 <size>
                  <width>0</width>
                  <height>30</height>
                 </size>
                </property>
                <property name="styleSheet">
                 <string notr="true">
    QWidget {
        background-color: rgb(52, 53, 55);
        color: white;
        font-weight: bold;
    }

</string>
                </property>
                <layout class="QGridLayout" name="gridLayout">
                 <item row="0" column="2">
                  <widget class="QLineEdit" name="number">
                   <property name="styleSheet">
                    <string notr="true">QLineEdit {
    border: none;  /* 隐藏边框 */
    font-size: 14px;
}</string>
                   </property>
                  </widget>
                 </item>
                 <item row="0" column="1">
                  <widget class="QLabel" name="label_5">
                   <property name="styleSheet">
                    <string notr="true">
    QLabel {
        background-color:rgb(52, 53, 55);
        color: white;
        font-weight: bold;
    }
</string>
                   </property>
                   <property name="text">
                    <string>工件数：</string>
                   </property>
                  </widget>
                 </item>
                 <item row="0" column="0">
                  <spacer name="horizontalSpacer_4">
                   <property name="orientation">
                    <enum>Qt::Horizontal</enum>
                   </property>
                   <property name="sizeType">
                    <enum>QSizePolicy::Preferred</enum>
                   </property>
                   <property name="sizeHint" stdset="0">
                    <size>
                     <width>40</width>
                     <height>20</height>
                    </size>
                   </property>
                  </spacer>
                 </item>
                </layout>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="label_6">
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>5</height>
                 </size>
                </property>
                <property name="styleSheet">
                 <string notr="true">QLabel {
        background-color:rgb(65, 66, 68);
    }
</string>
                </property>
                <property name="text">
                 <string/>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QWidget" name="widget_3" native="true">
                <property name="minimumSize">
                 <size>
                  <width>0</width>
                  <height>30</height>
                 </size>
                </property>
                <property name="styleSheet">
                 <string notr="true">
    QWidget {
        background-color: rgb(52, 53, 55);
        color: white;
        font-weight: bold;
    }

</string>
                </property>
                <layout class="QGridLayout" name="gridLayout_3">
                 <item row="0" column="2">
                  <widget class="QLineEdit" name="id">
                   <property name="styleSheet">
                    <string notr="true">QLineEdit {
    border: none;  /* 隐藏边框 */
    font-size: 14px;
}</string>
                   </property>
                  </widget>
                 </item>
                 <item row="0" column="1">
                  <widget class="QLabel" name="label_7">
                   <property name="styleSheet">
                    <string notr="true">
    QLabel {
        background-color:rgb(52, 53, 55);
        color: white;
        font-weight: bold;
    }
</string>
                   </property>
                   <property name="text">
                    <string>工单号：</string>
                   </property>
                  </widget>
                 </item>
                 <item row="0" column="0">
                  <spacer name="horizontalSpacer_5">
                   <property name="orientation">
                    <enum>Qt::Horizontal</enum>
                   </property>
                   <property name="sizeType">
                    <enum>QSizePolicy::Preferred</enum>
                   </property>
                   <property name="sizeHint" stdset="0">
                    <size>
                     <width>40</width>
                     <height>20</height>
                    </size>
                   </property>
                  </spacer>
                 </item>
                </layout>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="label_8">
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>5</height>
                 </size>
                </property>
                <property name="styleSheet">
                 <string notr="true">QLabel {
        background-color:rgb(65, 66, 68);
    }
</string>
                </property>
                <property name="text">
                 <string/>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QWidget" name="widget_4" native="true">
                <property name="minimumSize">
                 <size>
                  <width>0</width>
                  <height>30</height>
                 </size>
                </property>
                <property name="styleSheet">
                 <string notr="true">
    QWidget {
        background-color: rgb(52, 53, 55);
        color: white;
        font-weight: bold;
    }

</string>
                </property>
                <layout class="QGridLayout" name="gridLayout_5">
                 <item row="0" column="1">
                  <widget class="QLabel" name="label_10">
                   <property name="styleSheet">
                    <string notr="true">
    QLabel {
        background-color:rgb(52, 53, 55);
        color: white;
        font-weight: bold;
    }
</string>
                   </property>
                   <property name="text">
                    <string>构型：</string>
                   </property>
                  </widget>
                 </item>
                 <item row="0" column="2">
                  <widget class="QComboBox" name="configuration">
                   <property name="minimumSize">
                    <size>
                     <width>0</width>
                     <height>20</height>
                    </size>
                   </property>
                   <property name="styleSheet">
                    <string notr="true"/>
                   </property>
                   <item>
                    <property name="text">
                     <string>构型1</string>
                    </property>
                   </item>
                   <item>
                    <property name="text">
                     <string>新建项目</string>
                    </property>
                   </item>
                  </widget>
                 </item>
                 <item row="0" column="0">
                  <spacer name="horizontalSpacer_6">
                   <property name="orientation">
                    <enum>Qt::Horizontal</enum>
                   </property>
                   <property name="sizeType">
                    <enum>QSizePolicy::Maximum</enum>
                   </property>
                   <property name="sizeHint" stdset="0">
                    <size>
                     <width>40</width>
                     <height>20</height>
                    </size>
                   </property>
                  </spacer>
                 </item>
                </layout>
               </widget>
              </item>
             </layout>
            </item>
            <item>
             <layout class="QVBoxLayout" name="verticalLayout_3">
              <property name="spacing">
               <number>0</number>
              </property>
              <item>
               <widget class="QLabel" name="label_11">
                <property name="minimumSize">
                 <size>
                  <width>0</width>
                  <height>30</height>
                 </size>
                </property>
                <property name="styleSheet">
                 <string notr="true">
    QLabel {
        background-color: rgb(20, 47, 76);
        color: white;
        font-weight: bold;
    }
</string>
                </property>
                <property name="text">
                 <string>    操作信息</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="label_13">
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>5</height>
                 </size>
                </property>
                <property name="styleSheet">
                 <string notr="true">QLabel {
        background-color:rgb(65, 66, 68);
    }
</string>
                </property>
                <property name="text">
                 <string/>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QWidget" name="widget_5" native="true">
                <property name="minimumSize">
                 <size>
                  <width>0</width>
                  <height>110</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>120</height>
                 </size>
                </property>
                <property name="styleSheet">
                 <string notr="true">
    QWidget {
        background-color: rgb(52, 53, 55);
        color: white;
        font-weight: bold;
    }

</string>
                </property>
                <layout class="QGridLayout" name="gridLayout_6">
                 <item row="0" column="0">
                  <layout class="QVBoxLayout" name="verticalLayout_2" stretch="1,4">
                   <property name="spacing">
                    <number>6</number>
                   </property>
                   <property name="bottomMargin">
                    <number>6</number>
                   </property>
                   <item>
                    <widget class="QLabel" name="label_14">
                     <property name="maximumSize">
                      <size>
                       <width>16777215</width>
                       <height>30</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>   检测模式：</string>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <layout class="QHBoxLayout" name="horizontalLayout">
                     <property name="spacing">
                      <number>6</number>
                     </property>
                     <item>
                      <widget class="QPushButton" name="abnormal">
                       <property name="styleSheet">
                        <string notr="true">
QPushButton {
    background-color:rgb(13, 114, 200);  /* 默认颜色 */
}

QPushButton:pressed {
   background-color:rgb(172, 70, 9);  /* 按下时颜色 */
}
</string>
                       </property>
                       <property name="text">
                        <string>异常检测</string>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QPushButton" name="classification">
                       <property name="styleSheet">
                        <string notr="true">
QPushButton {
    background-color:rgb(13, 114, 200);  /* 默认颜色 */
}

QPushButton:pressed {
   background-color:rgb(172, 70, 9);  /* 按下时颜色 */
}
</string>
                       </property>
                       <property name="text">
                        <string>异常检测+分类</string>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </item>
                  </layout>
                 </item>
                </layout>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="label_15">
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>5</height>
                 </size>
                </property>
                <property name="styleSheet">
                 <string notr="true">QLabel {
        background-color:rgb(65, 66, 68);
    }
</string>
                </property>
                <property name="text">
                 <string/>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QWidget" name="widget_6" native="true">
                <property name="styleSheet">
                 <string notr="true">
    QWidget {
        background-color: rgb(52, 53, 55);
        color: white;
        font-weight: bold;
    }

</string>
                </property>
                <layout class="QGridLayout" name="gridLayout_7">
                 <item row="0" column="0">
                  <widget class="QPushButton" name="local">
                   <property name="styleSheet">
                    <string notr="true">
QPushButton {
    background-color:rgb(13, 114, 200);  /* 默认颜色 */
}

QPushButton:pressed {
   background-color:rgb(172, 70, 9);  /* 按下时颜色 */
}
</string>
                   </property>
                   <property name="text">
                    <string>本地上传</string>
                   </property>
                  </widget>
                 </item>
                 <item row="0" column="1">
                  <widget class="QLabel" name="label_16">
                   <property name="text">
                    <string/>
                   </property>
                  </widget>
                 </item>
                </layout>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="label_17">
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>5</height>
                 </size>
                </property>
                <property name="styleSheet">
                 <string notr="true">QLabel {
        background-color:rgb(65, 66, 68);
    }
</string>
                </property>
                <property name="text">
                 <string/>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QWidget" name="widget_7" native="true">
                <property name="styleSheet">
                 <string notr="true">
    QWidget {
        background-color: rgb(52, 53, 55);
        color: white;
        font-weight: bold;
    }

</string>
                </property>
                <layout class="QGridLayout" name="gridLayout_8">
                 <item row="0" column="0">
                  <widget class="QPushButton" name="start">
                   <property name="styleSheet">
                    <string notr="true">
QPushButton {
    background-color:rgb(13, 114, 200);  /* 默认颜色 */
}

QPushButton:pressed {
   background-color:rgb(172, 70, 9);  /* 按下时颜色 */
}
</string>
                   </property>
                   <property name="text">
                    <string>开始检测</string>
                   </property>
                  </widget>
                 </item>
                 <item row="0" column="1">
                  <widget class="QPushButton" name="stop">
                   <property name="styleSheet">
                    <string notr="true">
QPushButton {
    background-color:rgb(13, 114, 200);  /* 默认颜色 */
}

QPushButton:pressed {
   background-color:rgb(172, 70, 9);  /* 按下时颜色 */
}
</string>
                   </property>
                   <property name="text">
                    <string>停止检测</string>
                   </property>
                  </widget>
                 </item>
                </layout>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="label_18">
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>25</height>
                 </size>
                </property>
                <property name="styleSheet">
                 <string notr="true">QLabel {
        background-color:rgb(65, 66, 68);
    }
</string>
                </property>
                <property name="text">
                 <string/>
                </property>
               </widget>
              </item>
             </layout>
            </item>
            <item>
             <layout class="QVBoxLayout" name="verticalLayout_6">
              <property name="spacing">
               <number>0</number>
              </property>
              <item>
               <widget class="QLabel" name="label_21">
                <property name="minimumSize">
                 <size>
                  <width>0</width>
                  <height>30</height>
                 </size>
                </property>
                <property name="styleSheet">
                 <string notr="true">
    QLabel {
        background-color: rgb(20, 47, 76);
        color: white;
        font-weight: bold;
    }
</string>
                </property>
                <property name="text">
                 <string>    运行信息</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="label_22">
                <property name="minimumSize">
                 <size>
                  <width>0</width>
                  <height>5</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>5</height>
                 </size>
                </property>
                <property name="styleSheet">
                 <string notr="true">QLabel {
        background-color:rgb(65, 66, 68);
    }
</string>
                </property>
                <property name="text">
                 <string/>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QWidget" name="widget_8" native="true">
                <property name="minimumSize">
                 <size>
                  <width>0</width>
                  <height>80</height>
                 </size>
                </property>
                <property name="styleSheet">
                 <string notr="true">
    QWidget {
        background-color: rgb(52, 53, 55);
        color: white;
        font-weight: bold;
    }

</string>
                </property>
                <layout class="QGridLayout" name="gridLayout_9">
                 <!-- 进度显示已移除 -->
                </layout>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="label_25">
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>5</height>
                 </size>
                </property>
                <property name="styleSheet">
                 <string notr="true">QLabel {
        background-color:rgb(65, 66, 68);
    }
</string>
                </property>
                <property name="text">
                 <string/>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QWidget" name="widget_9" native="true">
                <property name="styleSheet">
                 <string notr="true">
    QWidget {
        background-color: rgb(52, 53, 55);
        color: white;
        font-weight: bold;
    }

</string>
                </property>
                <layout class="QGridLayout" name="gridLayout_10">
                 <item row="0" column="0">
                  <widget class="QLabel" name="label_26">
                   <property name="styleSheet">
                    <string notr="true">
    QLabel {
        background-color:rgb(52, 53, 55);
        color: white;
        font-weight: bold;
    }
</string>
                   </property>
                   <property name="text">
                    <string>   已检测数量：</string>
                   </property>
                  </widget>
                 </item>
                 <item row="0" column="1">
                  <widget class="QLabel" name="number_detected">
                   <property name="text">
                    <string>0/0</string>
                   </property>
                  </widget>
                 </item>
                </layout>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="label_34">
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>5</height>
                 </size>
                </property>
                <property name="styleSheet">
                 <string notr="true">QLabel {
        background-color:rgb(65, 66, 68);
    }
</string>
                </property>
                <property name="text">
                 <string/>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="label_35">
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>5</height>
                 </size>
                </property>
                <property name="styleSheet">
                 <string notr="true">QLabel {
        background-color:rgb(65, 66, 68);
    }
</string>
                </property>
                <property name="text">
                 <string/>
                </property>
               </widget>
              </item>
             </layout>
            </item>
            <item>
             <layout class="QVBoxLayout" name="verticalLayout_7">
              <property name="spacing">
               <number>0</number>
              </property>
              <item>
               <widget class="QLabel" name="label_39">
                <property name="minimumSize">
                 <size>
                  <width>0</width>
                  <height>30</height>
                 </size>
                </property>
                <property name="styleSheet">
                 <string notr="true">
    QLabel {
        background-color: rgb(20, 47, 76);
        color: white;
        font-weight: bold;
    }
</string>
                </property>
                <property name="text">
                 <string>    结果导出</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="label_40">
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>5</height>
                 </size>
                </property>
                <property name="styleSheet">
                 <string notr="true">QLabel {
        background-color:rgb(65, 66, 68);
    }
</string>
                </property>
                <property name="text">
                 <string/>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QWidget" name="widget_12" native="true">
                <property name="minimumSize">
                 <size>
                  <width>0</width>
                  <height>40</height>
                 </size>
                </property>
                <property name="styleSheet">
                 <string notr="true">
    QWidget {
        background-color: rgb(52, 53, 55);
        color: white;
        font-weight: bold;
    }

</string>
                </property>
                <layout class="QGridLayout" name="gridLayout_14">
                 <item row="0" column="1">
                  <widget class="QComboBox" name="comboBox_2">
                   <property name="styleSheet">
                    <string notr="true"/>
                   </property>
                   <item>
                    <property name="text">
                     <string>20250613</string>
                    </property>
                   </item>
                  </widget>
                 </item>
                 <item row="0" column="0">
                  <widget class="QLabel" name="label_41">
                   <property name="styleSheet">
                    <string notr="true">
    QLabel {
        background-color:rgb(52, 53, 55);
        color: white;
        font-weight: bold;
    }
</string>
                   </property>
                   <property name="text">
                    <string>   工单号：</string>
                   </property>
                  </widget>
                 </item>
                </layout>
               </widget>
              </item>
              <item>
               <widget class="QWidget" name="widget_123" native="true">
                <property name="minimumSize">
                 <size>
                  <width>0</width>
                  <height>50</height>
                 </size>
                </property>
                <property name="styleSheet">
                 <string notr="true">
    QWidget {
        background-color: rgb(65, 66, 68);
        color: white;
        font-weight: bold;
    }

</string>
                </property>
                <layout class="QGridLayout" name="gridLayout_15">
                 <item row="0" column="0">
                  <layout class="QHBoxLayout" name="horizontalLayout_6">
                   <item>
                    <spacer name="horizontalSpacer">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                   <item>
                    <widget class="QPushButton" name="output">
                     <property name="text">
                      <string>导出结果</string>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <spacer name="horizontalSpacer_2">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                  </layout>
                 </item>
                </layout>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="label_42">
                <property name="minimumSize">
                 <size>
                  <width>0</width>
                  <height>80</height>
                 </size>
                </property>
                <property name="styleSheet">
                 <string notr="true">QLabel {
        background-color:rgb(65, 66, 68);
    }
</string>
                </property>
                <property name="text">
                 <string/>
                </property>
               </widget>
              </item>
             </layout>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_8">
            <property name="spacing">
             <number>0</number>
            </property>
            <item>
             <widget class="QLabel" name="label_45">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Ignored">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>10</width>
                <height>0</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>10</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">
    QLabel {
        background-color: rgb(27, 28, 30);
        color: white;
        font-weight: bold;
    }
</string>
              </property>
              <property name="text">
               <string/>
              </property>
             </widget>
            </item>
            <item>
             <layout class="QVBoxLayout" name="verticalLayout_18">
              <property name="spacing">
               <number>0</number>
              </property>
              <item>
               <widget class="QLabel" name="label_44">
                <property name="minimumSize">
                 <size>
                  <width>0</width>
                  <height>30</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>30</height>
                 </size>
                </property>
                <property name="styleSheet">
                 <string notr="true">
    QLabel {
        background-color: rgb(13, 114, 200);
        color: white;
        font-weight: bold;
    }
</string>
                </property>
                <property name="text">
                 <string>检测结果</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignCenter</set>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="label_46">
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>5</height>
                 </size>
                </property>
                <property name="styleSheet">
                 <string notr="true">
    QLabel {
        background-color: rgb(27, 28, 30);
        color: white;
        font-weight: bold;
    }
</string>
                </property>
                <property name="text">
                 <string/>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QWidget" name="widget_14" native="true">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="styleSheet">
                 <string notr="true">
    QWidget {
        background-color: rgb(31, 65, 96);
        color: white;
        font-weight: bold;
    }

</string>
                </property>
                <layout class="QGridLayout" name="gridLayout_16">
                 <property name="topMargin">
                  <number>9</number>
                 </property>
                 <property name="spacing">
                  <number>12</number>
                 </property>
                 <item row="0" column="0">
                  <layout class="QHBoxLayout" name="horizontalLayout_7">
                   <property name="leftMargin">
                    <number>0</number>
                   </property>
                   <property name="bottomMargin">
                    <number>0</number>
                   </property>
                   <item>
                    <layout class="QVBoxLayout" name="verticalLayout_9">
                     <property name="spacing">
                      <number>10</number>
                     </property>
                     <property name="bottomMargin">
                      <number>0</number>
                     </property>
                     <item>
                      <widget class="QWidget" name="widget1" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>86</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_4">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn1">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>0</width>
                            <height>0</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="styleSheet">
                           <string notr="true"/>
                          </property>
                          <property name="text">
                           <string>1</string>
                          </property>
                          <property name="iconSize">
                           <size>
                            <width>48</width>
                            <height>102</height>
                           </size>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget10" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>86</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_11">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn10">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>10</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget19" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_23">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn19">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>19</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget28" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_24">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn28">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>28</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget37" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_25">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn37">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>37</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget46" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_26">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn46">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>46</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget55" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <property name="maximumSize">
                        <size>
                         <width>16777215</width>
                         <height>16777215</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_27">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn55">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>55</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget64" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_28">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn64">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>64</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item>
                    <layout class="QVBoxLayout" name="verticalLayout_10">
                     <property name="spacing">
                      <number>10</number>
                     </property>
                     <property name="bottomMargin">
                      <number>0</number>
                     </property>
                     <item>
                      <widget class="QWidget" name="widget2" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_29">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn2">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>2</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget11" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_30">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn11">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>11</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget20" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_31">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn20">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>20</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget29" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_32">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn29">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>29</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget38" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_33">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn38">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>38</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget47" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_34">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn47">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>47</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget56" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_35">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn56">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>56</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget65" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_36">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn65">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>65</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item>
                    <layout class="QVBoxLayout" name="verticalLayout_11">
                     <property name="spacing">
                      <number>10</number>
                     </property>
                     <property name="bottomMargin">
                      <number>0</number>
                     </property>
                     <item>
                      <widget class="QWidget" name="widget3" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_37">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn3">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>3</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget12" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_38">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn12">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>12</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget21" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_39">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn21">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>21</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget30" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_40">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn30">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>30</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget39" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_41">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn39">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>39</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget48" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_42">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn48">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>48</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget57" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_43">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn57">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>57</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget66" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_44">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn66">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>66</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item>
                    <layout class="QVBoxLayout" name="verticalLayout_12">
                     <property name="spacing">
                      <number>10</number>
                     </property>
                     <property name="bottomMargin">
                      <number>0</number>
                     </property>
                     <item>
                      <widget class="QWidget" name="widget4" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_51">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn4">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>4</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget13" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_52">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn13">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>13</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget22" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_53">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn22">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>22</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget31" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_54">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn31">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>31</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget40" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_55">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn40">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>40</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget49" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_56">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn49">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>49</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget58" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_57">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn58">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>58</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget67" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_45">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn67">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>67</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item>
                    <layout class="QVBoxLayout" name="verticalLayout_13">
                     <property name="spacing">
                      <number>10</number>
                     </property>
                     <property name="bottomMargin">
                      <number>0</number>
                     </property>
                     <item>
                      <widget class="QWidget" name="widget5" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_58">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn5">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>5</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget14" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_59">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn14">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>14</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget23" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_60">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn23">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>23</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget32" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_61">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn32">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>32</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget41" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_62">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn41">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>41</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget50" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_63">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn50">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>50</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget59" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_64">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn59">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>59</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget68" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_46">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn68">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>68</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item>
                    <layout class="QVBoxLayout" name="verticalLayout_14">
                     <property name="spacing">
                      <number>10</number>
                     </property>
                     <property name="bottomMargin">
                      <number>0</number>
                     </property>
                     <item>
                      <widget class="QWidget" name="widget6" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_74">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn6">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>6</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget15" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_73">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn15">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>15</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget24" native="true">
                       <property name="sizePolicy">
                        <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                         <horstretch>0</horstretch>
                         <verstretch>0</verstretch>
                        </sizepolicy>
                       </property>
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_72">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn24">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>24</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget33" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_71">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn33">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>33</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget42" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_70">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn42">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>42</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget51" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_69">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn51">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>51</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget60" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_66">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn60">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>60</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget69" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_47">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn69">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>69</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item>
                    <layout class="QGridLayout" name="gridLayout_85">
                     <property name="bottomMargin">
                      <number>0</number>
                     </property>
                     <item row="0" column="0">
                      <widget class="QWidget" name="widget7" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_75">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn7">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>7</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item row="6" column="0">
                      <widget class="QWidget" name="widget61" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_67">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn61">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>61</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item row="7" column="0">
                      <widget class="QWidget" name="widget70" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_48">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn70">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>70</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item row="2" column="0">
                      <widget class="QWidget" name="widget25" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_83">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn25">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>25</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item row="3" column="0">
                      <widget class="QWidget" name="widget34" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_84">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn34">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>34</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item row="1" column="0">
                      <widget class="QWidget" name="widget16" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_76">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn16">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>16</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item row="5" column="0">
                      <widget class="QWidget" name="widget52" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_87">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn52">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>52</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item row="4" column="0">
                      <widget class="QWidget" name="widget43" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_86">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn43">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>43</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item>
                    <layout class="QVBoxLayout" name="verticalLayout_16">
                     <property name="spacing">
                      <number>10</number>
                     </property>
                     <property name="bottomMargin">
                      <number>0</number>
                     </property>
                     <item>
                      <widget class="QWidget" name="widget8" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_77">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn8">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>8</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget17" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_79">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn17">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>17</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget26" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_80">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn26">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>26</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget35" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_88">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn35">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>35</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget44" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_90">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn44">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>44</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget53" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_92">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn53">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>53</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget62" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_68">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn62">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>62</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget71" native="true">
                       <property name="sizePolicy">
                        <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                         <horstretch>0</horstretch>
                         <verstretch>0</verstretch>
                        </sizepolicy>
                       </property>
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_49">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn71">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>71</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                    </layout>
                   </item>
                   <item>
                    <layout class="QVBoxLayout" name="verticalLayout_17">
                     <property name="spacing">
                      <number>10</number>
                     </property>
                     <property name="bottomMargin">
                      <number>0</number>
                     </property>
                     <item>
                      <widget class="QWidget" name="widget9" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_78">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn9">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>9</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget18" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_81">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn18">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>18</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget27" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_82">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn27">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>27</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget36" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_89">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn36">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>36</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget45" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_91">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn45">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>45</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget54" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_93">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn54">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>54</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget63" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_65">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn63">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>63</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                     <item>
                      <widget class="QWidget" name="widget72" native="true">
                       <property name="minimumSize">
                        <size>
                         <width>0</width>
                         <height>100</height>
                        </size>
                       </property>
                       <layout class="QGridLayout" name="gridLayout_50">
                        <item row="0" column="0">
                         <widget class="QPushButton" name="btn72">
                          <property name="sizePolicy">
                           <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                            <horstretch>0</horstretch>
                            <verstretch>0</verstretch>
                           </sizepolicy>
                          </property>
                          <property name="minimumSize">
                           <size>
                            <width>60</width>
                            <height>80</height>
                           </size>
                          </property>
                          <property name="maximumSize">
                           <size>
                            <width>16777215</width>
                            <height>16777215</height>
                           </size>
                          </property>
                          <property name="text">
                           <string>72</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </widget>
                     </item>
                    </layout>
                   </item>
                  </layout>
                 </item>
                </layout>
               </widget>
              </item>
             </layout>
            </item>
           </layout>
          </item>
         </layout>
        </item>
        <item>
         <widget class="QLabel" name="label_48">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Ignored">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>10</width>
            <height>0</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>10</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">
    QLabel {
        background-color: rgb(27, 28, 30);
        color: white;
        font-weight: bold;
    }
</string>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <layout class="QVBoxLayout" name="verticalLayout_29">
          <property name="spacing">
           <number>0</number>
          </property>
          <item>
           <widget class="QLabel" name="label_47">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>30</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>16777215</width>
              <height>30</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">
    QLabel {
        background-color: rgb(13, 114, 200);
        color: white;
        font-weight: bold;
    }
</string>
            </property>
            <property name="text">
             <string>实时图像</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_60">
            <property name="maximumSize">
             <size>
              <width>16777215</width>
              <height>5</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">
    QLabel {
        background-color: rgb(27, 28, 30);
        color: white;
        font-weight: bold;
    }
</string>
            </property>
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QWidget" name="widget_17" native="true">
            <property name="styleSheet">
             <string notr="true">
    QWidget {
        background-color:rgb(0, 0, 0);
        color: white;
        font-weight: bold;
    }

</string>
            </property>
            <layout class="QGridLayout" name="gridLayout_19">
             <item row="0" column="0">
              <layout class="QHBoxLayout" name="horizontalLayout_16" stretch="1,6,0">
               <item>
                <layout class="QVBoxLayout" name="verticalLayout_21">
                 <item>
                  <layout class="QHBoxLayout" name="horizontalLayout_15">
                   <item>
                    <widget class="QLabel" name="top_left">
                     <property name="minimumSize">
                      <size>
                       <width>40</width>
                       <height>40</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>16777215</width>
                       <height>40</height>
                      </size>
                     </property>
                     <property name="text">
                      <string/>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <spacer name="horizontalSpacer_9">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                  </layout>
                 </item>
                 <item>
                  <widget class="QWidget" name="widget_15" native="true">
                   <layout class="QGridLayout" name="gridLayout_17">
                    <item row="0" column="0">
                     <layout class="QVBoxLayout" name="verticalLayout_20" stretch="1,1,1,4">
                      <item>
                       <layout class="QHBoxLayout" name="horizontalLayout_12">
                        <item>
                         <widget class="QLabel" name="label_50">
                          <property name="text">
                           <string>2D图像</string>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <spacer name="horizontalSpacer_8">
                          <property name="orientation">
                           <enum>Qt::Horizontal</enum>
                          </property>
                          <property name="sizeHint" stdset="0">
                           <size>
                            <width>40</width>
                            <height>20</height>
                           </size>
                          </property>
                         </spacer>
                        </item>
                       </layout>
                      </item>
                      <item>
                       <layout class="QVBoxLayout" name="verticalLayout_19">
                        <item>
                         <layout class="QHBoxLayout" name="horizontalLayout_10">
                          <item>
                           <widget class="QLabel" name="label_51">
                            <property name="text">
                             <string>工件号：</string>
                            </property>
                           </widget>
                          </item>
                          <item>
                           <spacer name="horizontalSpacer_7">
                            <property name="orientation">
                             <enum>Qt::Horizontal</enum>
                            </property>
                            <property name="sizeHint" stdset="0">
                             <size>
                              <width>40</width>
                              <height>20</height>
                             </size>
                            </property>
                           </spacer>
                          </item>
                         </layout>
                        </item>
                        <item>
                         <widget class="QLabel" name="batch_number">
                          <property name="text">
                           <string>XT2604042/A</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </item>
                      <item>
                       <layout class="QHBoxLayout" name="horizontalLayout_11">
                        <item>
                         <widget class="QLabel" name="label_53">
                          <property name="text">
                           <string>叶片号：</string>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QLabel" name="blade_id">
                          <property name="text">
                           <string>0</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </item>
                      <item>
                       <widget class="QLabel" name="defect">
                        <property name="text">
                         <string>缺陷1：
 ×××××××××××
缺陷2：
××××××××××××</string>
                        </property>
                       </widget>
                      </item>
                     </layout>
                    </item>
                   </layout>
                  </widget>
                 </item>
                 <item>
                  <layout class="QHBoxLayout" name="horizontalLayout_14">
                   <item>
                    <widget class="QLabel" name="bottom_left">
                     <property name="minimumSize">
                      <size>
                       <width>40</width>
                       <height>40</height>
                      </size>
                     </property>
                     <property name="text">
                      <string/>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <spacer name="horizontalSpacer_10">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                  </layout>
                 </item>
                </layout>
               </item>
               <item>
                <layout class="QVBoxLayout" name="verticalLayout_23">
                 <item>
                  <widget class="QGraphicsView" name="graphicsView"/>
                 </item>
                 <item>
                  <widget class="QWidget" name="widget_16" native="true">
                   <layout class="QGridLayout" name="gridLayout_18">
                    <item row="0" column="0">
                     <layout class="QHBoxLayout" name="horizontalLayout_13">
                      <item>
                       <widget class="QPushButton" name="previous">
                        <property name="styleSheet">
                         <string notr="true">QPushButton {
    background-color:rgb(33, 33, 33);  /* 默认颜色 */
}</string>
                        </property>
                        <property name="text">
                         <string>&lt; 上一张</string>
                        </property>
                       </widget>
                      </item>
                      <item>
                       <widget class="QPushButton" name="retest">
                        <property name="styleSheet">
                         <string notr="true">QPushButton {
    background-color:rgb(33, 33, 33);  /* 默认颜色 */
}</string>
                        </property>
                        <property name="text">
                         <string>复检</string>
                        </property>
                       </widget>
                      </item>
                      <item>
                       <widget class="QPushButton" name="decision_making">
                        <property name="styleSheet">
                         <string notr="true">QPushButton {
    background-color:rgb(33, 33, 33);  /* 默认颜色 */
}</string>
                        </property>
                        <property name="text">
                         <string>决策</string>
                        </property>
                       </widget>
                      </item>
                      <item>
                       <widget class="QPushButton" name="scrap">
                        <property name="styleSheet">
                         <string notr="true">QPushButton {
    background-color:rgb(33, 33, 33);  /* 默认颜色 */
}</string>
                        </property>
                        <property name="text">
                         <string>报废</string>
                        </property>
                       </widget>
                      </item>
                      <item>
                       <widget class="QPushButton" name="next">
                        <property name="styleSheet">
                         <string notr="true">QPushButton {
    background-color:rgb(33, 33, 33);  /* 默认颜色 */
}</string>
                        </property>
                        <property name="text">
                         <string>下一张&gt;</string>
                        </property>
                       </widget>
                      </item>
                     </layout>
                    </item>
                   </layout>
                  </widget>
                 </item>
                </layout>
               </item>
               <item>
                <layout class="QVBoxLayout" name="verticalLayout_22">
                 <property name="spacing">
                  <number>0</number>
                 </property>
                 <item>
                  <widget class="QLabel" name="top_right">
                   <property name="minimumSize">
                    <size>
                     <width>40</width>
                     <height>40</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>16777215</width>
                     <height>40</height>
                    </size>
                   </property>
                   <property name="text">
                    <string/>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QLabel" name="label_59">
                   <property name="text">
                    <string/>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QLabel" name="bottom_right">
                   <property name="minimumSize">
                    <size>
                     <width>40</width>
                     <height>40</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>16777215</width>
                     <height>40</height>
                    </size>
                   </property>
                   <property name="text">
                    <string/>
                   </property>
                  </widget>
                 </item>
                </layout>
               </item>
              </layout>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_61">
            <property name="styleSheet">
             <string notr="true">
    QLabel {
        background-color: rgb(31, 65, 96);
        color: white;
        font-weight: bold;
    }
</string>
            </property>
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QWidget" name="widget_19" native="true">
            <property name="styleSheet">
             <string notr="true">
    QWidget {
        background-color:rgb(0, 0, 0);
        color: white;
        font-weight: bold;
    }

</string>
            </property>
            <layout class="QGridLayout" name="gridLayout_21">
             <item row="0" column="0">
              <layout class="QHBoxLayout" name="horizontalLayout_22" stretch="1,6,0">
               <item>
                <layout class="QVBoxLayout" name="verticalLayout_24">
                 <item>
                  <layout class="QHBoxLayout" name="horizontalLayout_17">
                   <item>
                    <widget class="QLabel" name="top_left_1">
                     <property name="minimumSize">
                      <size>
                       <width>40</width>
                       <height>40</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>16777215</width>
                       <height>40</height>
                      </size>
                     </property>
                     <property name="text">
                      <string/>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <spacer name="horizontalSpacer_11">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                  </layout>
                 </item>
                 <item>
                  <widget class="QWidget" name="widget_18" native="true">
                   <layout class="QGridLayout" name="gridLayout_20">
                    <item row="0" column="0">
                     <layout class="QVBoxLayout" name="verticalLayout_25" stretch="1,1,1,4">
                      <item>
                       <layout class="QHBoxLayout" name="horizontalLayout_18">
                        <item>
                         <widget class="QLabel" name="label_67">
                          <property name="text">
                           <string>2D图像</string>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <spacer name="horizontalSpacer_12">
                          <property name="orientation">
                           <enum>Qt::Horizontal</enum>
                          </property>
                          <property name="sizeHint" stdset="0">
                           <size>
                            <width>40</width>
                            <height>20</height>
                           </size>
                          </property>
                         </spacer>
                        </item>
                       </layout>
                      </item>
                      <item>
                       <layout class="QVBoxLayout" name="verticalLayout_26">
                        <item>
                         <layout class="QHBoxLayout" name="horizontalLayout_19">
                          <item>
                           <widget class="QLabel" name="label_68">
                            <property name="text">
                             <string>工件号：</string>
                            </property>
                           </widget>
                          </item>
                          <item>
                           <spacer name="horizontalSpacer_13">
                            <property name="orientation">
                             <enum>Qt::Horizontal</enum>
                            </property>
                            <property name="sizeHint" stdset="0">
                             <size>
                              <width>40</width>
                              <height>20</height>
                             </size>
                            </property>
                           </spacer>
                          </item>
                         </layout>
                        </item>
                        <item>
                         <widget class="QLabel" name="batch_number_1">
                          <property name="text">
                           <string>XT2604042/A</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </item>
                      <item>
                       <layout class="QHBoxLayout" name="horizontalLayout_20">
                        <item>
                         <widget class="QLabel" name="label_70">
                          <property name="text">
                           <string>叶片号：</string>
                          </property>
                         </widget>
                        </item>
                        <item>
                         <widget class="QLabel" name="blade_id_1">
                          <property name="text">
                           <string>0</string>
                          </property>
                         </widget>
                        </item>
                       </layout>
                      </item>
                      <item>
                       <widget class="QLabel" name="defect_1">
                        <property name="text">
                         <string>缺陷1：
 ×××××××××××
缺陷2：
××××××××××××</string>
                        </property>
                       </widget>
                      </item>
                     </layout>
                    </item>
                   </layout>
                  </widget>
                 </item>
                 <item>
                  <layout class="QHBoxLayout" name="horizontalLayout_21">
                   <item>
                    <widget class="QLabel" name="bottom_left_1">
                     <property name="minimumSize">
                      <size>
                       <width>40</width>
                       <height>40</height>
                      </size>
                     </property>
                     <property name="text">
                      <string/>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <spacer name="horizontalSpacer_14">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                  </layout>
                 </item>
                </layout>
               </item>
               <item>
                <layout class="QVBoxLayout" name="verticalLayout_28">
                 <item>
                  <widget class="QGraphicsView" name="graphicsView_2"/>
                 </item>
                 <item>
                  <spacer name="horizontalSpacer_15">
                   <property name="orientation">
                    <enum>Qt::Horizontal</enum>
                   </property>
                   <property name="sizeHint" stdset="0">
                    <size>
                     <width>40</width>
                     <height>20</height>
                    </size>
                   </property>
                  </spacer>
                 </item>
                </layout>
               </item>
               <item>
                <layout class="QVBoxLayout" name="verticalLayout_27">
                 <item>
                  <widget class="QLabel" name="top_right_1">
                   <property name="minimumSize">
                    <size>
                     <width>40</width>
                     <height>40</height>
                    </size>
                   </property>
                   <property name="text">
                    <string/>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <spacer name="verticalSpacer">
                   <property name="orientation">
                    <enum>Qt::Vertical</enum>
                   </property>
                   <property name="sizeHint" stdset="0">
                    <size>
                     <width>20</width>
                     <height>40</height>
                    </size>
                   </property>
                  </spacer>
                 </item>
                 <item>
                  <widget class="QLabel" name="bottom_right_1">
                   <property name="minimumSize">
                    <size>
                     <width>40</width>
                     <height>40</height>
                    </size>
                   </property>
                   <property name="text">
                    <string/>
                   </property>
                  </widget>
                 </item>
                </layout>
               </item>
              </layout>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_62">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>20</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">
    QLabel {
        background-color: rgb(31, 65, 96);
        color: white;
        font-weight: bold;
    }
</string>
            </property>
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <widget class="QLabel" name="label_64">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Ignored">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>10</width>
            <height>0</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>10</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">
    QLabel {
        background-color: rgb(31, 65, 96);
        color: white;
        font-weight: bold;
    }
</string>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <widget class="QLabel" name="label_74">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>20</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>20</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true">
    QLabel {
        background-color: rgb(27, 28, 30);
        color: white;
        font-weight: bold;
    }
</string>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
     </layout>
    </item>
   </layout>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources/>
 <connections/>
</ui>
