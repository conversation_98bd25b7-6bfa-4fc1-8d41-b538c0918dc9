<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>800</width>
    <height>645</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <property name="styleSheet">
   <string notr="true">background:rgb(50, 51, 61);</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QGridLayout" name="gridLayout_6">
    <item row="0" column="0">
     <layout class="QVBoxLayout" name="verticalLayout_9" stretch="1,1,1">
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_10" stretch="1,1">
        <item>
         <widget class="QGroupBox" name="groupBox">
          <property name="styleSheet">
           <string notr="true">QGroupBox {
    border: 2px solid rgb(255, 255, 255);
    border-radius: 5px;
    margin-top: 20px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 20px;
    color: rgb(255, 255, 255);
    font-size: 20px;
}
</string>
          </property>
          <property name="title">
           <string>叶片摆放数量</string>
          </property>
          <layout class="QGridLayout" name="gridLayout">
           <item row="0" column="0">
            <layout class="QVBoxLayout" name="verticalLayout" stretch="1,1">
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout" stretch="1,1">
               <item>
                <widget class="QLabel" name="label_7">
                 <property name="styleSheet">
                  <string notr="true">QLabel {font-size: 20px;
color:rgb(255, 255, 255)}</string>
                 </property>
                 <property name="text">
                  <string>行方向数量</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignCenter</set>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QSpinBox" name="spinBox">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>40</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>16777215</width>
                   <height>16777215</height>
                  </size>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">QSpinBox {
    border: 2px solid white;
    color: white;
font-size: 20px;}</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignCenter</set>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="1,1">
               <item>
                <widget class="QLabel" name="label_8">
                 <property name="styleSheet">
                  <string notr="true">QLabel {font-size: 20px;
color:rgb(255, 255, 255)}</string>
                 </property>
                 <property name="text">
                  <string>列方向数量</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignCenter</set>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QSpinBox" name="spinBox_2">
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>40</height>
                  </size>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">QSpinBox {
    border: 2px solid white;
    color: white;
font-size: 20px;}</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignCenter</set>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QWidget" name="widget" native="true">
          <property name="styleSheet">
           <string notr="true">QWidget {
    border: 2px solid rgb(255, 95, 0);
}
</string>
          </property>
          <layout class="QGridLayout" name="gridLayout_5">
           <item row="0" column="0">
            <layout class="QHBoxLayout" name="horizontalLayout_9">
             <item>
              <widget class="QGroupBox" name="groupBox_3">
               <property name="styleSheet">
                <string notr="true">QGroupBox {
    border: 2px solid rgb(255, 255, 255);
    border-radius: 5px;
    margin-top: 20px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 20px;
    color: rgb(255, 255, 255);
    font-size: 20px;
}
</string>
               </property>
               <property name="title">
                <string>叶片摆放形式</string>
               </property>
               <layout class="QGridLayout" name="gridLayout_3">
                <item row="0" column="0">
                 <layout class="QVBoxLayout" name="verticalLayout_6">
                  <item>
                   <widget class="QPushButton" name="pushButton_6">
                    <property name="sizePolicy">
                     <sizepolicy hsizetype="Minimum" vsizetype="Expanding">
                      <horstretch>0</horstretch>
                      <verstretch>0</verstretch>
                     </sizepolicy>
                    </property>
                    <property name="styleSheet">
                     <string notr="true">QPushButton {
    border: 2px solid black;
    color: white;
    font-size: 20px;
    background-color: transparent;  /* 初始背景透明或自定义颜色 */
}

QPushButton:pressed {
    background-color: rgb(255, 95, 0);       /* 按下时背景变橙色 */
}
</string>
                    </property>
                    <property name="text">
                     <string>行方向优先</string>
                    </property>
                   </widget>
                  </item>
                  <item>
                   <widget class="QPushButton" name="pushButton_7">
                    <property name="sizePolicy">
                     <sizepolicy hsizetype="Minimum" vsizetype="Expanding">
                      <horstretch>0</horstretch>
                      <verstretch>0</verstretch>
                     </sizepolicy>
                    </property>
                    <property name="styleSheet">
                     <string notr="true">QPushButton {
    border: 2px solid black;
    color: white;
    font-size: 20px;
    background-color: transparent;  /* 初始背景透明或自定义颜色 */
}

QPushButton:pressed {
    background-color: orange;       /* 按下时背景变橙色 */
}
</string>
                    </property>
                    <property name="text">
                     <string>列方向优先</string>
                    </property>
                   </widget>
                  </item>
                 </layout>
                </item>
               </layout>
              </widget>
             </item>
             <item>
              <widget class="QGroupBox" name="groupBox_4">
               <property name="styleSheet">
                <string notr="true">QGroupBox {
    border: 2px solid rgb(255, 255, 255);
    border-radius: 5px;
    margin-top: 20px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 20px;
    color: rgb(255, 255, 255);
    font-size: 20px;
}
</string>
               </property>
               <property name="title">
                <string>抓取测试</string>
               </property>
               <layout class="QGridLayout" name="gridLayout_4">
                <item row="0" column="0">
                 <layout class="QVBoxLayout" name="verticalLayout_7" stretch="1,1">
                  <item>
                   <widget class="QSpinBox" name="spinBox_3">
                    <property name="sizePolicy">
                     <sizepolicy hsizetype="Minimum" vsizetype="Expanding">
                      <horstretch>0</horstretch>
                      <verstretch>0</verstretch>
                     </sizepolicy>
                    </property>
                    <property name="minimumSize">
                     <size>
                      <width>0</width>
                      <height>40</height>
                     </size>
                    </property>
                    <property name="maximumSize">
                     <size>
                      <width>16777215</width>
                      <height>16777215</height>
                     </size>
                    </property>
                    <property name="styleSheet">
                     <string notr="true">QSpinBox {
    border: 2px solid white;
    color: white;
font-size: 20px;}</string>
                    </property>
                    <property name="alignment">
                     <set>Qt::AlignCenter</set>
                    </property>
                   </widget>
                  </item>
                  <item>
                   <widget class="QPushButton" name="pushButton_8">
                    <property name="sizePolicy">
                     <sizepolicy hsizetype="Minimum" vsizetype="Expanding">
                      <horstretch>0</horstretch>
                      <verstretch>0</verstretch>
                     </sizepolicy>
                    </property>
                    <property name="styleSheet">
                     <string notr="true">QPushButton {
    border: 2px solid black;
    color: white;
    font-size: 20px;
    background-color: transparent;  /* 初始背景透明或自定义颜色 */
}

QPushButton:pressed {
    background-color: orange;       /* 按下时背景变橙色 */
}
</string>
                    </property>
                    <property name="text">
                     <string>开始测试</string>
                    </property>
                   </widget>
                  </item>
                 </layout>
                </item>
               </layout>
              </widget>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_11" stretch="1,1">
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_3" stretch="1,1">
          <item>
           <layout class="QVBoxLayout" name="verticalLayout_2" stretch="1,2">
            <item>
             <widget class="QLabel" name="label_2">
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>30</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">QLabel {font-size: 16px;
background: rgb(50, 51, 61);
    color: white}</string>
              </property>
              <property name="text">
               <string>左上摆放点</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="pushButton">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>60</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">QPushButton {
    border: 2px solid black;
    color: white;
    font-size: 16px;
}
QPushButton:pressed {
    background-color: rgb(255, 95, 0);       /* 按下时背景变橙色 */
}</string>
              </property>
              <property name="text">
               <string>获取</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <widget class="QLabel" name="label">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>150</width>
              <height>140</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>150</width>
              <height>140</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">QLabel {
    color: white;
    border: 2px solid white;
    border-radius: 4px;
    padding: 8px;
    font-size: 16px;
}
</string>
            </property>
            <property name="text">
             <string>1145.24
-6.68
844.94
2.09
0.0
0.79</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignTop</set>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_5">
          <item>
           <layout class="QVBoxLayout" name="verticalLayout_4" stretch="1,1">
            <item>
             <widget class="QLabel" name="label_5">
              <property name="styleSheet">
               <string notr="true">QLabel {font-size: 20px;
background: rgb(50, 51, 61);
    color: white}</string>
              </property>
              <property name="text">
               <string>右上摆放点</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="pushButton_3">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Minimum" vsizetype="Expanding">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="styleSheet">
               <string notr="true">QPushButton {
    border: 2px solid black;
    color: white;
    font-size: 20px;
}
QPushButton:pressed {
    background-color: rgb(255, 95, 0);       /* 按下时背景变橙色 */
}</string>
              </property>
              <property name="text">
               <string>获取</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <widget class="QLabel" name="label_6">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>120</width>
              <height>120</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>120</width>
              <height>120</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">QLabel {
    color: white;
    border: 2px solid white;
    border-radius: 4px;
    padding: 4px;
    font-size: 14px;
}
</string>
            </property>
            <property name="text">
             <string>1144.18
73.5
844.94
2.09
0.0
0.79</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignTop</set>
            </property>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_12" stretch="1,1">
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_4">
          <item>
           <layout class="QVBoxLayout" name="verticalLayout_3" stretch="1,1">
            <item>
             <widget class="QLabel" name="label_4">
              <property name="styleSheet">
               <string notr="true">QLabel {font-size: 20px;
background: rgb(50, 51, 61);
    color: white}</string>
              </property>
              <property name="text">
               <string>左下摆放点</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="pushButton_2">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Minimum" vsizetype="Expanding">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="styleSheet">
               <string notr="true">QPushButton {
    border: 2px solid black;
    color: white;
    font-size: 20px;
}
QPushButton:pressed {
    background-color: rgb(255, 95, 0);       /* 按下时背景变橙色 */
}
</string>
              </property>
              <property name="text">
               <string>获取</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <widget class="QLabel" name="label_3">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>120</width>
              <height>120</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>120</width>
              <height>120</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">QLabel {
    color: white;
    border: 2px solid white;
    border-radius: 4px;
    padding: 4px;
    font-size: 14px;
}
</string>
            </property>
            <property name="text">
             <string>1459.64
-3.93
844.94
2.09
0.0
0.79</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignTop</set>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QVBoxLayout" name="verticalLayout_8" stretch="3,1">
          <item>
           <widget class="QGroupBox" name="groupBox_2">
            <property name="styleSheet">
             <string notr="true">QGroupBox {
    border: 2px solid rgb(255, 255, 255);
    border-radius: 5px;
    margin-top: 20px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 20px;
    color: rgb(255, 255, 255);
    font-size: 20px;
}
</string>
            </property>
            <property name="title">
             <string>行列间距</string>
            </property>
            <layout class="QGridLayout" name="gridLayout_2">
             <item row="0" column="0">
              <layout class="QVBoxLayout" name="verticalLayout_5">
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_7" stretch="1,4,1">
                 <item>
                  <widget class="QLabel" name="label_9">
                   <property name="styleSheet">
                    <string notr="true">QLabel {font-size: 20px;
background: rgb(50, 51, 61);
    color: white}</string>
                   </property>
                   <property name="text">
                    <string>行</string>
                   </property>
                   <property name="alignment">
                    <set>Qt::AlignCenter</set>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QLabel" name="label_11">
                   <property name="styleSheet">
                    <string notr="true">QLabel {
    border: 1px solid black;    /* 黑色边框 */
    font-size: 13px;            /* 字体大小 15px */
    color: white;               /* 可选：字体颜色为白色 */
    padding: 2px;               /* 可选：内边距 */
}
</string>
                   </property>
                   <property name="text">
                    <string>X:44 Y;39 Z:0</string>
                   </property>
                   <property name="alignment">
                    <set>Qt::AlignCenter</set>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QLabel" name="label_13">
                   <property name="styleSheet">
                    <string notr="true">QLabel {font-size: 20px;
background: rgb(50, 51, 61);
    color: white}</string>
                   </property>
                   <property name="text">
                    <string>mm</string>
                   </property>
                   <property name="alignment">
                    <set>Qt::AlignCenter</set>
                   </property>
                  </widget>
                 </item>
                </layout>
               </item>
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_6" stretch="1,4,1">
                 <item>
                  <widget class="QLabel" name="label_10">
                   <property name="styleSheet">
                    <string notr="true">QLabel {font-size: 20px;
background: rgb(50, 51, 61);
    color: white}</string>
                   </property>
                   <property name="text">
                    <string>列</string>
                   </property>
                   <property name="alignment">
                    <set>Qt::AlignCenter</set>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QLabel" name="label_12">
                   <property name="styleSheet">
                    <string notr="true">QLabel {
    border: 1px solid black;    /* 黑色边框 */
    font-size: 13px;            /* 字体大小 15px */
    color: white;               /* 可选：字体颜色为白色 */
    padding: 2px;               /* 可选：内边距 */
}
</string>
                   </property>
                   <property name="text">
                    <string>X:44 Y;39 Z:0</string>
                   </property>
                   <property name="alignment">
                    <set>Qt::AlignCenter</set>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QLabel" name="label_14">
                   <property name="styleSheet">
                    <string notr="true">QLabel {font-size: 20px;
background: rgb(50, 51, 61);
    color: white}</string>
                   </property>
                   <property name="text">
                    <string>mm</string>
                   </property>
                   <property name="alignment">
                    <set>Qt::AlignCenter</set>
                   </property>
                  </widget>
                 </item>
                </layout>
               </item>
              </layout>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_8" stretch="8,1,8">
            <item>
             <widget class="QPushButton" name="pushButton_4">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="styleSheet">
               <string notr="true">QPushButton {
    border: 2px solid black;
    color: white;
    font-size: 20px;
}
QPushButton:pressed {
    background-color: rgb(255, 95, 0);       /* 按下时背景变橙色 */
}</string>
              </property>
              <property name="text">
               <string>确定</string>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <widget class="QPushButton" name="pushButton_5">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="styleSheet">
               <string notr="true">QPushButton {
    border: 2px solid black;
    color: white;
    font-size: 20px;
}
QPushButton:pressed {
    background-color: rgb(255, 95, 0);       /* 按下时背景变橙色 */
}</string>
              </property>
              <property name="text">
               <string>取消</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </item>
       </layout>
      </item>
     </layout>
    </item>
   </layout>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources/>
 <connections/>
</ui>
