import sys
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QPixmap
from PyQt5 import QtWidgets, uic
from PyQt5.QtWidgets import QLabel

class ConMainWindow(QtWidgets.QMainWindow):
    def __init__(self, task_name=None, parent=None):
        super().__init__(parent)
        uic.loadUi("static/ui/window.ui", self)
        self.operator_2 = self.findChild(QtWidgets.QLineEdit, "operator_2")
        self.number = self.findChild(QtWidgets.QLineEdit, "number")
        self.id = self.findChild(QtWidgets.QLineEdit, "id")
        self.abnormal = self.findChild(QtWidgets.QPushButton, "abnormal")
        self.classification = self.findChild(QtWidgets.QPushButton, "classification")
        self.local = self.findChild(QtWidgets.QPushButton, "local")
        self.start = self.findChild(QtWidgets.QPushButton, "start")
        self.stop = self.findChild(QtWidgets.QPushButton, "stop")
        self.progressBar = self.findChild(QtWidgets.QProgressBar, "progressBar")
        self.number_detected = self.findChild(QtWidgets.QLineEdit, "number_detected")
        self.year_month = self.findChild(QtWidgets.QLabel, "year_month")
        self.hours = self.findChild(QtWidgets.QLabel, "hours")
        self.running = self.findChild(QtWidgets.QLabel, "running")
        self.comboBox_2 = self.findChild(QtWidgets.QComboBox, "comboBox_2")
        self.output = self.findChild(QtWidgets.QPushButton, "output")
        self.image_labels = []
        self.btns = []
        self.decided_flags = [False] * 72
        self.selected_flags = [False] * 72
        self.scrapped_flags = [False] * 72
        self.current_selected_index = None
        for i in range(1, 73):
            weight = self.findChild(QtWidgets.QWidget, f"widget{i}")
            if weight:
                margin = 5
                label = QLabel(weight)
                label.setGeometry(0, 0, weight.width(), weight.height())
                pixmap = QPixmap("static/ui/10.png")
                scaled_pixmap = pixmap.scaled(
                    int(label.width() * 0.8),
                    int(label.height() * 0.8),
                    Qt.KeepAspectRatio,
                    Qt.SmoothTransformation
                )
                label.setPixmap(scaled_pixmap)
                label.setAlignment(Qt.AlignCenter)
                label.setScaledContents(False)
                self.image_labels.append(label)
                self.selected_flags.append(False)
            else:
                self.image_labels.append(None)
                self.selected_flags.append(False)

            btn = self.findChild(QtWidgets.QPushButton, f"btn{i}")
            if btn:
                self.btns.append(btn)
                btn.clicked.connect(lambda checked=False, idx=i: self.label_clicked(idx))
                btn.setStyleSheet("""
                    background: transparent;
                    color: black;
                    font-weight: normal;
                    font-size: 24px;
                    font-weight: bold;
                    padding-left: 20px; 
                """)
                btn.raise_()

        self.top_left = self.findChild(QtWidgets.QLabel, "top_left")
        self.bottom_left = self.findChild(QtWidgets.QLabel, "bottom_left")
        self.top_right = self.findChild(QtWidgets.QLabel, "top_right")
        self.bottom_right = self.findChild(QtWidgets.QLabel, "bottom_right")
        self.batch_number = self.findChild(QtWidgets.QLabel, "batch_number")
        self.blade_id = self.findChild(QtWidgets.QLabel, "blade_id")
        self.defect = self.findChild(QtWidgets.QLabel, "defect")
        self.graphicsView = self.findChild(QtWidgets.QGraphicsView, "graphicsView")
        self.previous = self.findChild(QtWidgets.QPushButton, "previous")
        self.next = self.findChild(QtWidgets.QPushButton, "next")
        self.decision_making = self.findChild(QtWidgets.QPushButton, "decision_making")
        self.retest = self.findChild(QtWidgets.QPushButton, "retest")
        self.scrap = self.findChild(QtWidgets.QPushButton, "scrap")
        self.graphicsView_2 = self.findChild(QtWidgets.QGraphicsView, "graphicsView_2")
        self.top_left_1 = self.findChild(QtWidgets.QLabel, "top_left_1")
        self.bottom_left_1 = self.findChild(QtWidgets.QLabel, "bottom_left_1")
        self.top_right_1 = self.findChild(QtWidgets.QLabel, "top_right_1")
        self.bottom_right_1 = self.findChild(QtWidgets.QLabel, "bottom_right_1")


        # self.logo = self.findChild(QtWidgets.QLabel, "logo")
        # pixmap = QPixmap("static/ui/img_2.png")
        # self.logo.setPixmap(pixmap)
        # self.logo.setScaledContents(True)
        # self.logo.setFixedHeight(self.logo.height() + 20)
        #
        #
        # self.title = self.findChild(QtWidgets.QLabel, "titile")
        # pixmap1 = QPixmap("static/ui/img.png")
        # self.title.setPixmap(pixmap1)
        # self.title.setScaledContents(True)
        # self.title.setFixedHeight(self.title.height() + 20)


        self.start.clicked.connect(self.start_check)
        self.scrap.clicked.connect(self.on_scrap_clicked)
        self.decision_making.clicked.connect(self.on_decision_making_clicked)

        if task_name:
            self.setWindowTitle(f"任务详情 - {task_name}")

    def label_clicked(self, index):
        print(f"按钮 {index} 被点击")
        idx = index - 1
        if self.current_selected_index is not None and self.current_selected_index != idx:
            last_label = self.image_labels[self.current_selected_index]
            if last_label:
                if self.scrapped_flags[self.current_selected_index]:
                    pixmap = QPixmap("static/ui/7.png")
                elif self.decided_flags[self.current_selected_index]:
                    pixmap = QPixmap("static/ui/6.png")
                else:
                    pixmap = QPixmap("static/ui/10.png")

                scaled_pixmap = pixmap.scaled(
                    int(last_label.width() * 0.8),
                    int(last_label.height() * 0.8),
                    Qt.KeepAspectRatio,
                    Qt.SmoothTransformation
                )
                last_label.setPixmap(scaled_pixmap)
                self.selected_flags[self.current_selected_index] = False

        current_label = self.image_labels[idx]
        if not current_label:
            return
        if self.selected_flags[idx]:
            if self.scrapped_flags[idx]:
                pixmap = QPixmap("static/ui/7.png")
            elif self.decided_flags[idx]:
                pixmap = QPixmap("static/ui/6.png")
            else:
                pixmap = QPixmap("static/ui/10.png")

            self.selected_flags[idx] = False
            self.current_selected_index = None
        else:
            pixmap = QPixmap("static/ui/8.png")
            self.selected_flags[idx] = True
            self.current_selected_index = idx

        scaled_pixmap = pixmap.scaled(
            int(current_label.width() * 0.8),
            int(current_label.height() * 0.8),
            Qt.KeepAspectRatio,
            Qt.SmoothTransformation
        )
        current_label.setPixmap(scaled_pixmap)

    def on_scrap_clicked(self):
        if self.current_selected_index is None:
            print("未选中叶片，无法报废")
            return
        idx = self.current_selected_index
        label = self.image_labels[idx]
        if label:
            new_pixmap = QPixmap("static/ui/7.png")
            scaled_pixmap = new_pixmap.scaled(
                int(label.width() * 0.8),
                int(label.height() * 0.8),
                Qt.KeepAspectRatio,
                Qt.SmoothTransformation
            )
            label.setPixmap(scaled_pixmap)
            self.scrapped_flags[idx] = True

    def on_decision_making_clicked(self):
        if self.current_selected_index is None:
            print("未选中叶片，无法决策")
            return
        idx = self.current_selected_index
        label = self.image_labels[idx]
        if label:
            pixmap = QPixmap("static/ui/6.png")
            scaled_pixmap = pixmap.scaled(
                int(label.width() * 0.8),
                int(label.height() * 0.8),
                Qt.KeepAspectRatio,
                Qt.SmoothTransformation
            )
            label.setPixmap(scaled_pixmap)
            self.decided_flags[idx] = True
            self.scrapped_flags[idx] = False

    def start_check(self):
        print("开始检测")


if __name__ == "__main__":
    app = QtWidgets.QApplication(sys.argv)
    window = ConMainWindow()
    window.show()
    sys.exit(app.exec_())