#!/usr/bin/python
# -*- coding: utf-8 -*-

from hdmtv.core.node import Request, Response
from hdmtv.node import Publisher, Timer
from hd_robotics.core.flags import Codes
from hd_robotics.nodes.device_node import DeviceNode
from hd_robotics.core.registration import get_device_class
from blade_inspection.devices.recognizer.abstract import AbstractRecognizer


class VisualRecognizerNode(DeviceNode):
    """ Visual recognizer node class. """

    def __init__(
        self,
        node_name: str,
        endpoint: str,
        namespace: str,
        recognizer_class: str = "GeneralRecognizer",
        **options
    ) -> None:
        super().__init__(node_name=node_name, endpoint=endpoint, namespace=namespace, **options)

        # Initialize recognizer device
        self._recognizer: AbstractRecognizer = get_device_class(class_name=recognizer_class)(**options)
        self._recognizer.connect()

        # Create publishers
        self._recognizer_connection_publisher: Publisher = self.create_publisher(
            topic=self._prefix + "/recognizer_is_connected", val=False)

        # Create data publishing timer
        self._data_timer: Timer = self.create_timer(
            timer_period_sec=self._publish_interval, callback=self.publish_recognizer_data)

        # Register services
        self.create_service(srv_name=self._prefix + "/connect", callback=self.connect)
        self.create_service(srv_name=self._prefix + "/disconnect", callback=self.disconnect)
        self.create_service(srv_name=self._prefix + "/destroy", callback=self.destroy)
        self.create_service(srv_name=self._prefix + "/recognize", callback=self.recognize)
        self.create_service(srv_name=self._prefix + "/get_info", callback=self.get_info)
        self.create_service(srv_name=self._prefix + "/initialize", callback=self.initialize)
        self.create_service(srv_name=self._prefix + "/identify_furnace_batch_number",
                            callback=self.identify_furnace_batch_number)
        self.create_service(srv_name=self._prefix + "/visual_inspection_one",
                            callback=self.visual_inspection_one)
        self.create_service(srv_name=self._prefix + "/visual_inspection_two",
                            callback=self.visual_inspection_two)
        self.create_service(srv_name=self._prefix + "/visual_inspection_three",
                            callback=self.visual_inspection_three)

    def publish_recognizer_data(self) -> None:
        """ Publish the recognizer connection status """
        # 发布连接状态，先使用固定值进行测试
        connection_status = self._recognizer.is_connected if self._recognizer else False
        self._recognizer_connection_publisher.publish(msg=connection_status)

    def connect(self, request: Request) -> Response:
        """ Connect to the recognizer, it returns whether the connection is successful. """
        if self._recognizer.is_connected:
            return Response(code=Codes.SUCCESS, message="视觉识别器已经连接")
        return self._single_function_wrapper(
            function=self._recognizer.connect, request=request, error_code=Codes.ERROR)

    def disconnect(self, request: Request) -> Response:
        """ Disconnect to the recognizer, it returns whether the connection has been terminated. """
        if not self._recognizer.is_connected:
            return Response(code=Codes.SUCCESS, message="视觉识别器已经断开连接")
        return self._single_function_wrapper(
            function=self._recognizer.disconnect, request=request, error_code=Codes.ERROR)

    def destroy(self, request: Request) -> Response:
        """ Function for destroying the recognizer object. """
        return self._single_function_wrapper(
            function=self._recognizer.destroy, request=request, error_code=Codes.ERROR)

    def recognize(self, request: Request) -> Response:
        """ Main function for part recognition. """
        return self._function_wrapper(
            function=self._recognizer.recognize, request=request, res_name="result",
            error_code=Codes.ERROR)

    def identify_furnace_batch_number(self, request: Request) -> Response:
        """ Main function for part identify furnace batch number. """
        return self._function_wrapper(
            function=self._recognizer.identify_furnace_batch_number, request=request, res_name="result",
            error_code=Codes.ERROR)

    def visual_inspection_one(self, request: Request) -> Response:
        """ Main function for part visual inspection one. """
        return self._function_wrapper(
            function=self._recognizer.visual_inspection_one, request=request, res_name="result",
            error_code=Codes.ERROR)

    def visual_inspection_two(self, request: Request) -> Response:
        """ Main function for part visual inspection two. """
        return self._function_wrapper(
            function=self._recognizer.visual_inspection_two, request=request, res_name="result",
            error_code=Codes.ERROR)

    def visual_inspection_three(self, request: Request) -> Response:
        """ Main function for part visual inspection three. """
        return self._function_wrapper(
            function=self._recognizer.visual_inspection_three, request=request, res_name="result",
            error_code=Codes.ERROR)

    def get_info(self, request: Request) -> Response:
        """ Get recognizer information. """
        try:
            info = self._recognizer.info
            return Response(code=Codes.SUCCESS, data={"info": info}, message="获取识别器信息成功")
        except Exception as e:
            self.get_logger().error(f"获取识别器信息失败: {e}")
            return Response(code=Codes.ERROR, message=f"获取识别器信息失败: {e}")

    def initialize(self, request: Request) -> Response:
        """ Initialize the recognizer. """
        if hasattr(self._recognizer, 'initialize'):
            return self._function_wrapper(
                function=self._recognizer.initialize, request=request, error_code=Codes.ERROR)
        else:
            # If no initialize method, just try to connect
            return self.connect(request)

    def destroy_node(self, **options) -> None:
        """ Destroy the recognizer node and clean up all resources. """
        super().destroy_node(**options)
        try:
            self._recognizer.destroy()
        except Exception as e:
            print(f"销毁VisualRecognizerNode时发生异常: {e}")
