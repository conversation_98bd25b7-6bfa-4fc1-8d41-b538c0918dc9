#!/usr/bin/python
# -*- coding: utf-8 -*-

import cv2
import typing as t
import numpy as np
from PIL import Image


def convert_rgb(image) -> np.ndarray:
    if image.shape[-1] == 4:
        image = image[:, :, :3]
    elif image.shape[-1] != 3:
        image = cv2.cvtColor(image, cv2.COLOR_GRAY2RGB)
    return image


def crop_image(image: np.ndarray, roi: t.Optional[t.List[int]]) -> np.ndarray:
    """ Crop image given the ROI. """
    if roi is None or len(roi) != 4:
        return image

    x_min, y_min, x_max, y_max = roi
    image_h, image_w = image.shape[:2]
    x_min, y_min = max(0, x_min), max(0, y_min)
    x_max = min(image_w, max(x_min+1, x_max))
    y_max = min(image_h, max(y_min+1, y_max))

    return image[y_min:y_max, x_min:x_max]


def map_boxes(boxes, input_shape, image_shape):
    w, h = input_shape[:2]
    img_w, img_h = image_shape[:2]
    ratio_h, ratio_w = img_h / h, img_w / w

    m_boxes = list()
    for box in boxes:
        xmin, ymin, xmax, ymax = box[0] * ratio_w, box[1] * ratio_h, box[2] * ratio_w, box[3] * ratio_h
        m_boxes.append([xmin, ymin, xmax, ymax])

    return m_boxes


def patch_image(image, input_w, input_h):
    image_h, image_w = image.shape[:2]
    image_wh_ratio = image_w / image_h
    input_wh_ratio = input_w / input_h

    if len(image.shape) == 2:
        background = np.zeros((input_h, input_w), dtype=np.float32)
    else:
        background = np.zeros((input_h, input_w, image.shape[-1]), dtype=np.float32)

    if image_wh_ratio > input_wh_ratio:
        scale = input_w / image_w
        scaled_h = int(image_h * scale)
        off_h = int((input_h - scaled_h) / 2)
        image = cv2.resize(image, (input_w, scaled_h), interpolation=cv2.INTER_LINEAR)
        background[off_h:off_h + scaled_h, :] = image
    else:
        scale = input_h / image_h
        scaled_w = int(image_w * scale)
        off_w = int((input_w - scaled_w) / 2)
        image = cv2.resize(image, (scaled_w, input_h), interpolation=cv2.INTER_LINEAR)
        background[:, off_w:off_w + scaled_w] = image

    return background


def calculate_iou(box1: list, box2: list) -> float:
    xmin1, ymin1, xmax1, ymax1 = box1
    xmin2, ymin2, xmax2, ymax2 = box2

    inter_x1 = max(xmin1, xmin2)
    inter_x2 = min(xmax1, xmax2)
    inter_y1 = max(ymin1, ymin2)
    inter_y2 = min(ymax1, ymax2)

    if inter_x1 >= inter_x2 or inter_y1 >= inter_y2:
        return 0

    inter_area = (inter_x2 - inter_x1) * (inter_y2 - inter_y1)
    area = (xmax1 - xmin1) * (ymax1 - ymin1) + (xmax2 - xmin2) * (ymax2 - ymin2)
    iou = inter_area / (area - inter_area)

    return iou


def nms_multi_classes(boxes, labels, scores, iou_thresh=0.25) -> t.Any:
    """ Non-maximum suppression over classes """
    nms_boxes, nms_labels, nms_scores = list(), list(), list()
    pop_indices = list()

    while len(boxes):
        box, label, score = boxes.pop(0), labels.pop(0), scores.pop(0)
        nms_boxes.append(box)
        nms_labels.append(label)
        nms_scores.append(score)
        pop_indices.clear()

        pop_index = 0
        for ref_box, ref_label, ref_score in zip(boxes, labels, scores):
            iou = calculate_iou(box, ref_box)
            if iou > iou_thresh:
                if ref_score > score:
                    nms_boxes[-1] = ref_box
                    nms_labels[-1] = ref_label
                    nms_scores[-1] = ref_score
                pop_indices.append(pop_index)
            pop_index += 1

        for index in pop_indices[::-1]:
            boxes.pop(index)
            labels.pop(index)
            scores.pop(index)

    return nms_boxes, nms_labels, nms_scores


def resize_image(image, input_h, input_w) -> np.ndarray:
    resolution = (input_w, input_h)

    image = Image.fromarray(image)
    image = image.resize(resolution, resample=Image.BILINEAR)
    image = np.array(image, dtype=np.float32)

    return image


def normalize_image(image) -> np.ndarray:
    image -= 127.5
    image *= 0.007843
    # HWC to CHW format:
    image = np.transpose(image, [2, 0, 1])
    # CHW to NCHW format:
    image = np.expand_dims(image, axis=0)
    # Convert the image to row-major order, also known as "C order":
    image = np.array(image, dtype=np.float32, order='C')
    return image


