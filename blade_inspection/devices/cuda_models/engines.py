#!/usr/bin/python
# -*- coding: utf-8 -*-

import os
import numpy as np
import typing as t
# from paddle import fluid
# from paddle.fluid.core import AnalysisConfig, create_paddle_predictor, PaddleTensor
from blade_inspection.devices.cuda_models.abstract import AbstractEngine


def get_engine(name: str) -> t.Type:
    """ Get the engine class variable by passing the name. """
    if name.lower() in ["pp", "paddle", "paddlepaddle"]:
        return PaddleEngine
    else:
        raise ValueError("Unsupported engine type:", name, ".")


class PaddleEngine(AbstractEngine):
    """ Base paddlepaddle v1.8.5 inference model """

    def __init__(self, model_path: str, use_gpu: bool = False, **options) -> None:
        super(PaddleEngine, self).__init__(model_path, use_gpu, **options)
        # if use_gpu:
        #     self._exe = fluid.Executor(fluid.CUDAPlace(0))
        # else:
        #     self._exe = fluid.Executor(fluid.CPUPlace())
        #
        # self._predictor, self._input_tensor_dict, self._output_tensor_dict = self._get_infer_engine()

    def cls(self, image: np.ndarray, **options) -> t.Tuple[int, float]:
        """ Classify the input image and returns the integer label and confidence score. """
        # outputs = self._paddle_cls_infer(image)
        # index = np.argmax(outputs[0])
        # score = outputs[0][0, index]
        # return int(index), score
        return 1, 1.0  # Fake output because the test environment does not have paddlepaddle installed.

    def det(self, image: np.ndarray, **options) -> t.Tuple[t.List[t.List[float]], t.List[int], t.List[float]]:
        """ Detect the input image and returns the localities, integer labels, and confidence scores. """
        # outputs = self._paddle_det_infer(image)
        #
        # results = np.array(outputs[0])
        # if results.shape[1] != 6:
        #     labels = list()
        #     scores = list()
        #     boxes = list()
        # else:
        #     labels = results[:, 0].astype('int32').tolist()
        #     scores = results[:, 1].astype('float32').tolist()
        #     boxes = results[:, 2:].astype('float32').tolist()
        # return boxes, labels, scores
        return [[0.0, 0.0, 1.0, 1.0]], [1], [1.0]  # Fake output because the test environment does not have paddlepaddle installed.

    def _paddle_cls_infer(self, image: np.ndarray) -> t.List[t.Any]:
        """ Put image info the paddlepaddle classification inference engine and do the inference. """
        # image = PaddleTensor(image)
        # self._predictor.run([image])
        #
        # outputs: t.List[t.Any] = list()
        # for output_tensor in self._output_tensor_dict.values():
        #     output = output_tensor.copy_to_cpu()
        #     outputs.append(output)
        # return outputs
        pass

    def _paddle_det_infer(self, image: np.ndarray) -> t.List[t.Any]:
        """ Put image info the paddlepaddle detection inference engine and do the inference. """
        # image = PaddleTensor(image)
        # input_shape = self._options.get("input_shape")
        # if not input_shape:
        #     raise AssertionError("Paddle engine input shape not defined.")
        # else:
        #     input_w, input_h = input_shape
        #     input_shape[0], input_shape[1] = input_h, input_w
        #     input_shape = np.array(input_shape).astype(np.uint32)
        #     input_shape = PaddleTensor(input_shape[np.newaxis, :])
        #
        # self._predictor.run([image, input_shape])
        #
        # outputs: t.List[t.Any] = list()
        # for output_tensor in self._output_tensor_dict.values():
        #     output = output_tensor.copy_to_cpu()
        #     outputs.append(output)
        # return outputs
        pass

    def _get_infer_engine(self, **options) -> t.Tuple[t.Any, t.Dict, t.Dict]:
        """ Get the paddlepaddle v1.8.5 inference engine """
        # model_file_path = os.path.join(self._model_path, '__model__')
        # params_file_path = os.path.join(self._model_path, 'params')
        # engine = AnalysisConfig(model_file_path, params_file_path)
        # engine.enable_use_gpu(options.get("gpu_mem", 512), options.get("device_id", 0))
        # # engine.enable_tensorrt_engine(precision_mode=AnalysisConfig.Precision.Float32, use_calib_mode=False)
        # engine.disable_glog_info()
        #
        # predictor = create_paddle_predictor(engine)
        #
        # input_names = predictor.get_input_names()
        # input_tensor_dict: t.Dict[str, t.Any] = dict()
        # for input_name in input_names:
        #     input_tensor_dict[input_name] = predictor.get_input_tensor(input_name)
        #
        # output_names = predictor.get_output_names()
        # output_tensor_dict: t.Dict[str, t.Any] = dict()
        # for output_name in output_names:
        #     output_tensor = predictor.get_output_tensor(output_name)
        #     output_tensor_dict[output_name] = output_tensor
        #
        # return predictor, input_tensor_dict, output_tensor_dict
        pass

