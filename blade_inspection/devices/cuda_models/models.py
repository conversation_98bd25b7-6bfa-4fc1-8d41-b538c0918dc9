#!/usr/bin/python
# -*- coding: utf-8 -*-

import typing as t
import numpy as np
from blade_inspection.devices.cuda_models.engines import get_engine
from blade_inspection.devices.cuda_models.abstract import AbstractEngine, AbstractModel, AbstractPreProcess, AbstractPostProcess, DefaultPostProcess
from blade_inspection.devices.cuda_models.utils import nms_multi_classes, map_boxes


def get_cuda_model(name: str) -> t.ClassVar:
    """ Returns the cuda model by giving the model's name. """
    if name.lower() in ["cls", "class", "classification"]:
        return ClsModel
    elif name.lower() in ["det", "detect", "detection"]:
        return DetModel
    else:
        raise ValueError("Unsupported cuda model type:", name, ".")


class DetPostProcess(DefaultPostProcess):
    """ Postprocessing object for the detection. """

    def __init__(self, **options):
        super(DetPostProcess, self).__init__(**options)
        self._input_shape = options.get("input_shape", (352, 352))
        self._obj_threshold = options.get("obj_threshold", 0.5)
        self._nms_threshold = options.get("nms_threshold", 0.5)

    def process(self, origin: np.ndarray, result: t.Dict[str, t.Any], **options) -> t.Dict[str, t.Any]:
        """Main function for the detection postprocessing.  """
        result = super(DetPostProcess, self).process(origin, result, **options)

        origin_h, origin_w = origin.shape[:2]
        boxes, labels, scores = result.get("boxes"), result.get("labels"), result.get("scores")
        # boxes, labels, scores = nms_multi_classes(boxes, labels, scores, self._nms_threshold)
        boxes = map_boxes(boxes, self._input_shape, (origin_w, origin_h))

        result["boxes"] = boxes
        result["labels"] = labels
        result["scores"] = scores

        return result


class ClsModel(AbstractModel):
    """ Wrapper for the classification model, it will output the image's class and confidence score,
        this object will output the detection result with the following format:

        result = {
            "image": np.ndarray,
            "label": list[int],
            "score": list[float]
        }
    """

    def __init__(
        self,
        model_path: str,
        input_shape: t.Union[tuple, list],
        engine_type: str,
        use_gpu: bool = False,
        pre_process: t.Optional[AbstractPreProcess] = None,
        post_process: t.Optional[AbstractPostProcess] = None,
        **options
    ) -> None:
        super(ClsModel, self).__init__(model_path, input_shape, engine_type, use_gpu,
                                       pre_process, post_process, **options)
        self._obj_threshold = options.get("obj_threshold", 0.5)

    def _set_engine(self, engine_type: str, model_path: str, use_gpu: bool, **options) -> AbstractEngine:
        """ Set up the classification model engine. """
        return get_engine(engine_type)(model_path, input_shape=self._input_shape, use_gpu=use_gpu)

    def _infer(self, image: np.ndarray, **options) -> t.Dict[str, t.Any]:
        """ Core inference process definition. """
        label, score = self._engine.cls(image)

        result = {
            "label": label,
            "score": score
        }

        return result


class DetModel(AbstractModel):
    """ Wrapper for the detection model, it will localize each character and its label.
        Given the input shape and some key parameters, this object will output the detection result
        with the following format:

        result = {
            "image": np.ndarray,
            "boxes": list[list[float]],
            "labels": list[int],
            "scores": list[float]
        }
    """

    def __init__(
        self,
        model_path: str,
        input_shape: t.Iterable[int],
        engine_type: str,
        use_gpu: bool = False,
        pre_process: t.Optional[AbstractPreProcess] = None,
        post_process: t.Optional[AbstractPostProcess] = None,
        **options
    ) -> None:
        if post_process is None:
            post_process = DetPostProcess(model_path=model_path, input_shape=input_shape, engine_type=engine_type,
                                          use_gpu=use_gpu, **options)
        super(DetModel, self).__init__(model_path, input_shape, engine_type, use_gpu,
                                       pre_process, post_process, **options)

        self._obj_threshold = options.get("obj_threshold", 0.5)
        self._nms_threshold = options.get("nms_threshold", 0.5)

    def _set_engine(self, engine_type: str, model_path: str, use_gpu: bool, **options) -> AbstractEngine:
        """ Setup the detection model engine. """
        return get_engine(engine_type)(model_path, input_shape=self._input_shape, use_gpu=use_gpu)

    def _infer(self, image: np.ndarray, **options) -> t.Dict[str, t.Any]:
        """ Core inference process definition. """
        boxes, labels, scores = self._engine.det(image)

        result = {
            "boxes": boxes,
            "labels": labels,
            "scores": scores
        }

        return result
