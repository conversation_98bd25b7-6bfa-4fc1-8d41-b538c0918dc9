#!/usr/bin/python
# -*- coding: utf-8 -*-

import os
import copy
import typing as t
import numpy as np
from abc import abstractmethod
from blade_inspection.devices.cuda_models.utils import convert_rgb, crop_image, patch_image, normalize_image


class AbstractPreProcess(object):
    """ Abstract preprocess object, which serves as the customized preprocessing object for the AbstractModel class """

    def __init__(self, *args, **kwargs) -> None:
        """ Initialization function. """
        pass

    @abstractmethod
    def process(self, origin: np.ndarray, **options) -> np.ndarray:
        """ Main function for the preprocessing API. """
        pass

    def __call__(self, origin: np.ndarray, **options) -> np.ndarray:
        """ Callable function for the preprocessing API. """
        return self.process(origin, **options)


class AbstractPostProcess(object):
    """ Abstract post-process object, which serves as the customized post-processing object for the AbstractModel
        class
    """

    def __init__(self, *args, **kwargs) -> None:
        """ Initialization function. """
        pass

    @abstractmethod
    def process(self, origin: np.ndarray, result: t.Dict[str, t.Any], **options) -> t.Dict[str, t.Any]:
        """ Main function for the post-processing API. """
        pass

    def __call__(self, origin: np.ndarray, result: t.Dict[str, t.Any], **options) -> t.Dict[str, t.Any]:
        """ Callable function for the post-processing API. """
        return self.process(origin, result, **options)


class DefaultPreProcess(AbstractPreProcess):
    """ Default preprocessing class. """

    def __init__(self, **options):
        super(DefaultPreProcess, self).__init__(**options)
        self._input_shape = options.get("input_shape")

    def process(self, origin: np.ndarray, **options) -> np.ndarray:
        """ Main function for the preprocessing API. """
        image = copy.deepcopy(origin)
        roi: t.Optional[t.List[int]] = options.get("roi")
        image = crop_image(image, roi)
        image = convert_rgb(image)
        image = patch_image(image, self._input_shape[0], self._input_shape[1])
        image = normalize_image(image)

        return image


class DefaultPostProcess(AbstractPostProcess):
    """ Default postprocessing class. """

    def __init__(self, **options):
        super(DefaultPostProcess, self).__init__(**options)
        self._label_map: t.Optional[t.List[str]] = options.get("label_map")

    def process(self, origin: np.ndarray, result: t.Dict[str, t.Any], **options) -> t.Dict[str, t.Any]:
        """ Main function for the preprocessing API. """
        result["image"] = origin

        if "labels" in result:
            result["labels"] = self._map_labels(result["labels"])
        if "label" in result:
            result["label"] = self._map_label(result["label"])

        return result

    def _map_label(self, label: int) -> t.Union[int, str]:
        """ Map the integer label into string label """
        if self._label_map is None:
            return label
        if label < len(self._label_map):
            return self._label_map[label]
        else:
            return ""

    def _map_labels(self, labels: t.List[int]) -> t.Union[t.List[int], t.List[str]]:
        """ Map the integer labels into string labels """
        if self._label_map is None:
            return labels

        str_labels: t.List[str] = list()
        for label in labels:
            str_labels.append(self._map_label(label))
        return str_labels


class AbstractEngine(object):
    """ Base model object, abstract wrapper for different deep learning frameworks. """

    _file_path: str = os.path.dirname(__file__)
    _root_path: str = os.path.abspath(os.path.join(_file_path, "../.."))
    _default_model_dir: str = os.path.join(_root_path, "static/cuda_models")

    def __init__(self, model_path: str, use_gpu: bool = False, **options) -> None:
        """ Initialize function. """
        model_dir = options.get("model_dir", AbstractEngine._default_model_dir)
        self._model_path = os.path.join(model_dir, model_path)
        self._use_gpu = use_gpu
        self._options = options

    @abstractmethod
    def cls(self, image: np.ndarray, **options) -> t.Tuple[int, float]:
        """ Abstract classification wrapper function, which returns the inference result as:
            (label: int, score: float)
        """
        pass

    @abstractmethod
    def det(self, image: np.ndarray, **options) -> t.Tuple[t.List[t.List[float]], t.List[int], t.List[float]]:
        """ Abstract detection wrapper function, which returns the inference result as:
            (boxes: list of box points, labels: list of int, score: list of float values)
        """
        pass


class AbstractModel(object):
    """ Abstract Cuda model class. """

    def __init__(
        self,
        model_path: str,
        input_shape: t.Iterable[int],
        engine_type: str,
        use_gpu: bool = False,
        pre_process: t.Optional[AbstractPreProcess] = None,
        post_process: t.Optional[AbstractPostProcess] = None,
        **options
    ) -> None:
        """ Initialization function. """
        self._model_path = model_path
        self._input_shape = input_shape
        self._engine_type = engine_type
        self._use_gpu = use_gpu
        self._engine = self._set_engine(engine_type, model_path, use_gpu)
        self._options = options

        if pre_process is None:
            pre_process = DefaultPreProcess(model_path=model_path, input_shape=input_shape, engine_type=engine_type,
                                            use_gpu=use_gpu, **options)
        if post_process is None:
            post_process = DefaultPostProcess(model_path=model_path, input_shape=input_shape, engine_type=engine_type,
                                              use_gpu=use_gpu, **options)
        self._pre_process = pre_process
        self._post_process = post_process

    @property
    def pre_process(self) -> t.Optional[AbstractPreProcess]:
        """ Returns the pre-process object. """
        return self._pre_process

    @property
    def post_process(self) -> t.Optional[AbstractPostProcess]:
        """ Returns post-process object. """
        return self._post_process

    def infer(self, origin: np.ndarray, **options) -> t.Dict[str, t.Any]:
        """ Main API for producing the detection result. """
        image = self._pre_process(origin, **options)
        result = self._infer(image, **options)
        result = self._post_process(origin, result, **options)

        return result

    @abstractmethod
    def _set_engine(self, engine_type: str, model_path: str, use_gpu: bool, **options) -> AbstractEngine:
        """ Set the cuda model engine. """
        pass

    @abstractmethod
    def _infer(self, image: np.ndarray, **options) -> t.Dict[str, t.Any]:
        """ Core inference process definition. """
        pass

    def __call__(self, image: np.ndarray, **options) -> t.Dict[str, t.Any]:
        """ Main API for producing the detection result. """
        return self.infer(image, **options)

