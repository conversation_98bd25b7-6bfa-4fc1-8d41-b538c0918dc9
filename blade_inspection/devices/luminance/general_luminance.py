#!/usr/bin/python
# -*- coding: utf-8 -*-

import time
import serial
import typing as t
from blade_inspection.devices.luminance.abstract import AbstractLuminance


class GeneralLuminance(AbstractLuminance):
    """ General luminance controlling object, it controls the luminance through sending serial signals into the
        luminance controller. """

    def __init__(self, **options) -> None:
        super(GeneralLuminance, self).__init__(**options)
        self._port: str = options.get("port", "COM1")
        self._bps: int = options.get("baudrate", 9600)
        self._timeout: int = int(options.get("wait_time", 0.2))
        self._num_channels: int = options.get("num_channels", 4)
        self._serial: t.Optional[serial.Serial] = None
        self._intensities: t.List[int] = list()
        for c in range(self._num_channels):
            self._intensities.append(0)
        self._is_connected: bool = False

    @property
    def is_connected(self) -> bool:
        """ Returns whether the luminance device has been connected. """
        return self._is_connected

    def connect(self, **options) -> bool:
        """ Main API for connecting to the luminance device. """
        try:
            self._serial = serial.Serial(self._port, self._bps, timeout=self._timeout)
            for c in range(self._num_channels):
                self.set(channel=c+1, intensity=0)
            self._is_connected = True
        except Exception as e:
            self._serial = None
            self._is_connected = False
            print("---异常---：", e)
        return self._is_connected

    def disconnect(self, **options) -> bool:
        """ Main API for disconnecting to the luminance device. """
        try:
            if self._serial and self._serial.is_open:
                self._serial.close()
                print("✅ 串口连接已关闭")
        except Exception as e:
            print(f"⚠️ 关闭串口连接失败: {e}")
        finally:
            self._serial = None
            self._is_connected = False
        return True

    def open(self, channel: int, **options) -> bool:
        """ Opening the luminance device, it returns whether the operation is successfully implemented. """
        if self._serial is None or not self._is_connected:
            return False
        is_set: bool = False
        for c, intensity in enumerate(self._intensities):
            is_set = self.set(c+1, intensity)
            if not is_set:
                break
        return is_set

    def close(self, **options) -> bool:
        """ Closing the luminance device, it returns whether the operation is successfully implemented. """
        if self._serial is None or not self._is_connected:
            return False
        is_set: bool = False
        for c in range(self._num_channels):
            is_set = self.set(c+1, 0)
            if not is_set:
                break
        return is_set

    def set(self, channel: int, intensity: int, wait: float = 0.1, **options) -> bool:
        """ Setting the luminance intensity, it returns whether the operation is successfully implemented. """
        if not 0 <= intensity <= 255:
            return False
        elif not 0 < channel <= self._num_channels:
            return False
        elif self._serial is None or not self._is_connected:
            return False

        intensity_str: str = hex(intensity)[2:]
        while len(intensity_str) < 3:
            intensity_str = "0" + intensity_str
        try:
            cmd_string: str = "$3" + str(channel) + intensity_str
            check_str: str = GeneralLuminance._check_code(cmd_string)
            send_str = cmd_string + check_str
            self._serial.write(send_str.encode("gbk"))
            time.sleep(wait)
            self._serial.read()
            self._intensities[channel-1] = intensity
            return True
        except Exception as expt:
            self.disconnect()
            print(expt)
            return False

    @staticmethod
    def _check_code(serial_str) -> str:
        """ Serial coding checking method. """
        bytes_list = bytearray(serial_str, "gbk")
        check_code = bytes_list[0]
        for i in range(1, len(bytes_list)):
            check_code = check_code ^ bytes_list[i]
        str_code = "{:x}".format(check_code)
        return str_code
