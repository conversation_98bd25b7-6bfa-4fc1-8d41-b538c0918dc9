#!/usr/bin/python
# -*- coding: utf-8 -*-

from abc import ABC, abstractmethod


class AbstractLuminance(ABC):
    """ Abstract luminance controlling object. """

    def __init__(self, **options) -> None:
        self._options = options

    @property
    @abstractmethod
    def is_connected(self) -> bool:
        """ Returns whether the luminance device has been connected. """
        pass

    @abstractmethod
    def connect(self, **options) -> bool:
        """ Abstract method connecting to the luminance device. """
        pass

    @abstractmethod
    def disconnect(self, **options) -> bool:
        """ Abstract method disconnecting to the luminance device. """
        pass

    @abstractmethod
    def open(self, channel: int, **options) -> bool:
        """ Opening the luminance device, it returns whether the operation is successfully implemented. """
        pass

    @abstractmethod
    def close(self, **options) -> bool:
        """ Closing the luminance device, it returns whether the operation is successfully implemented. """
        pass

    @abstractmethod
    def set(self, channel: int, intensity: int, wait: float = 0.1, **options) -> bool:
        """ Setting the luminance intensity, it returns whether the operation is successfully implemented. """
        pass
