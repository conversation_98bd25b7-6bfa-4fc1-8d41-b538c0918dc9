#!/usr/bin/python
# -*- coding: utf-8 -*-

from blade_inspection.devices.luminance.abstract import AbstractLuminance


class FakeLuminance(AbstractLuminance):
    """ Fake luminance controlling object. """

    def __init__(self, **options) -> None:
        super(FakeLuminance, self).__init__(**options)

    @property
    def is_connected(self) -> bool:
        """ Returns whether the luminance device has been connected. """
        return True

    def connect(self, **options) -> bool:
        """ Main API for connecting to the luminance device. """
        return True

    def disconnect(self, **options) -> bool:
        """ Main API for disconnecting to the luminance device. """
        return True

    def open(self, channel: int, **options) -> bool:
        """ Opening the luminance device, it returns whether the operation is successfully implemented. """
        return True

    def close(self, **options) -> bool:
        """ Closing the luminance device, it returns whether the operation is successfully implemented. """
        return True

    def set(self, channel: int, intensity: int, wait: float = 0.1, **options) -> bool:
        """ Setting the luminance intensity, it returns whether the operation is successfully implemented. """
        return True
