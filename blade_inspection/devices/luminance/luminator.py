#!/usr/bin/python
# -*- coding: utf-8 -*-

import time
import serial
import typing as t


class FakeLuminance(object):
    """ Fake luminance object. """

    def __init__(self, **options) -> None:
        self._options = options

    def run_cmd(self, cmd: str) -> None:
        """ Send the serialized command to the luminator. """
        pass

    def run_cmds(self, cmds: t.Union[str, t.Iterable]) -> None:
        """ Run a single command or a command list. """
        pass


class Luminator(object):
    """ Luminator controller which controls by sending the serialized signal. """

    def __init__(self, **lum_config) -> None:
        try:
            port = lum_config['port']
            bps = 9600
            timex = 5
            self.ser = serial.Serial(port, bps, timeout=timex)
        except Exception as e:
            self.ser = None
            print("---异常---：", e)
        self.__init_luminator(lum_config.get('setting'))

    def __init_luminator(self, setting: t.Any):
        """ Initialize the luminator. """
        if setting is None:
            return
        else:
            self.run_cmds(setting)

    def run_cmd(self, cmd: str):
        """ Send the serialized command to the luminator. """
        if self.ser is None:
            return
        check_str = self.check_code(cmd)
        send_str = cmd + check_str
        self.ser.write(send_str.encode("gbk"))
        time.sleep(0.1)
        out = self.ser.read()

    def run_cmds(self, cmds: t.Union[str, t.Iterable]):
        """ Run a single command or a command list. """
        if isinstance(cmds, str):
            self.run_cmd(cmds)
        elif isinstance(cmds, t.Iterable):
            for cmd_str in cmds:
                self.run_cmd(cmd_str)

    def use_ch(self, ptime: float = 0.1) -> None:
        if self.ser is None: return
        self.ser.write("$1100014".encode("gbk"))
        time.sleep(ptime)
        out = self.ser.read()
        print("out:", out)
        self.ser.write("$2100017".encode("gbk"))
        time.sleep(ptime)
        out = self.ser.read()
        print("out:", out)

    @staticmethod
    def check_code(serial_str) -> str:
        """ Unknown usage. """
        bytes = bytearray(serial_str, "gbk")
        check_code = bytes[0]
        for i in range(1, len(bytes)):
            check_code = check_code ^ bytes[i]
        str_code = "{:x}".format(check_code)
        return str_code

    def __del__(self) -> None:
        """ Delete the object and release the resources. """
        if self.ser is not None:
            self.ser.close()


if __name__ == "__main__":
    lum_cfg = {
        "port": "COM3",
        "setting": [
            "$31000",
            "$32000",
            "$33000"
        ],
        "doc": [
            "$31000",
            "$32042",
            "$33028"
        ],
        "pn": [
            "$31000",
            "$32042",
            "$33064"
        ]
    }
    lum = Luminator(**lum_cfg)
    time.sleep(1)
    lum.run_cmds(lum_cfg.get("setting"))
    # time.sleep(1)
    # lum.run_cmds(lum_cfg.get("pn"))