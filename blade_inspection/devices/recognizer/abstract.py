#!/usr/bin/python
# -*- coding: utf-8 -*-

import typing as t
from abc import ABC, abstractmethod


class AbstractRecognizer(ABC):
    """ Abstract recognizer object. """

    SUCCESS: int = 0
    ERROR: int = -1
    MODEL_ERROR: int = 1
    CAMERA_ERROR: int = 2
    LUMINANCE_ERROR: int = 3

    def __init__(
        self,
        model_path: str,
        input_shape: t.Iterable[int],
        engine_type: str,
        label_map: t.List[str],
        camera_config: t.Dict[str, t.Any],
        luminance_config: t.Dict[str, t.Any],
        **options
    ):
        # super(AbstractRecognizer, self).__init__(**options)
        self._model_path = model_path
        self._input_shape = input_shape
        self._engine_type = engine_type
        self._label_map = label_map
        self._use_gpu: bool = options.get("use_gpu", False)
        self._camera_config = camera_config
        self._luminance_config = luminance_config
        self._is_model_initialized: bool = False
        self._is_camera_connected: bool = False
        self._is_luminance_connected: bool = False

    @abstractmethod
    def _initialize_model(self, **options) -> bool:
        """ Abstract method for initializing the recognition model. """
        pass

    @abstractmethod
    def _initialize_camera(self, **options) -> bool:
        """ Abstract method for initializing the camera. """
        pass

    @abstractmethod
    def _initialize_luminance(self, **options) -> bool:
        """ Abstract method for initializing the luminance device. """
        pass

    @abstractmethod
    def recognize(self, **options) -> t.Tuple[int, t.Dict[str, t.Any]]:
        """ Abstract function for recognizing the part number. """
        pass

    @abstractmethod
    def identify_furnace_batch_number(self, **options) -> t.Tuple[int, t.Dict[str, t.Any]]:
        """ Main function for applying the part number recognition. """
        pass

    @abstractmethod
    def visual_inspection_one(self, **options) -> t.Tuple[int, t.Dict[str, t.Any]]:
        """ Main function for applying the part number recognition. """
        pass

    @abstractmethod
    def visual_inspection_two(self, **options) -> t.Tuple[int, t.Dict[str, t.Any]]:
        """ Main function for applying the part number recognition. """
        pass

    @abstractmethod
    def visual_inspection_three(self, **options) -> t.Tuple[int, t.Dict[str, t.Any]]:
        """ Main function for applying the part number recognition. """
        pass

    @property
    def info(self, **options) -> t.Optional[t.Dict[str, t.Any]]:
        """ Returns the recognizer object's information dictionary. """
        return {
            "model_path": self._model_path,
            "input_shape": self._input_shape,
            "label_map": self._label_map,
            "camera": self._camera_config,
            "luminance": self._luminance_config
        }

    @property
    def is_connected(self) -> bool:
        """ Returns whether the recognition model is connected or not. """
        return self._is_model_initialized and self._is_camera_connected and self._is_luminance_connected

    def connect(self, **options) -> bool:
        """ Connect to the recognition model, it returns whether the connection is successful. """
        self._is_model_initialized = self._initialize_model(**options)
        self._is_camera_connected = self._initialize_camera(**options)
        self._is_luminance_connected = self._initialize_luminance(**options)
        return self.is_connected

    @abstractmethod
    def disconnect(self, **options) -> bool:
        """ Disconnect to the recognition mode, camera, as well as the luminance device. """
        pass

    @abstractmethod
    def destroy(self, **options) -> bool:
        """ Destroy the recognition mode object, camera object, as well as the luminance device object. """
        pass
