#!/usr/bin/python
# -*- coding: utf-8 -*-

import copy
import numpy as np
import typing as t
from blade_inspection.devices.recognizer.formatters.formatter import Formatter


class _CharTuple(object):
    """ Character result tuple object storing multiple OCR inference results in the same region. The input result is a
        python dictionary should have at least the following format:

        result = {
            "points": [[x0, y0], [x1, y1], [x2, y2], [x3, y3], ...]
            "label": "A",
            "score": 0.973
        }
    """

    def __init__(self, char_result: t.Dict[str, t.Any], **options) -> None:
        self._char_results: t.List[t.Dict[str, t.Any]] = [char_result]
        self._char_results_dict: t.Dict[str, t.Dict[str, t.Any]] = {char_result["label"]: char_result}
        self._top_score: float = char_result["score"]
        self._char_points: t.List[t.List[t.List[float]]] = [char_result["points"]]
        self._char_labels: t.List[str] = [char_result["label"]]
        self._char_scores: t.List[float] = [char_result["score"]]
        self._options = options

    @property
    def char_results(self) -> t.List[t.Dict[str, t.Any]]:
        """ Returns the character result list. """
        return self._char_results

    @property
    def char_points(self) -> t.List[t.List[t.List[float]]]:
        """ Returns the character points list. """
        return self._char_points

    @property
    def char_labels(self) -> t.List[str]:
        """ Returns the character label list. """
        return self._char_labels

    @property
    def char_scores(self) -> t.List[float]:
        """ Returns the character score list. """
        return self._char_scores

    @property
    def top_char_result(self) -> t.Dict[str, t.Any]:
        """ Returns the character result with top score. """
        return self._char_results[0]

    @property
    def top_char_points(self) -> t.List[t.List[float]]:
        """ Returns the top character points for all the cached character results. """
        return self._char_results[0]["points"]

    @property
    def top_char_label(self) -> str:
        """ Returns the top character label for all the cached character results. """
        return self._char_results[0]["label"]

    @property
    def top_char_score(self) -> float:
        """ Returns the top character score for all the cached character results. """
        return self._char_results[0]["score"]

    def get_result_by_label(self, label: str) -> t.Optional[t.Dict[str, t.Any]]:
        """ Get the character result given the label. """
        return self._char_results_dict.get(label)

    def add_char_result(self, char_result: t.Dict[str, t.Any]) -> None:
        """ Add a new result into the tuple. """
        self._char_points.append(char_result["points"])
        self._char_labels.append(char_result["label"])
        self._char_scores.append(char_result["score"])
        self._char_results.append(char_result)
        indices: np.ndarray = np.argsort(np.array(copy.deepcopy(self._char_scores), dtype=np.float32))[::-1]

        arranged_points: t.List[float] = list()
        arranged_labels: t.List[float] = list()
        arranged_scores: t.List[float] = list()
        arranged_results: t.List[t.Dict[str, t.Any]] = list()
        for index in indices:
            arranged_points.append(self._char_results[index]["points"])
            arranged_labels.append(self._char_results[index]["label"])
            arranged_scores.append(self._char_results[index]["score"])
            arranged_results.append(self._char_results[index])
        self._char_results = arranged_results
        self._char_points = arranged_points
        self._char_labels = arranged_labels
        self._char_scores = arranged_scores
        self._char_results_dict[char_result["label"]] = char_result


class CFFormatter(Formatter):
    """ Part number OCR result Formatter built for Chengfa shop part number recognition. It is inherited from the basic
        formatter object. Compared to its parent, it inputs the batch number and the prefix information as the
        indication to modify the raw inference results.

        Please note that if you use CFFormatter or its children formatters, mute the NMS operations for the raw
        inference result post-processing.

        The output result has the following format:
            result = {
                "image": np.ndarray,
                "part_type": str,
                "char_points": list[list[list[float]]],
                "char_labels": list[str],
                "char_scores": list[float],
                "text_points": list[list[list[float]]],
                "text_labels": list[str],
                "text_scores": list[float],
                "arranged_char_points": list[list[list[list[float]]]],
                "arranged_char_labels": list[list[str]],
                "arranged_char_scores": list[list[float]]
                "part_number": str,
                "is_part": bool,
                "is_valid": bool,
                "is_obscure": bool
            }
        """

    def __init__(self, **options) -> None:
        super(CFFormatter, self).__init__(**options)
        self._num_lines: int = options.get("num_lines", 2)
        if self._num_lines not in [1, 2]:
            raise AssertionError("XFFormatter only supports line number equals to 1 or 2.")

        self._lower_thresh: float = options.get("lower_thresh", 0.3)
        self._upper_thresh: float = options.get("upper_thresh", 0.8)
        self._conf_thresh: float = options.get("conf_thresh", 0.8)
        self._iou_thresh: float = options.get("iou_thresh", 0.64)
        self._h_deviate_ratio: float = options.get("h_deviate_ratio", 0.5)
        self._minimum_line_char_num: int = options.get("minimum_line_char_num", 3)

    def format(self, result: t.Dict[str, t.Any], **options) -> t.Dict[str, t.Any]:
        """ Returns the json format OCR result. """
        result: t.Dict[str, t.Any] = self._filter_raw_result(result)
        result: t.Dict[str, t.Any] = super(CFFormatter, self).format(result, **options)
        result: t.Dict[str, t.Any] = self._process_arranged_result(result)
        result: t.Dict[str, t.Any] = self._check_arranged_line_number(result)
        result: t.Dict[str, t.Any] = self._modify_arranged_result(result)
        result.pop("arranged_char_tuples", None)
        return result

    def _filter_raw_result(self, result: t.Dict[str, t.Any]) -> t.Dict[str, t.Any]:
        """ Filter out the raw result from the inference model. This function is built for filtering out the boxes that
            have the large height.
        """
        box_heights: t.List[float] = list()
        char_points: t.List[t.List[t.List[float]]] = result.pop("char_points")
        char_labels: t.List[str] = result.pop("char_labels")
        char_scores: t.List[float] = result.pop("char_scores")
        for char_pts in char_points:
            x_min, y_min, x_max, y_max = Formatter._get_box_from_points(char_pts)
            box_heights.append(y_max - y_min)
        median_h: float = float(np.median(box_heights))

        filtered_char_points: t.List[t.List[t.List[float]]] = list()
        filtered_char_labels: t.List[str] = list()
        filtered_char_scores: t.List[float] = list()
        for i, box_h in enumerate(box_heights):
            if char_labels[i] == "minus" or abs((box_h - median_h) / median_h) <= self._h_deviate_ratio:
                filtered_char_points.append(char_points[i])
                filtered_char_scores.append(char_scores[i])
                char_label: str = char_labels[i]
                if char_label == "minus":
                    char_label = "-"
                elif char_label == "slash":
                    char_label = "/"
                filtered_char_labels.append(char_label)

        result["char_points"] = filtered_char_points
        result["char_labels"] = filtered_char_labels
        result["char_scores"] = filtered_char_scores
        return result

    def _process_arranged_result(self, result: t.Dict[str, t.Any]) -> t.Dict[str, t.Any]:
        """ Process the arranged result. This function will do the following:
            1. Filter out the arranged character lines that are too short;
            2. Check whether the result indicates a valid part;
        """
        is_part: bool = True
        filtered_char_points: t.List[t.List[t.List[float]]] = list()
        filtered_char_labels: t.List[str] = list()
        filtered_char_scores: t.List[float] = list()

        for i in range(len(result["arranged_char_points"])):
            line_char_num: int = len(result["arranged_char_points"][i])
            if line_char_num >= self._minimum_line_char_num:
                filtered_char_points.append(result["arranged_char_points"][i])
                filtered_char_labels.append(result["arranged_char_labels"][i])
                filtered_char_scores.append(result["arranged_char_scores"][i])

        result["arranged_char_points"] = filtered_char_points
        result["arranged_char_labels"] = filtered_char_labels
        result["arranged_char_scores"] = filtered_char_scores

        if len(filtered_char_points) <= 5:
            is_part: bool = False
        result["is_part"] = is_part
        return result

    def _check_arranged_line_number(self, result: t.Dict[str, t.Any]) -> t.Dict[str, t.Any]:
        """ Check the result's validation based on the number of arranged line number. It will stamp the 'is_valid' key
            into the result. Please note that this function may be overwritten by its children classes.
        """
        if len(result["arranged_char_points"]) == self._num_lines:
            is_valid: bool = True
        else:
            is_valid: bool = False
        result["is_valid"] = is_valid
        return result

    def _modify_arranged_result(self, result: t.Dict[str, t.Any]) -> t.Dict[str, t.Any]:
        """ Modify the formatted result given the batch number and the prefix string list. """
        result: t.Dict[str, t.Any] = self._cluster_result(result)
        result: t.Dict[str, t.Any] = self._modify_part_number(result)
        result: t.Dict[str, t.Any] = CFFormatter._form_modified_part_number(result)
        return result

    def _modify_part_number(self, result: t.Dict[str, t.Any]) -> t.Dict[str, t.Any]:
        """ Modify part result, which also all the key 'is_obscure' inside the input result. """
        is_obscure: bool = False
        arranged_char_tuples: t.List[t.List[_CharTuple]] = result["arranged_char_tuples"]
        if len(arranged_char_tuples) == 0 or len(arranged_char_tuples[-1]) < 3:
            result["is_obscure"] = True
            return result
        if arranged_char_tuples[-1][0].char_labels[0] == "-":
            arranged_char_tuples[-1].pop(0)
        if arranged_char_tuples[-1][-1].char_labels[0] == "-":
            arranged_char_tuples[-1].pop(-1)

        num_minus, num_slash = 0, 0
        for char_tuple in arranged_char_tuples[-1]:
            if char_tuple.char_labels[0] == "/":
                num_slash += 1
            if char_tuple.char_labels[0] == "-":
                if char_tuple.char_scores[0] < 0.6:
                    is_obscure = True
                num_minus += 1
            elif char_tuple.char_scores[0] < self._conf_thresh:
                is_obscure = True
            elif len(char_tuple.char_scores) > 1:
                if char_tuple.char_scores[0] < self._upper_thresh or char_tuple.char_scores[1] > self._lower_thresh:
                    is_obscure = True

        if num_minus != 1 or num_slash > 1:
            is_obscure = True
        result["is_obscure"] = is_obscure
        return result

    @staticmethod
    def _form_modified_part_number(result: t.Dict[str, t.Any]) -> t.Dict[str, t.Any]:
        """ Form the modified part number based on the modified character tuples. """
        part_number: str = ""
        if len(result["arranged_char_tuples"]):
            for char_tuple in result["arranged_char_tuples"][-1]:
                part_number += char_tuple.char_labels[0]
        result["part_number"] = part_number
        return result

    def _cluster_result(self, result: t.Dict[str, t.Any]) -> t.Dict[str, t.Any]:
        """ Cluster the inference result based on the neighborhood character's IoU."""
        arranged_char_points: t.List[t.List[t.List[t.List[float]]]] = result["arranged_char_points"]
        arranged_char_labels: t.List[t.List[str]] = result["arranged_char_labels"]
        arranged_char_scores: t.List[t.List[str]] = result["arranged_char_scores"]
        arranged_char_tuples: t.List[t.List[_CharTuple]] = list()

        for char_points_line, char_labels_line, char_scores_line in zip(arranged_char_points, arranged_char_labels,
                                                                        arranged_char_scores):
            anchor_char_result: t.Optional[t.Dict[str, t.Any]] = None
            arranged_char_tuples.append(list())
            for char_points, char_label, char_score in zip(char_points_line, char_labels_line, char_scores_line):
                cur_result: t.Dict[str, t.Any] = {"points": char_points, "label": char_label, "score": char_score}
                if anchor_char_result is None:
                    arranged_char_tuples[-1].append(_CharTuple(cur_result))
                    anchor_char_result = cur_result
                    continue

                cur_box: t.Tuple[int, int, int, int] = CFFormatter._get_roi_from_points(char_points)
                anchor_box: t.Tuple[int, int, int, int] = CFFormatter._get_roi_from_points(
                    anchor_char_result["points"])
                if CFFormatter._box_iou(cur_box, anchor_box) >= self._iou_thresh:
                    arranged_char_tuples[-1][-1].add_char_result(cur_result)
                else:
                    arranged_char_tuples[-1].append(_CharTuple(cur_result))
                    anchor_char_result = cur_result

        result["arranged_char_tuples"] = arranged_char_tuples
        return result

    @staticmethod
    def _box_iou(box1: t.Iterable[float], box2: t.Iterable[float]) -> float:
        """ Return the IoU value between two input boxes. """
        x_min_1, y_min_1, x_max_1, y_max_1 = box1
        x_min_2, y_min_2, x_max_2, y_max_2 = box2

        inter_x1 = max(x_min_1, x_min_2)
        inter_x2 = min(x_max_1, x_max_2)
        inter_y1 = max(y_min_1, y_min_2)
        inter_y2 = min(y_max_1, y_max_2)

        if inter_x1 >= inter_x2 or inter_y1 >= inter_y2:
            return 0.0
        inter_area = (inter_x2 - inter_x1) * (inter_y2 - inter_y1)
        area = (x_max_1 - x_min_1) * (y_max_1 - y_min_1) + (x_max_2 - x_min_2) * (y_max_2 - y_min_2)
        iou = inter_area / (area - inter_area)
        return iou

    @staticmethod
    def _get_roi_from_points(points: t.List[t.List[float]], is_copy: bool = False) -> t.Tuple[int, int, int, int]:
        """ Get the ROI from the given list of points. """
        if is_copy:
            points = np.array(copy.deepcopy(points), dtype=np.float32)
        else:
            points = np.array(points, dtype=np.float32)

        x_min: int = int(np.min(points[:, 0]))
        y_min: int = int(np.min(points[:, 1]))
        x_max: int = int(np.max(points[:, 0]))
        y_max: int = int(np.max(points[:, 1]))
        return x_min, y_min, x_max, y_max
