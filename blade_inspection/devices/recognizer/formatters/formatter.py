#!/usr/bin/python
# -*- coding: utf-8 -*-

import copy
import numpy as np
import typing as t
from blade_inspection.devices.recognizer.formatters.abstract import AbstractFormatter


class Formatter(AbstractFormatter):
    """ Part number OCR result Formatter, which formats the input result into the following format:

        result = {
            "image": np.ndarray,
            "part_type": str,
            "char_points": list[list[list[float]]],
            "char_labels": list[str],
            "char_scores": list[float],
            "text_points": list[list[list[float]]],
            "text_labels": list[str],
            "text_scores": list[float],
            "arranged_char_points": list[list[list[list[float]]]],
            "arranged_char_labels": list[list[str]],
            "arranged_char_scores": list[list[float]]
            "part_number": str
        }
    """

    def __init__(self, **options) -> None:
        super(Formatter, self).__init__(**options)
        self._image: t.Optional[np.ndarray] = None
        self._part_type: t.Optional[str] = None
        self._char_points: t.Optional[t.List[t.List[t.List[float]]]] = None
        self._char_labels: t.Optional[t.List[str]] = None
        self._char_scores: t.Optional[t.List[float]] = None
        self._text_points: t.Optional[t.List[t.List[t.List[float]]]] = None
        self._text_labels: t.Optional[t.List[str]] = None
        self._text_scores: t.Optional[t.List[float]] = None
        self._part_number: t.Optional[str] = None
        self._arranged_char_points: t.Optional[t.List] = None
        self._arranged_char_labels: t.Optional[t.List] = None
        self._arranged_char_scores: t.Optional[t.List] = None

        self._delimiter: str = options.get("delimiter", "\n")
        self._options = options

    def format(self, result: t.Dict[str, t.Any], **options) -> t.Dict[str, t.Any]:
        """ Returns the json format OCR result. """
        self._image = result.get("image")
        self._part_type = result.get("part_type")
        self._char_points: t.List[t.List[t.List[float]]] = result.get("char_points")
        self._char_labels: t.List[str] = result.get("char_labels")
        self._char_scores: t.List[float] = result.get("char_scores")
        self._text_points: t.List[t.List[t.List[float]]] = result.get("text_points") or list()
        self._text_labels: t.List[str] = result.get("text_labels") or list()
        self._text_scores: t.List[float] = result.get("text_scores") or list()
        self._part_number: str = ""
        self._arranged_char_points: t.List = list()
        self._arranged_char_labels: t.List = list()
        self._arranged_char_scores: t.List = list()
        self._combine()

        result = {
            "image": self._image,
            "part_type": self._part_type,
            "char_points": self._char_points,
            "char_labels": self._char_labels,
            "char_scores": self._char_scores,
            "text_points": self._text_points,
            "text_labels": self._text_labels,
            "text_scores": self._text_scores,
            "part_number": self._part_number,
            "arranged_char_points": self._arranged_char_points,
            "arranged_char_labels": self._arranged_char_labels,
            "arranged_char_scores": self._arranged_char_scores
        }

        return result

    def _combine(self) -> None:
        """ Form the OCR character-wised result into scene-text-wised result. """
        if len(self._text_labels):
            self._rearrange_scene_text()
        elif len(self._char_labels) == 0:
            return
        elif len(self._char_labels) == 1:
            self._text_points = self._char_points
            self._text_labels = self._char_labels
            self._text_scores = self._char_scores
            self._rearrange_scene_text()
        else:
            self._aggregate_result_by_line()
        self._form_part_number()

    def _aggregate_result_by_line(self) -> None:
        """ Aggregate the character-wised OCR result by line from top to bottom,
            and then rearranged from left to right.
        """
        self._text_points.clear()
        self._text_labels.clear()
        self._text_scores.clear()
        self._arranged_char_points: t.List[t.List[t.List[t.List[float]]]] = list()
        self._arranged_char_labels: t.List[t.List[str]] = list()
        self._arranged_char_scores: t.List[t.List[float]] = list()

        x_centers: t.List[t.List[float]] = list()
        temp_points: t.List[t.List[t.List[t.List[float]]]] = list()
        temp_labels: t.List[t.List[str]] = list()
        temp_scores: t.List[t.List[float]] = list()
        y_center_lib: t.List[float] = list()
        origin_points = copy.deepcopy(self._char_points)
        origin_labels = copy.deepcopy(self._char_labels)
        origin_scores = copy.deepcopy(self._char_scores)
        avg_pt_height = None

        def get_points_center(pts: t.List[t.List[float]]) -> t.Optional[t.Tuple[float, float]]:
            num_pts = len(pts)
            if num_pts == 0:
                return
            pts = np.array(pts)
            x_ctr = np.sum(pts[:, 0]) / num_pts
            y_ctr = np.sum(pts[:, 1]) / num_pts
            pts.tolist()

            return x_ctr, y_ctr

        def get_points_height(pts: t.List[t.List[float]]) -> t.Optional[float]:
            if len(pts) == 0:
                return
            pts = np.array(pts)
            ht = float(np.max(pts[:, 1]) - np.min(pts[:, 1]))
            pts.tolist()

            return ht

        def create_new_line(x: float, y: float, pt: t.List[t.List[float]], lb: str, s: float) -> None:
            """ Create a new aggregated line. """
            y_center_lib.append(y)
            x_centers.append([x])
            temp_points.append([pt])
            temp_labels.append([lb])
            temp_scores.append([s])

        for points, label, score in zip(origin_points, origin_labels, origin_scores):
            pt_height = get_points_height(points)
            x_center, y_center = get_points_center(points)

            # Check whether this character belongs to a line
            if not len(y_center_lib):
                avg_pt_height = pt_height
                create_new_line(x_center, y_center, points, label, score)
            else:
                is_matched = False
                avg_pt_height = 0.8 * avg_pt_height + 0.2 * pt_height

                for i, y_gauge in enumerate(y_center_lib):
                    if abs(y_gauge - y_center) <= 0.8 * avg_pt_height:
                        is_matched = True
                        x_centers[i].append(x_center)
                        temp_points[i].append(points)
                        temp_labels[i].append(label)
                        temp_scores[i].append(score)
                        y_gauge = 0.8 * y_gauge + 0.2 * y_center
                        y_center_lib[i] = y_gauge
                        break
                if not is_matched:
                    create_new_line(x_center, y_center, points, label, score)

        # Shift the line index from top to bottom
        x_centers_y_arranged: t.List[t.List[float]] = list()
        points_y_arranged: t.List[t.List[t.List[t.List[float]]]] = list()
        labels_y_arranged: t.List[t.List[str]] = list()
        scores_y_arranged: t.List[t.List[float]] = list()

        y_sort_indices = np.argsort(y_center_lib)
        for y_index in y_sort_indices:
            x_centers_y_arranged.append(x_centers[y_index])
            points_y_arranged.append(temp_points[y_index])
            labels_y_arranged.append(temp_labels[y_index])
            scores_y_arranged.append(temp_scores[y_index])

        # Shift the item index from left to right
        for line_index in range(len(x_centers_y_arranged)):
            x_centers_line = x_centers_y_arranged[line_index]
            boxes_line = points_y_arranged[line_index]
            labels_line = labels_y_arranged[line_index]
            scores_line = scores_y_arranged[line_index]

            x_sort_indices = np.argsort(x_centers_line)
            self._arranged_char_points.append(list())
            self._arranged_char_labels.append(list())
            self._arranged_char_scores.append(list())

            for x_index in x_sort_indices:
                self._arranged_char_points[-1].append(boxes_line[x_index])
                self._arranged_char_labels[-1].append(labels_line[x_index])
                self._arranged_char_scores[-1].append(scores_line[x_index])

        # Get the scene text ROI points, labels, and scores
        for line_points, line_labels, line_scores in zip(self._arranged_char_points, self._arranged_char_labels,
                                                         self._arranged_char_scores):
            left_x_min, left_y_min, _, left_y_max = Formatter._get_box_from_points(line_points[0])
            _, right_y_min, right_x_max, right_y_max = Formatter._get_box_from_points(line_points[-1])

            label = ""
            for line_label in line_labels:
                label += line_label
            score = sum(line_scores) / len(line_scores)

            self._text_points.append([[left_x_min, left_y_min], [right_x_max, right_y_min],
                                       [right_x_max, right_y_max], [left_x_min, left_y_max]])
            self._text_labels.append(label)
            self._text_scores.append(score)

    def _rearrange_scene_text(self) -> None:
        """ Rearrange the scene text result from top to bottom. """
        ys: t.List[float] = list()
        for points in self._text_points:
            pts_array = copy.deepcopy(points)
            pts_array = np.array(pts_array)
            ys.append(np.min(pts_array[:, 1]))
        y_indices = np.argsort(ys)

        text_points: t.List[t.List[t.List[float]]] = list()
        text_labels: t.List[str] = list()
        text_scores: t.List[float] = list()
        for y_index in y_indices:
            text_points.append(self._text_points[y_index])
            text_labels.append(self._text_labels[y_index])
            text_scores.append(self._text_scores[y_index])
        self._text_points = text_points
        self._text_labels = text_labels
        self._text_scores = text_scores

    def _form_part_number(self) -> None:
        if (not self._text_labels) or (not len(self._text_labels)):
            return
        for text_label in self._text_labels:
            self._part_number += text_label + self._delimiter
        if self._delimiter != "":
            self._part_number = self._part_number[:len(self._part_number)-1]

    @staticmethod
    def _get_box_from_points(points: t.List[t.List[float]]) -> t.List[float]:
        """ Get the ROI x_min, x_max, y_min, y_max from the input points. """
        points = np.array(copy.deepcopy(points), dtype=np.float32)
        x_min = np.min(points[:, 0])
        x_max = np.max(points[:, 0])
        y_min = np.min(points[:, 1])
        y_max = np.max(points[:, 1])
        points.tolist()

        return [x_min, y_min, x_max, y_max]
