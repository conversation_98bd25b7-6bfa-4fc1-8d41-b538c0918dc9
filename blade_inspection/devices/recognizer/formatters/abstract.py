#!/usr/bin/python
# -*- coding: utf-8 -*-

import typing as t
from abc import abstractmethod


class AbstractFormatter(object):
    """ Abstract OCR result combiner, which is used to combine the raw character-wised or scene-text-wised OCR result
        into a full part number. Note that the OCR result should come from one image which only contains a SINGLE part.
    """

    def __init__(self, **options) -> None:
        self._part_type: t.Optional[str] = None
        self._options = options

    @property
    def part_type(self) -> str:
        """ Returns the part type name. """
        return self._part_type

    @abstractmethod
    def format(self, result: t.Dict[str, t.Any], **options) -> None:
        """ Abstract part number recognition result formatting method. """
        pass
