#!/usr/bin/python
# -*- coding: utf-8 -*-

import os
import cv2
import time
import copy
import glob as gb
import typing as t
import numpy as np
from blade_inspection.devices.recognizer.abstract import AbstractRecognizer
from blade_inspection.devices.luminance import GeneralLuminance


class FakeRecognizer(AbstractRecognizer):
    """ Fake recognizer object. """

    def __init__(
        self,
        model_path: str,
        input_shape: t.Iterable[int],
        engine_type: str,
        label_map: t.List[str],
        camera_config: t.Dict[str, t.Any],
        luminance_config: t.Dict[str, t.Any],
        **options
    ):
        super(FakeRecognizer, self).__init__(model_path=model_path, input_shape=input_shape, engine_type=engine_type,
                                             label_map=label_map, camera_config=camera_config,
                                             luminance_config=luminance_config,  **options)

        self._part_info_list: t.List[t.Dict[str, t.Any]] = [
            {
                "part_number": "221002X-02",
                "is_obscure": False,
                "is_part": True,
                "is_valid": True
            },
            {
                "part_number": "230315X-24",
                "is_obscure": False,
                "is_part": True,
                "is_valid": True
            },
            {
                "part_number": "230313X-25",
                "is_obscure": False,
                "is_part": True,
                "is_valid": True
            },
            {
                "part_number": "230405X-01",
                "is_obscure": False,
                "is_part": True,
                "is_valid": True
            },
            {
                "part_number": "230311X-24",
                "is_obscure": False,
                "is_part": True,
                "is_valid": True
            },
            {
                "part_number": "230323X-19",
                "is_obscure": False,
                "is_part": True,
                "is_valid": True
            },
            {
                "part_number": "230325/S-05",
                "is_obscure": False,
                "is_part": True,
                "is_valid": True
            },
            {
                "part_number": "230720-26",
                "is_obscure": True,
                "is_part": True,
                "is_valid": True
            },
            {
                "part_number": "230304X-14",
                "is_obscure": False,
                "is_part": True,
                "is_valid": True
            },
            {
                "part_number": "230301X-19",
                "is_obscure": False,
                "is_part": True,
                "is_valid": True
            },
            {
                "part_number": "230311X-22",
                "is_obscure": False,
                "is_part": True,
                "is_valid": True
            },
            {
                "part_number": "230311X-27",
                "is_obscure": False,
                "is_part": True,
                "is_valid": True
            },
            {
                "part_number": "230405X-31",
                "is_obscure": False,
                "is_part": True,
                "is_valid": True
            },
            {
                "part_number": "270303X-18",
                "is_obscure": True,
                "is_part": True,
                "is_valid": True
            },
            {
                "part_number": "230402X-21",
                "is_obscure": False,
                "is_part": True,
                "is_valid": True
            },
            {
                "part_number": "230601X-02",
                "is_obscure": False,
                "is_part": True,
                "is_valid": True
            },
            {
                "part_number": "230311X-23",
                "is_obscure": False,
                "is_part": True,
                "is_valid": True
            },
            {
                "part_number": "230325/S-02",
                "is_obscure": False,
                "is_part": True,
                "is_valid": True
            },
            {
                "part_number": "230S22-20",
                "is_obscure": True,
                "is_part": True,
                "is_valid": True
            },
            {
                "part_number": "230325/S-08",
                "is_obscure": False,
                "is_part": True,
                "is_valid": True
            }
        ]

        self._cursor: int = 0
        root_path: str = os.path.abspath(os.path.join(os.path.dirname(__file__), "../../.."))
        image_path: str = os.path.join(root_path, "static/images/demo")
        self._image_files: t.List[str] = gb.glob(image_path + r"/*.png")
        if len(self._image_files) == 0:
            raise AssertionError("Cannot find any image file at", image_path)

    def recognize(self, **options) -> t.Tuple[int, t.Dict[str, t.Any]]:
        """ Main function for applying the part number recognition. """
        part_type: str = options.get("part_type", "")
        intensities: t.List[int] = self._luminance_config.get("intensities", dict()).get(part_type, list())
        for c, intensity in enumerate(intensities):
            self._luminance.set(c + 1, intensity)
        time.sleep(self._luminance_config.get("wait", 0.2))

        image: np.ndarray = cv2.imread(self._image_files[self._cursor], -1)
        self._cursor += 1
        if self._cursor >= len(self._image_files):
            self._cursor = 0

        self._luminance.close()
        result: t.Dict[str, t.Any] = copy.deepcopy(self._part_info_list[self._cursor])
        result["image"] = image
        return FakeRecognizer.SUCCESS, result

    def identify_furnace_batch_number(self, **options) -> t.Tuple[int, t.Dict[str, t.Any]]:
        """ Main function for applying the part number recognition. """
        return FakeRecognizer.SUCCESS, {"result": None}

    def visual_inspection_one(self, **options) -> t.Tuple[int, t.Dict[str, t.Any]]:
        """ Main function for applying the part number recognition. """
        return FakeRecognizer.SUCCESS, {"result": None}

    def visual_inspection_two(self, **options) -> t.Tuple[int, t.Dict[str, t.Any]]:
        """ Main function for applying the part number recognition. """
        return FakeRecognizer.SUCCESS, {"result": None}

    def visual_inspection_three(self, **options) -> t.Tuple[int, t.Dict[str, t.Any]]:
        """ Main function for applying the part number recognition. """
        return FakeRecognizer.SUCCESS, {"result": None}

    def _initialize_model(self, **options) -> bool:
        """ Main function for initializing the recognition model. """
        return True

    def _initialize_camera(self, **options) -> bool:
        """ Main function for initializing the camera. """
        return True

    def _initialize_luminance(self, **options) -> bool:
        """ Main function for initializing the luminance device. """
        self._luminance: GeneralLuminance = GeneralLuminance(**self._luminance_config)
        return self._luminance.connect()

    def disconnect(self, **options) -> bool:
        """ Disconnect to the recognition mode, camera, as well as the luminance device. """
        is_luminance_disconnected: bool = self._luminance.disconnect()
        return is_luminance_disconnected

    def destroy(self, **options) -> bool:
        """ Destroy the recognition mode object, camera object, as well as the luminance device object. """
        return True
