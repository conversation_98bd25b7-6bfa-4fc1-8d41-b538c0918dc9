#!/usr/bin/python
# -*- coding: utf-8 -*-

import os
import time
import numpy as np
import typing as t
from hd_robotics.devices.cameras import AbstractCamera, GXCamera
from blade_inspection.devices.cuda_models import DetModel
from blade_inspection.devices.luminance import AbstractLuminance, GeneralLuminance
from blade_inspection.devices.recognizer.abstract import AbstractR<PERSON>ognizer
from blade_inspection.devices.recognizer.formatters import CFFormatter


class GeneralRecognizer(AbstractRecognizer):
    """ General recognizer object. """

    _root_path: str = os.path.abspath(os.path.join(os.path.dirname(__file__), "../../.."))
    _model_root_path: str = os.path.join(_root_path, "static/cuda_models")

    def __init__(
        self,
        model_path: str,
        input_shape: t.Iterable[int],
        engine_type: str,
        label_map: t.List[str],
        camera_config: t.Dict[str, t.Any],
        luminance_config: t.Dict[str, t.Any],
        **options
    ):
        super(GeneralR<PERSON>ognizer, self).__init__(model_path=model_path, input_shape=input_shape, engine_type=engine_type,
                                                label_map=label_map, camera_config=camera_config,
                                                luminance_config=luminance_config, **options)
        self._camera: t.Optional[AbstractCamera] = None
        self._luminance: t.Optional[AbstractLuminance] = None
        self._model: t.Optional[DetModel] = None
        self._rois: t.Optional[t.Dict[str, t.List[int]]] = options.get("rois")
        self._formatter = CFFormatter(part_type="level")

    def recognize(self, **options) -> t.Tuple[int, t.Dict[str, t.Any]]:
        """ Main function for applying the part number recognition. """
        part_type: str = options.get("part_type", "")
        intensities: t.List[int] = self._luminance_config.get("intensities", dict()).get(part_type, list())
        for c, intensity in enumerate(intensities):
            self._luminance.set(c + 1, intensity)
        time.sleep(self._luminance_config.get("wait", 0.2))

        image: t.Optional[np.ndarray] = self._camera.get_image()
        self._luminance.close()
        if image is None:
            return GeneralRecognizer.ERROR, dict()
        elif self._rois is not None and part_type in self._rois:
            x_min, y_min, x_max, y_max = self._rois[part_type]
            image = image[y_min:y_max, x_min:x_max]
        result: t.Dict[str, t.Any] = self._model.infer(origin=image)
        result: t.Dict[str, t.Any] = self._arrange_model_result(image=image, result=result)
        result: t.Dict[str, t.Any] = self._formatter.format(result=result)
        return GeneralRecognizer.SUCCESS, result

    def identify_furnace_batch_number(self, **options) -> t.Tuple[int, t.Dict[str, t.Any]]:
        """ Main function for applying the part number recognition. """
        return GeneralRecognizer.SUCCESS, {"result": None}

    def visual_inspection_one(self, **options) -> t.Tuple[int, t.Dict[str, t.Any]]:
        """ Main function for applying the part number recognition. """
        return GeneralRecognizer.SUCCESS, {"result": None}

    def visual_inspection_two(self, **options) -> t.Tuple[int, t.Dict[str, t.Any]]:
        """ Main function for applying the part number recognition. """
        return GeneralRecognizer.SUCCESS, {"result": None}

    def visual_inspection_three(self, **options) -> t.Tuple[int, t.Dict[str, t.Any]]:
        """ Main function for applying the part number recognition. """
        return GeneralRecognizer.SUCCESS, {"result": None}

    def _initialize_model(self, **options) -> bool:
        """ Main function for initializing the recognition model. """
        abs_model_path: str = os.path.join(GeneralRecognizer._model_root_path, self._model_path)
        self._model: DetModel = DetModel(model_path=abs_model_path, input_shape=self._input_shape,
                                         engine_type=self._engine_type, use_gpu=self._use_gpu)
        return True

    def _initialize_camera(self, **options) -> bool:
        """ Main function for initializing the camera. """
        self._camera: GXCamera = GXCamera(**self._camera_config)
        return self._camera.connect()

    def _initialize_luminance(self, **options) -> bool:
        """ Main function for initializing the luminance device. """
        self._luminance: GeneralLuminance = GeneralLuminance(**self._luminance_config)
        return self._luminance.connect()

    def _arrange_model_result(self, image:  np.ndarray, result: t.Dict[str, t.Any]) -> t.Dict[str, t.Any]:
        """ Arrange the input raw result into the format that can be further processed by the formatter. """
        result["image"] = image
        boxes: t.List[t.List[int]] = result.pop("boxes", list())
        labels: t.List[int] = result.pop("labels", list())
        scores: t.List[float] = result.pop("scores", list())

        char_points: t.List[t.List[t.List[int]]] = list()
        char_labels: t.List[str] = list()
        char_scores: t.List[float] = scores

        for box, index in zip(boxes, labels):
            char_label: str = self._label_map[index]
            x_min, y_min, x_max, y_max = box
            char_labels.append(char_label)
            char_points.append([[x_min, y_min], [x_max, y_min], [x_max, y_max], [x_min, y_max]])

        result["char_points"] = char_points
        result["char_labels"] = char_labels
        result["char_scores"] = char_scores
        return result

    def disconnect(self, **options) -> bool:
        """ Disconnect to the recognition mode, camera, as well as the luminance device. """
        is_camera_connected: bool = self._camera.release()
        is_luminance_disconnected: bool = self._luminance.disconnect()
        return is_camera_connected and is_luminance_disconnected

    def destroy(self, **options) -> bool:
        """ Destroy the recognition mode object, camera object, as well as the luminance device object. """
        self._camera = None
        self._luminance = None
        self._model = None
        return True