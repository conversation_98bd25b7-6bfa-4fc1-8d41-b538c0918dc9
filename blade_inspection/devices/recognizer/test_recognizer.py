#!/usr/bin/python
# -*- coding: utf-8 -*-

import os
import cv2
import random
import typing as t
import numpy as np
import glob as gb
from blade_inspection.devices.recognizer.abstract import AbstractRecognizer


class TestRecognizer(AbstractRecognizer):
    """ Fake recognizer object. """

    def __init__(
        self,
        model_path: str,
        input_shape: t.Iterable[int],
        engine_type: str,
        label_map: t.List[str],
        camera_config: t.Dict[str, t.Any],
        luminance_config: t.Dict[str, t.Any],
        **options
    ):
        super(TestRecognizer, self).__init__(model_path=model_path, input_shape=input_shape, engine_type=engine_type,
                                             label_map=label_map, camera_config=camera_config,
                                             luminance_config=luminance_config,  **options)
        self._is_connected = None
        self._image_cursor: int = 0
        self._images: t.List[np.ndarray] = list()
        self._image_suffix: str = options.get("image_suffix", ".png")

        _abs_path: str = os.path.abspath(os.path.join(os.path.dirname(__file__), "../../.."))
        _src_path: str = os.path.join(_abs_path, "static/images/rec")
        image_files: t.List[str] = gb.glob(_src_path + r"/*.png")
        if len(image_files) == 0:
            raise AssertionError("Could not find image file inside", _src_path)
        for image_file in image_files:
            image: np.ndarray = cv2.imread(image_file)
            self._images.append(image)

    def _initialize_model(self, **options) -> bool:
        """ Main method for initializing the recognition model. """
        return True

    def _initialize_camera(self, **options) -> bool:
        """ Main method for initializing the camera. """
        return True

    def _initialize_luminance(self, **options) -> bool:
        """ Main method for initializing the luminance device. """
        return True

    def recognize(self, **options) -> t.Tuple[int, t.Any]:
        """ Main function for printing the information. """
        part_number: str = str(random.randint(10**9, 10**10-1))
        possibility: float = random.uniform(0.0, 1.0)
        image: np.ndarray = self._images[self._image_cursor]
        self._image_cursor += 1
        if self._image_cursor == len(self._images):
            self._image_cursor = 0
        if possibility >= 0.95:
            is_obscure: bool = True
        else:
            is_obscure: bool = False
        result: t.Dict[str, t.Any] = {
            "image": image,
            "part_number": part_number,
            "is_part": True,
            "is_valid": True,
            "is_obscure": is_obscure
        }
        return AbstractRecognizer.SUCCESS, result

    @property
    def is_connected(self) -> bool:
        """ Returns whether the jet printer has been connected. """
        return self._is_connected

    def connect(self, **options) -> bool:
        """ Main function for connecting the jet printer. """
        self._is_connected = True
        return self._is_connected

    def disconnect(self, **options) -> bool:
        """ Main function for disconnecting to the jet printer. """
        self._is_connected = False
        return self._is_connected

    def destroy(self, **options) -> bool:
        """ Main function for destroying the jet printer object. """
        return self.disconnect(**options)
