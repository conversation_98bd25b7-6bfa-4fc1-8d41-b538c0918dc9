# 带有自定义参数的配置文件示例
# 这个文件展示了如何向RobotNode传递自定义参数

INSPECT:
  cache_path: "D:/blade_inspect"

NODES:
  # 机器人节点配置 - 包含自定义参数
  - node_name: "robot_1"
    endpoint: "localhost"
    namespace: "ns"
    node_class: "RobotNode"
    robot_class: "FakeRobot"
    description: "本地机器人通讯节点"
    
    # 基本网络参数
    ip_addr: "*************"
    port: 502
    timeout: 5.0
    retry_count: 3
    connection_type: "tcp"
    
    # 运动控制参数
    rapid_rate: 0.8
    validate_rapid_rate: 0.5
    max_speed: 30
    
    # 夹爪控制参数
    gripper_delay: 1.0
    alt_gripper_delay: 2.0
    
    # 工具坐标系参数 [x, y, z, rx, ry, rz]
    tool_coordinate:
      - 0.0    # X偏移 (mm)
      - 0.0    # Y偏移 (mm)
      - 0.0    # Z偏移 (mm)
      - 0.0    # RX旋转 (弧度)
      - 0.0    # RY旋转 (弧度)
      - 0.0    # RZ旋转 (弧度)
    
    # ========== 自定义参数示例 ==========
    
    # 安全参数
    collision_detection: true
    force_limit: 100.0
    torque_limit: 50.0
    position_tolerance: 0.5
    orientation_tolerance: 0.05
    
    # 性能参数
    acceleration_limit: 1000.0
    jerk_limit: 5000.0
    smoothing_factor: 0.8
    
    # 工作区域参数
    workspace_limits:
      x_min: -1000.0
      x_max: 1000.0
      y_min: -1000.0
      y_max: 1000.0
      z_min: 0.0
      z_max: 1500.0
    
    # 校准参数
    calibration_offset:
      - 0.1    # X校准偏移
      - 0.2    # Y校准偏移
      - 0.3    # Z校准偏移
      - 0.01   # RX校准偏移
      - 0.02   # RY校准偏移
      - 0.03   # RZ校准偏移
    
    # 自定义业务参数
    custom_params:
      inspection_mode: "high_precision"
      auto_calibration: true
      error_recovery: true
      log_level: "INFO"
      data_collection: true
    
    # 扩展功能参数
    extensions:
      vision_guidance: true
      force_control: false
      path_planning: "advanced"
      collision_avoidance: true
    
    # 维护参数
    maintenance:
      auto_lubrication: false
      temperature_monitoring: true
      vibration_monitoring: true
      usage_tracking: true

  # PLC节点配置
  - node_name: "plc_1"
    endpoint: "localhost"
    namespace: "ns"
    node_class: "PLCNode"
    plc_class: "FakePLC"
    description: "本地总控PLC通讯节点"
    ip_addr: "*************"
    con_type: 1

  # 视觉识别节点配置
  - node_name: "visual_1"
    endpoint: "localhost"
    namespace: "ns"
    node_class: "VisualRecognizerNode"
    recognizer_class: "GeneralRecognizer"
    description: "视觉识别节点"
    model_path: "detection_model.engine"
    input_shape: [640, 640, 3]
    engine_type: "paddle"

# ========== 使用说明 ==========
#
# 1. 参数传递方式：
#    - 所有在NODES配置中的参数都会传递给对应的节点构造函数
#    - 参数通过**options的方式传递，可以在节点的__init__方法中接收
#
# 2. 参数访问方式：
#    - 在节点类中：self._custom_param = options.get('custom_param', default_value)
#    - 在外部代码中：robot_node._custom_param 或通过配置对象访问
#
# 3. 参数类型支持：
#    - 字符串：string_param: "value"
#    - 数字：numeric_param: 123
#    - 布尔值：boolean_param: true/false
#    - 列表：list_param: [1, 2, 3]
#    - 字典：dict_param: {key: value}
#
# 4. 参数命名建议：
#    - 使用下划线分隔：my_custom_param
#    - 避免与系统参数冲突
#    - 使用有意义的名称
#
# 5. 参数验证：
#    - 在节点的__init__方法中添加参数验证逻辑
#    - 设置合理的默认值
#    - 记录参数使用情况
#
# ========== 示例代码 ==========
#
# 在RobotNode子类中使用自定义参数：
#
# class CustomRobotNode(RobotNode):
#     def __init__(self, node_name, endpoint, namespace, **options):
#         super().__init__(node_name, endpoint, namespace, **options)
#         
#         # 获取自定义参数
#         self._collision_detection = options.get('collision_detection', False)
#         self._force_limit = options.get('force_limit', 100.0)
#         self._custom_params = options.get('custom_params', {})
#         
#         # 参数验证
#         if self._force_limit <= 0:
#             raise ValueError("force_limit must be positive")
#
# 在外部代码中访问参数：
#
# robot_node = get_robot_node("robot_1")
# if hasattr(robot_node, '_collision_detection'):
#     print(f"碰撞检测: {robot_node._collision_detection}")
