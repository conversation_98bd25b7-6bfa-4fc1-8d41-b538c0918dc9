#!/usr/bin/python
# -*- coding: utf-8 -*-

"""
测试Robotics实例的可用方法

这个脚本用于检查Robotics实例有哪些方法可以用来获取RobotNode
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from hdmtv.core.cdi import get_instance
from blade_inspection.core.inspect_service import InspectService


def test_robotics_methods():
    """测试Robotics实例的方法"""
    print("=== 测试Robotics实例的方法 ===")
    
    try:
        # 获取InspectService实例
        inspect_service = get_instance(clazz=InspectService)
        robotics = inspect_service._robotics
        
        print(f"✅ 成功获取Robotics实例: {type(robotics).__name__}")
        print(f"   实例ID: {id(robotics)}")
        
        # 检查所有可用的方法和属性
        print("\n🔍 检查可用的方法和属性:")
        all_attrs = dir(robotics)
        
        # 过滤出可能与节点相关的方法
        node_related_methods = []
        for attr in all_attrs:
            if not attr.startswith('__'):
                if 'node' in attr.lower() or 'get' in attr.lower():
                    node_related_methods.append(attr)
        
        print("   可能与节点相关的方法:")
        for method in node_related_methods:
            try:
                obj = getattr(robotics, method)
                if callable(obj):
                    print(f"     {method}() - 方法")
                else:
                    print(f"     {method} - 属性")
            except:
                print(f"     {method} - 无法访问")
        
        # 检查特定的方法
        print("\n🎯 检查特定方法:")
        methods_to_check = [
            'get_node', 'get_node_by_prefix', 'get_all_nodes',
            '_nodes', 'nodes', 'node_list'
        ]
        
        available_methods = {}
        for method in methods_to_check:
            if hasattr(robotics, method):
                obj = getattr(robotics, method)
                if callable(obj):
                    available_methods[method] = "方法"
                    print(f"   ✅ {method}() - 可用方法")
                else:
                    available_methods[method] = "属性"
                    print(f"   ✅ {method} - 可用属性")
            else:
                print(f"   ❌ {method} - 不存在")
        
        return robotics, available_methods
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None, {}


def test_node_access_methods(robotics, available_methods):
    """测试不同的节点访问方法"""
    print("\n=== 测试节点访问方法 ===")
    
    if not robotics:
        print("❌ Robotics实例不可用")
        return None
    
    robot_node = None
    
    # 方法1: get_node
    if 'get_node' in available_methods:
        try:
            robot_node = robotics.get_node("robot_1")
            if robot_node:
                print("✅ 方法1 (get_node) 成功")
                return robot_node
            else:
                print("⚠️ 方法1 (get_node) 返回None")
        except Exception as e:
            print(f"❌ 方法1 (get_node) 失败: {e}")
    
    # 方法2: _nodes属性
    if '_nodes' in available_methods:
        try:
            nodes = robotics._nodes
            print(f"   _nodes类型: {type(nodes)}")
            
            if isinstance(nodes, dict):
                print(f"   _nodes包含 {len(nodes)} 个项目:")
                for key, value in nodes.items():
                    print(f"     {key}: {type(value).__name__}")
                
                # 尝试获取robot_1
                robot_node = nodes.get("robot_1")
                if robot_node:
                    print("✅ 方法2 (_nodes['robot_1']) 成功")
                    return robot_node
                
                # 尝试完整路径
                robot_node = nodes.get("/localhost/ns/robot_1")
                if robot_node:
                    print("✅ 方法2 (_nodes['/localhost/ns/robot_1']) 成功")
                    return robot_node
                
                print("⚠️ 方法2 (_nodes) 未找到robot_1节点")
            else:
                print(f"⚠️ _nodes不是字典类型: {type(nodes)}")
                
        except Exception as e:
            print(f"❌ 方法2 (_nodes) 失败: {e}")
    
    # 方法3: get_node_by_prefix
    if 'get_node_by_prefix' in available_methods:
        try:
            robot_node = robotics.get_node_by_prefix("/localhost/ns/robot_1")
            if robot_node:
                print("✅ 方法3 (get_node_by_prefix) 成功")
                return robot_node
            else:
                print("⚠️ 方法3 (get_node_by_prefix) 返回None")
        except Exception as e:
            print(f"❌ 方法3 (get_node_by_prefix) 失败: {e}")
    
    # 方法4: get_all_nodes
    if 'get_all_nodes' in available_methods:
        try:
            all_nodes = robotics.get_all_nodes()
            if all_nodes and isinstance(all_nodes, dict):
                print(f"   get_all_nodes返回 {len(all_nodes)} 个节点:")
                for key, value in all_nodes.items():
                    print(f"     {key}: {type(value).__name__}")
                
                robot_node = all_nodes.get("robot_1")
                if robot_node:
                    print("✅ 方法4 (get_all_nodes) 成功")
                    return robot_node
                
                print("⚠️ 方法4 (get_all_nodes) 未找到robot_1节点")
            else:
                print("⚠️ 方法4 (get_all_nodes) 返回空或非字典")
        except Exception as e:
            print(f"❌ 方法4 (get_all_nodes) 失败: {e}")
    
    print("❌ 所有方法都失败了")
    return None


def test_robot_node_functionality(robot_node):
    """测试RobotNode的功能"""
    print("\n=== 测试RobotNode功能 ===")
    
    if not robot_node:
        print("❌ RobotNode不可用")
        return
    
    print(f"✅ RobotNode类型: {type(robot_node).__name__}")
    print(f"   节点ID: {id(robot_node)}")
    
    # 检查常用方法
    methods_to_check = [
        'get_tcp_position', 'get_joint_position',
        'joint_move', 'linear_move', 'connect', 'disconnect'
    ]
    
    print("\n🔍 检查RobotNode方法:")
    for method in methods_to_check:
        if hasattr(robot_node, method):
            print(f"   ✅ {method}")
        else:
            print(f"   ❌ {method}")
    
    # 检查属性
    attributes_to_check = [
        '_node_name', '_endpoint', '_namespace', '_robot'
    ]
    
    print("\n🔍 检查RobotNode属性:")
    for attr in attributes_to_check:
        if hasattr(robot_node, attr):
            try:
                value = getattr(robot_node, attr)
                print(f"   ✅ {attr}: {value}")
            except:
                print(f"   ✅ {attr}: <无法访问>")
        else:
            print(f"   ❌ {attr}")


def main():
    """主函数"""
    print("🚀 Robotics方法测试开始")
    print("=" * 60)
    
    try:
        # 1. 测试Robotics方法
        robotics, available_methods = test_robotics_methods()
        
        # 2. 测试节点访问方法
        robot_node = test_node_access_methods(robotics, available_methods)
        
        # 3. 测试RobotNode功能
        test_robot_node_functionality(robot_node)
        
        # 4. 总结
        print("\n" + "=" * 60)
        print("📋 测试总结:")
        print(f"   Robotics实例: {'✅' if robotics else '❌'}")
        print(f"   可用方法数: {len(available_methods)}")
        print(f"   RobotNode获取: {'✅' if robot_node else '❌'}")
        
        if available_methods:
            print("   推荐的获取方法:")
            if 'get_node' in available_methods:
                print("     1. robotics.get_node('robot_1')")
            if '_nodes' in available_methods:
                print("     2. robotics._nodes['robot_1']")
            if 'get_node_by_prefix' in available_methods:
                print("     3. robotics.get_node_by_prefix('/localhost/ns/robot_1')")
        
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n✅ 测试完成")


if __name__ == "__main__":
    main()
