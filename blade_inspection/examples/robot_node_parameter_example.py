#!/usr/bin/python
# -*- coding: utf-8 -*-

"""
RobotNode参数传递示例

这个示例演示了如何向RobotNode单例传递参数的正确方法。
由于RobotNode使用了@singleton装饰器，参数必须通过配置文件传递。
"""

import os
import typing as t
from hdmtv.core.cdi import get_instance
from blade_inspection.core.inspect_service import InspectService
from blade_inspection.utils.node_utils import get_robot_node


def show_current_robot_config():
    """显示当前机器人配置"""
    print("\n=== 当前机器人配置 ===")
    
    try:
        # 获取robot_node实例
        robot_node = get_robot_node("robot_1")
        
        if robot_node:
            print(f"✅ 成功获取RobotNode: {type(robot_node).__name__}")
            print(f"   节点ID: {id(robot_node)}")
            
            # 显示节点基本信息
            if hasattr(robot_node, '_node_name'):
                print(f"   节点名称: {robot_node._node_name}")
            if hasattr(robot_node, '_endpoint'):
                print(f"   端点: {robot_node._endpoint}")
            if hasattr(robot_node, '_namespace'):
                print(f"   命名空间: {robot_node._namespace}")
            
            # 显示机器人设备信息
            if hasattr(robot_node, '_robot') and robot_node._robot:
                robot_device = robot_node._robot
                print(f"   机器人设备类型: {type(robot_device).__name__}")
                
                # 显示配置参数
                config_attrs = [
                    ('_ip_addr', 'IP地址'),
                    ('_rapid_rate', '快速运行速率'),
                    ('_validate_rapid_rate', '验证模式速率'),
                    ('_max_speed', '最大速度'),
                    ('_gripper_delay', '夹爪延迟'),
                    ('_alt_gripper_delay', '备用夹爪延迟'),
                    ('_tool_coordinate', '工具坐标'),
                    ('_timeout', '超时时间'),
                    ('_retry_count', '重试次数'),
                ]
                
                print("   配置参数:")
                for attr, desc in config_attrs:
                    if hasattr(robot_device, attr):
                        value = getattr(robot_device, attr)
                        print(f"     {desc}: {value}")
                    elif hasattr(robot_node, attr):
                        value = getattr(robot_node, attr)
                        print(f"     {desc}: {value}")
            
            return robot_node
        else:
            print("❌ 无法获取RobotNode实例")
            return None
            
    except Exception as e:
        print(f"❌ 显示配置失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def demonstrate_config_modification():
    """演示如何通过配置文件修改参数"""
    print("\n=== 配置文件参数修改示例 ===")
    
    print("📝 要修改RobotNode参数，请按以下步骤操作：")
    print()
    print("1. 打开配置文件：blade_inspection/configs/config.yaml")
    print()
    print("2. 找到robot_1节点配置：")
    print("   ```yaml")
    print("   NODES:")
    print("     - node_name: \"robot_1\"")
    print("       endpoint: \"localhost\"")
    print("       namespace: \"ns\"")
    print("       node_class: \"RobotNode\"")
    print("       robot_class: \"FakeRobot\"")
    print("       # 在这里修改或添加参数")
    print("       ip_addr: \"*************\"")
    print("       rapid_rate: 0.8")
    print("       max_speed: 30")
    print("       # 添加自定义参数")
    print("       custom_param1: \"value1\"")
    print("       custom_param2: 123")
    print("   ```")
    print()
    print("3. 保存配置文件")
    print()
    print("4. 重新启动应用程序或重新初始化Robotics系统")


def demonstrate_parameter_access():
    """演示如何访问RobotNode的参数"""
    print("\n=== 参数访问示例 ===")
    
    try:
        # 获取robot_node实例
        robot_node = get_robot_node("robot_1")
        
        if not robot_node:
            print("❌ 无法获取RobotNode实例")
            return
        
        print("🔍 访问RobotNode参数的方法：")
        print()
        
        # 方法1：直接访问节点属性
        print("方法1：直接访问节点属性")
        if hasattr(robot_node, '_node_name'):
            print(f"   节点名称: {robot_node._node_name}")
        
        # 方法2：访问机器人设备属性
        print("方法2：访问机器人设备属性")
        if hasattr(robot_node, '_robot') and robot_node._robot:
            robot_device = robot_node._robot
            if hasattr(robot_device, '_max_speed'):
                print(f"   最大速度: {robot_device._max_speed}")
            if hasattr(robot_device, '_rapid_rate'):
                print(f"   快速运行速率: {robot_device._rapid_rate}")
        
        # 方法3：通过配置获取参数
        print("方法3：通过配置获取参数")
        try:
            inspect_service = get_instance(clazz=InspectService)
            config = inspect_service._config
            if config:
                nodes_config = config.get("NODES", [])
                robot_config = next((node for node in nodes_config 
                                   if node.get("node_name") == "robot_1"), None)
                if robot_config:
                    print(f"   配置中的IP地址: {robot_config.get('ip_addr')}")
                    print(f"   配置中的最大速度: {robot_config.get('max_speed')}")
        except Exception as e:
            print(f"   配置访问失败: {e}")
        
    except Exception as e:
        print(f"❌ 参数访问示例失败: {e}")


def create_custom_config_example():
    """创建自定义配置示例"""
    print("\n=== 自定义配置示例 ===")
    
    custom_config = {
        "node_name": "robot_1",
        "endpoint": "localhost",
        "namespace": "ns",
        "node_class": "RobotNode",
        "robot_class": "FakeRobot",
        "description": "自定义机器人通讯节点",
        
        # 网络参数
        "ip_addr": "*************",
        "port": 502,
        "timeout": 5.0,
        "retry_count": 3,
        
        # 运动参数
        "rapid_rate": 0.9,
        "validate_rapid_rate": 0.6,
        "max_speed": 50,
        
        # 夹爪参数
        "gripper_delay": 1.5,
        "alt_gripper_delay": 2.5,
        
        # 工具坐标
        "tool_coordinate": [10.0, 20.0, 30.0, 0.1, 0.2, 0.3],
        
        # 自定义参数
        "custom_param1": "my_custom_value",
        "custom_param2": 123,
        "custom_param3": True,
    }
    
    print("🔧 自定义配置示例：")
    for key, value in custom_config.items():
        print(f"   {key}: {value}")
    
    print()
    print("💡 要使用这些参数，请将它们添加到config.yaml文件中")


def main():
    """主函数"""
    print("🚀 RobotNode参数传递示例")
    print("=" * 60)
    
    try:
        # 1. 显示当前配置
        robot_node = show_current_robot_config()
        
        # 2. 演示配置修改方法
        demonstrate_config_modification()
        
        # 3. 演示参数访问方法
        demonstrate_parameter_access()
        
        # 4. 创建自定义配置示例
        create_custom_config_example()
        
        print("\n" + "=" * 60)
        print("✅ 示例完成")
        print()
        print("📋 总结：")
        print("1. RobotNode使用单例模式，参数必须通过配置文件传递")
        print("2. 修改config.yaml文件中的NODES配置来添加参数")
        print("3. 重新启动程序使参数生效")
        print("4. 通过robot_node实例或配置对象访问参数")
        
    except Exception as e:
        print(f"❌ 示例执行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
