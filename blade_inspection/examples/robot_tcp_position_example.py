#!/usr/bin/python
# -*- coding: utf-8 -*-

"""
在RobotSelectDialog中调用get_tcp_position的示例

这个文件展示了如何在robot_select_dialog.py中调用机器人的get_tcp_position函数
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from PyQt5.QtWidgets import QApplication
from blade_inspection.templates.robot_select_dialog import RobotSelectDialog


def example_tcp_position_in_dialog():
    """示例：在RobotSelectDialog中获取TCP位置"""
    print("=== RobotSelectDialog中获取TCP位置示例 ===")
    
    try:
        # 创建QApplication（如果还没有的话）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建对话框实例
        dialog = RobotSelectDialog()
        
        # 调用get_tcp_position方法
        print("🔄 调用get_tcp_position...")
        tcp_result = dialog.get_robot_tcp_position("robot_1")
        
        if tcp_result:
            print(f"✅ 获取TCP位置成功")
            print(f"📊 完整结果: {tcp_result}")
            
            if tcp_result.get("code") == 0:
                tcp_pos = tcp_result.get("tcp_position", [])
                if tcp_pos and len(tcp_pos) >= 6:
                    print(f"📍 TCP位置:")
                    print(f"   X: {tcp_pos[0]:.3f}")
                    print(f"   Y: {tcp_pos[1]:.3f}")
                    print(f"   Z: {tcp_pos[2]:.3f}")
                    print(f"   RX: {tcp_pos[3]:.3f}")
                    print(f"   RY: {tcp_pos[4]:.3f}")
                    print(f"   RZ: {tcp_pos[5]:.3f}")
                else:
                    print("⚠️ TCP位置数据格式异常")
            else:
                print(f"❌ 获取失败，错误码: {tcp_result.get('code')}")
                print(f"❌ 错误信息: {tcp_result.get('message')}")
        else:
            print("❌ 获取TCP位置失败")
            
    except Exception as e:
        print(f"❌ 示例执行失败: {e}")
        import traceback
        traceback.print_exc()


def example_multiple_calls():
    """示例：多次调用get_tcp_position"""
    print("\n=== 多次调用get_tcp_position示例 ===")
    
    try:
        # 创建QApplication（如果还没有的话）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建对话框实例
        dialog = RobotSelectDialog()
        
        # 多次调用
        for i in range(3):
            print(f"\n🔄 第{i+1}次调用get_tcp_position...")
            tcp_result = dialog.get_robot_tcp_position("robot_1")
            
            if tcp_result and tcp_result.get("code") == 0:
                tcp_pos = tcp_result.get("tcp_position", [])
                if tcp_pos and len(tcp_pos) >= 6:
                    print(f"✅ 第{i+1}次获取成功: [{tcp_pos[0]:.3f}, {tcp_pos[1]:.3f}, {tcp_pos[2]:.3f}]")
                else:
                    print(f"⚠️ 第{i+1}次获取的数据格式异常")
            else:
                print(f"❌ 第{i+1}次获取失败")
                
    except Exception as e:
        print(f"❌ 多次调用示例失败: {e}")


def example_error_handling():
    """示例：错误处理"""
    print("\n=== 错误处理示例 ===")
    
    try:
        # 创建QApplication（如果还没有的话）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建对话框实例
        dialog = RobotSelectDialog()
        
        # 测试不存在的节点
        print("🔄 测试不存在的节点...")
        tcp_result = dialog.get_robot_tcp_position("non_existent_robot")
        
        if tcp_result:
            print(f"📊 结果: {tcp_result}")
        else:
            print("✅ 正确处理了不存在的节点")
            
        # 测试正常节点
        print("\n🔄 测试正常节点...")
        tcp_result = dialog.get_robot_tcp_position("robot_1")
        
        if tcp_result:
            print(f"✅ 正常节点处理成功")
            print(f"📊 状态码: {tcp_result.get('code')}")
            print(f"📊 消息: {tcp_result.get('message')}")
        else:
            print("⚠️ 正常节点处理失败")
            
    except Exception as e:
        print(f"❌ 错误处理示例失败: {e}")


def main():
    """主函数"""
    print("🚀 RobotSelectDialog中get_tcp_position调用示例开始")
    
    try:
        # 1. 基本使用示例
        example_tcp_position_in_dialog()
        
        # 2. 多次调用示例
        example_multiple_calls()
        
        # 3. 错误处理示例
        example_error_handling()
        
    except Exception as e:
        print(f"❌ 主函数执行失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n✅ RobotSelectDialog中get_tcp_position调用示例完成")


if __name__ == "__main__":
    main()
