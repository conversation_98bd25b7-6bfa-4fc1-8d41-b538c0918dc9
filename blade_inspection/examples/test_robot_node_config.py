#!/usr/bin/python
# -*- coding: utf-8 -*-

"""
RobotNode配置测试脚本

这个脚本用于测试和验证RobotNode的配置参数是否正确传入
"""

import sys
import os
import json

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from blade_inspection.utils import get_robot_node, check_robot_node_status, debug_robot_node


def test_robot_node_existence():
    """测试RobotNode是否存在并可以获取"""
    print("=== 测试RobotNode存在性 ===")
    
    try:
        robot_node = get_robot_node("robot_1")
        
        if robot_node:
            print("✅ RobotNode获取成功")
            print(f"   节点类型: {type(robot_node).__name__}")
            print(f"   节点ID: {id(robot_node)}")
            return True
        else:
            print("❌ RobotNode获取失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def test_robot_node_parameters():
    """测试RobotNode的参数配置"""
    print("\n=== 测试RobotNode参数配置 ===")
    
    try:
        robot_node = get_robot_node("robot_1")
        
        if not robot_node:
            print("❌ 无法获取RobotNode，跳过参数测试")
            return False
        
        # 测试基本节点参数
        basic_params = {
            '_node_name': 'robot_1',
            '_endpoint': 'localhost',
            '_namespace': 'ns'
        }
        
        print("🔍 检查基本参数:")
        for param, expected in basic_params.items():
            if hasattr(robot_node, param):
                actual = getattr(robot_node, param)
                status = "✅" if actual == expected else "⚠️"
                print(f"   {param}: {actual} {status}")
            else:
                print(f"   {param}: 未找到 ❌")
        
        # 测试机器人设备参数
        if hasattr(robot_node, '_robot') and robot_node._robot:
            print("\n🤖 检查机器人设备参数:")
            robot_device = robot_node._robot
            
            device_params = [
                '_ip_addr', '_rapid_rate', '_validate_rapid_rate',
                '_max_speed', '_gripper_delay', '_alt_gripper_delay'
            ]
            
            for param in device_params:
                if hasattr(robot_device, param):
                    value = getattr(robot_device, param)
                    print(f"   {param}: {value} ✅")
                else:
                    print(f"   {param}: 未找到 ❌")
        else:
            print("\n⚠️ 机器人设备未初始化或不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 参数测试失败: {e}")
        return False


def test_robot_node_singleton():
    """测试RobotNode的单例特性"""
    print("\n=== 测试RobotNode单例特性 ===")
    
    try:
        # 获取第一个实例
        robot_node_1 = get_robot_node("robot_1")
        
        # 获取第二个实例
        robot_node_2 = get_robot_node("robot_1")
        
        if robot_node_1 and robot_node_2:
            # 检查是否为同一个实例
            is_singleton = robot_node_1 is robot_node_2
            
            print(f"第一个实例ID: {id(robot_node_1)}")
            print(f"第二个实例ID: {id(robot_node_2)}")
            print(f"是否为单例: {'✅' if is_singleton else '❌'}")
            
            return is_singleton
        else:
            print("❌ 无法获取RobotNode实例")
            return False
            
    except Exception as e:
        print(f"❌ 单例测试失败: {e}")
        return False


def test_robot_node_status():
    """测试RobotNode的状态"""
    print("\n=== 测试RobotNode状态 ===")
    
    try:
        status = check_robot_node_status("robot_1")
        
        print("📊 节点状态:")
        for key, value in status.items():
            print(f"   {key}: {value}")
        
        # 检查关键状态
        if status.get("exists"):
            print("✅ 节点存在")
        else:
            print("❌ 节点不存在")
            
        if status.get("error"):
            print(f"⚠️ 发现错误: {status['error']}")
        
        return status.get("exists", False)
        
    except Exception as e:
        print(f"❌ 状态测试失败: {e}")
        return False


def test_robot_node_debug_info():
    """测试RobotNode的调试信息"""
    print("\n=== 测试RobotNode调试信息 ===")
    
    try:
        debug_info = debug_robot_node("robot_1")
        
        print("🔍 调试信息:")
        print(f"   节点存在: {debug_info.get('node_exists')}")
        print(f"   节点类型: {debug_info.get('node_type')}")
        print(f"   有get_tcp_position方法: {debug_info.get('has_get_tcp_position')}")
        print(f"   可用方法数量: {len(debug_info.get('available_methods', []))}")
        
        # 显示常见方法的可用性
        common_methods = debug_info.get('common_methods', {})
        if common_methods:
            print("   常见方法可用性:")
            for method, available in common_methods.items():
                status = "✅" if available else "❌"
                print(f"     {method}: {status}")
        
        return debug_info.get('node_exists', False)
        
    except Exception as e:
        print(f"❌ 调试信息测试失败: {e}")
        return False


def generate_test_report(results):
    """生成测试报告"""
    print("\n" + "=" * 60)
    print("📋 测试报告")
    print("=" * 60)
    
    total_tests = len(results)
    passed_tests = sum(1 for result in results.values() if result)
    
    print(f"总测试数: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"失败测试: {total_tests - passed_tests}")
    print(f"通过率: {(passed_tests / total_tests * 100):.1f}%")
    
    print("\n详细结果:")
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    # 生成JSON报告
    report = {
        "timestamp": str(os.popen("date").read().strip()),
        "total_tests": total_tests,
        "passed_tests": passed_tests,
        "failed_tests": total_tests - passed_tests,
        "pass_rate": passed_tests / total_tests * 100,
        "results": results
    }
    
    try:
        report_file = os.path.join(project_root, "test_report.json")
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        print(f"\n📄 详细报告已保存到: {report_file}")
    except Exception as e:
        print(f"⚠️ 保存报告失败: {e}")


def main():
    """主测试函数"""
    print("🚀 RobotNode配置测试开始")
    print("=" * 60)
    
    # 执行所有测试
    test_results = {}
    
    test_results["节点存在性测试"] = test_robot_node_existence()
    test_results["参数配置测试"] = test_robot_node_parameters()
    test_results["单例特性测试"] = test_robot_node_singleton()
    test_results["节点状态测试"] = test_robot_node_status()
    test_results["调试信息测试"] = test_robot_node_debug_info()
    
    # 生成测试报告
    generate_test_report(test_results)
    
    # 总结
    all_passed = all(test_results.values())
    if all_passed:
        print("\n🎉 所有测试通过！RobotNode配置正常")
    else:
        print("\n⚠️ 部分测试失败，请检查配置")
    
    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
