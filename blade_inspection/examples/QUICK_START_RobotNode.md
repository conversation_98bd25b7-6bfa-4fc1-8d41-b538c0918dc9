# RobotNode单例快速入门指南

## 🚀 快速开始

### 1. 查看当前配置

```python
from blade_inspection.utils import get_robot_node

# 获取RobotNode实例
robot_node = get_robot_node("robot_1")
if robot_node:
    print("✅ RobotNode获取成功")
else:
    print("❌ RobotNode获取失败")
```

### 2. 运行测试脚本

```bash
cd blade_inspection/examples
python test_robot_node_config.py
```

### 3. 查看示例代码

```bash
python robot_node_singleton_example.py
```

## 📋 配置参数速查表

| 参数名称 | 类型 | 默认值 | 说明 |
|---------|------|--------|------|
| `node_name` | str | "robot_1" | 节点名称 |
| `ip_addr` | str | "*************" | 机器人IP地址 |
| `rapid_rate` | float | 0.8 | 快速运行速率 |
| `max_speed` | int | 30 | 最大速度 |
| `gripper_delay` | float | 1.0 | 夹爪延迟 |
| `tool_coordinate` | list | [0,0,0,0,0,0] | 工具坐标 |

## 🔧 常用配置模板

### 高精度模式
```yaml
rapid_rate: 0.5
max_speed: 15
gripper_delay: 0.8
```

### 高速模式
```yaml
rapid_rate: 0.95
max_speed: 80
gripper_delay: 0.3
```

### 测试模式
```yaml
robot_class: "FakeRobot"
rapid_rate: 1.0
timeout: 10.0
```

## 📁 文件结构

```
blade_inspection/examples/
├── robot_node_singleton_example.py    # 完整示例
├── custom_robot_config.yaml          # 配置示例
├── test_robot_node_config.py         # 测试脚本
├── README_RobotNode_Singleton.md     # 详细文档
└── QUICK_START_RobotNode.md          # 本文件
```

## ⚡ 一键测试

```python
# 一键测试RobotNode配置
from blade_inspection.examples.test_robot_node_config import main
success = main()
```

## 🔍 故障排除

### 问题1：获取不到RobotNode
**解决方案**：
1. 检查配置文件路径
2. 确认InspectService已初始化
3. 查看日志输出

### 问题2：参数不生效
**解决方案**：
1. 修改配置文件后重启程序
2. 检查参数名称是否正确
3. 验证参数类型和范围

### 问题3：连接失败
**解决方案**：
1. 检查IP地址和端口
2. 确认网络连通性
3. 验证机器人控制器状态

## 📞 获取帮助

- 查看详细文档：`README_RobotNode_Singleton.md`
- 运行调试工具：`debug_robot_node("robot_1")`
- 检查节点状态：`check_robot_node_status("robot_1")`

## 🎯 下一步

1. 根据实际需求修改配置参数
2. 运行测试验证配置正确性
3. 集成到实际应用中
4. 监控运行状态和性能
