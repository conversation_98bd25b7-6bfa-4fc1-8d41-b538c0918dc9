# RobotNode单例参数传入指南

本文档详细说明了如何在 `blade_inspection` 项目中为 RobotNode 单例传入相应的参数。

## 概述

在 `blade_inspection` 项目中，RobotNode 是通过 `hd_robotics` 框架管理的单例对象。参数传入主要通过以下方式：

1. **配置文件方式**（推荐）
2. **程序化配置方式**
3. **运行时参数修改**

## 1. 配置文件方式（推荐）

### 1.1 配置文件位置

主配置文件位于：`blade_inspection/configs/config.yaml`

### 1.2 RobotNode配置结构

```yaml
NODES:
  - node_name: "robot_1"              # 节点名称（必需）
    endpoint: "localhost"             # 端点地址（必需）
    namespace: "ns"                   # 命名空间（必需）
    node_class: "RobotNode"           # 节点类名（必需）
    robot_class: "FakeRobot"          # 机器人设备类（必需）
    description: "机器人通讯节点"      # 描述信息
    
    # 网络连接参数
    ip_addr: "*************"          # 机器人IP地址
    port: 502                         # 通讯端口
    timeout: 5.0                      # 连接超时时间（秒）
    retry_count: 3                    # 重试次数
    connection_type: "tcp"            # 连接类型
    
    # 运动控制参数
    rapid_rate: 0.8                   # 快速运行速率 (0.0-1.0)
    validate_rapid_rate: 0.5          # 验证模式速率
    max_speed: 30                     # 最大速度 (mm/s)
    
    # 夹爪控制参数
    gripper_delay: 1.0                # 夹爪延迟（秒）
    alt_gripper_delay: 2.0            # 备用夹爪延迟（秒）
    
    # 工具坐标系 [x, y, z, rx, ry, rz]
    tool_coordinate:
      - 0.0  # X偏移 (mm)
      - 0.0  # Y偏移 (mm)  
      - 0.0  # Z偏移 (mm)
      - 0.0  # RX旋转 (弧度)
      - 0.0  # RY旋转 (弧度)
      - 0.0  # RZ旋转 (弧度)
```

### 1.3 参数说明

#### 基本参数
- `node_name`: 节点唯一标识符
- `endpoint`: 通讯端点，通常为 "localhost"
- `namespace`: 命名空间，用于节点分组
- `node_class`: 固定为 "RobotNode"
- `robot_class`: 机器人设备类型

#### 网络参数
- `ip_addr`: 机器人控制器IP地址
- `port`: 通讯端口号
- `timeout`: 网络连接超时时间
- `retry_count`: 连接失败重试次数

#### 运动参数
- `rapid_rate`: 快速运行模式下的速度比例
- `validate_rapid_rate`: 验证模式下的速度比例
- `max_speed`: 机器人最大运行速度

#### 工具参数
- `gripper_delay`: 夹爪动作延迟时间
- `tool_coordinate`: 工具坐标系偏移量

## 2. 程序化配置方式

### 2.1 通过InspectService初始化

```python
from blade_inspection.core.inspect_service import InspectService
from hdmtv.core.cdi import get_instance

# 获取InspectService实例（单例）
inspect_service = get_instance(clazz=InspectService)

# 获取Robotics实例
robotics = inspect_service._robotics

# 获取RobotNode实例（单例）
robot_node = robotics.get_node("robot_1")
```

### 2.2 直接通过Robotics初始化

```python
from hd_robotics.robotics import Robotics

# 初始化Robotics系统
Robotics.initialize(
    log_path="/path/to/logs",
    config_file="/path/to/config.yaml",
    node_paths=["/path/to/nodes"],
    action_paths=["/path/to/actions"],
    device_paths=["/path/to/devices"]
)

# 创建Robotics实例
robotics = Robotics(name="blade_inspect")

# 获取RobotNode实例
robot_node = robotics.get_node("robot_1")
```

## 3. 运行时参数修改

### 3.1 检查当前参数

```python
from blade_inspection.utils import get_robot_node

# 获取robot_node实例
robot_node = get_robot_node("robot_1")

if robot_node:
    # 检查节点参数
    print(f"节点名称: {robot_node._node_name}")
    print(f"IP地址: {robot_node._ip_addr}")
    
    # 检查机器人设备参数
    if hasattr(robot_node, '_robot'):
        robot_device = robot_node._robot
        print(f"最大速度: {robot_device._max_speed}")
        print(f"快速运行速率: {robot_device._rapid_rate}")
```

### 3.2 修改参数（谨慎使用）

```python
# 注意：直接修改参数可能影响系统稳定性
# 建议通过配置文件修改后重新初始化

if robot_node and hasattr(robot_node, '_robot'):
    robot_device = robot_node._robot
    
    # 修改运动参数
    robot_device._max_speed = 25
    robot_device._rapid_rate = 0.7
    
    # 修改网络参数
    robot_device._timeout = 3.0
```

## 4. 不同应用场景的配置示例

### 4.1 高精度作业配置

```yaml
- node_name: "precision_robot"
  # ... 基本配置 ...
  rapid_rate: 0.5          # 低速率提高精度
  max_speed: 15            # 低速度
  gripper_delay: 0.8       # 较长延迟确保稳定
  tool_coordinate: [5.0, 2.0, 10.0, 0.1, 0.05, 0.0]
```

### 4.2 高速作业配置

```yaml
- node_name: "speed_robot"
  # ... 基本配置 ...
  rapid_rate: 0.95         # 高速率
  max_speed: 80            # 高速度
  gripper_delay: 0.3       # 短延迟提高效率
  timeout: 2.0             # 短超时时间
```

### 4.3 测试环境配置

```yaml
- node_name: "test_robot"
  robot_class: "FakeRobot" # 使用虚拟机器人
  # ... 其他配置 ...
  rapid_rate: 1.0          # 测试时可用最高速率
  timeout: 10.0            # 较长超时便于调试
```

## 5. 最佳实践

### 5.1 参数配置建议

1. **生产环境**：使用保守的速度和延迟参数
2. **测试环境**：可以使用更激进的参数进行测试
3. **调试模式**：增加超时时间和重试次数

### 5.2 参数修改流程

1. 备份当前配置文件
2. 修改配置文件中的参数
3. 重新启动应用程序或重新初始化Robotics
4. 验证参数是否生效
5. 进行功能测试

### 5.3 安全注意事项

1. 修改速度参数前确保安全
2. 测试新参数时使用仿真模式
3. 逐步调整参数，避免大幅度变更
4. 保留工作配置的备份

## 6. 故障排除

### 6.1 常见问题

1. **节点获取失败**：检查配置文件路径和格式
2. **参数不生效**：确认是否重新初始化了系统
3. **连接超时**：检查网络参数和机器人状态

### 6.2 调试方法

```python
# 使用调试函数检查节点状态
from blade_inspection.utils import debug_robot_node

debug_info = debug_robot_node("robot_1")
print(debug_info)
```

## 7. 示例代码

完整的示例代码请参考：
- `blade_inspection/examples/robot_node_singleton_example.py`
- `blade_inspection/examples/custom_robot_config.yaml`

## 8. 相关文档

- [Robot Node 获取工具](../utils/README.md)
- [机器人TCP位置获取](README_robot_tcp_position.md)
- [节点获取分析](../docs/get_node_analysis.md)
