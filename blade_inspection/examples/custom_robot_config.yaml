# 自定义机器人节点配置示例
# 这个文件展示了如何为RobotNode单例配置不同的参数

INSPECT:
  cache_path: "D:/blade_inspect"

NODES:
  # 标准机器人节点配置
  - node_name: "robot_1"
    endpoint: "localhost"
    namespace: "ns"
    node_class: "RobotNode"
    robot_class: "FakeRobot"  # 可选: FakeRobot, ABBRobot, KukaRobot, UniversalRobot 等
    description: "主要机器人通讯节点"
    
    # 网络连接参数
    ip_addr: "*************"
    port: 502
    timeout: 5.0
    retry_count: 3
    connection_type: "tcp"
    
    # 运动控制参数
    rapid_rate: 0.8              # 快速运行速率 (0.0-1.0)
    validate_rapid_rate: 0.5     # 验证模式下的快速运行速率
    max_speed: 30                # 最大速度 (mm/s 或 度/s)
    
    # 夹爪控制参数
    gripper_delay: 1.0           # 夹爪动作延迟 (秒)
    alt_gripper_delay: 2.0       # 备用夹爪动作延迟 (秒)
    
    # 工具坐标系参数 [x, y, z, rx, ry, rz]
    tool_coordinate:
      - 0.0    # X偏移 (mm)
      - 0.0    # Y偏移 (mm)
      - 0.0    # Z偏移 (mm)
      - 0.0    # RX旋转 (弧度)
      - 0.0    # RY旋转 (弧度)
      - 0.0    # RZ旋转 (弧度)
    
    # 安全参数
    collision_detection: true
    force_limit: 100.0           # 力限制 (N)
    torque_limit: 50.0           # 扭矩限制 (Nm)
    
    # 其他可选参数
    publish_interval: 0.1        # 数据发布间隔 (秒)
    log_level: "INFO"           # 日志级别
    enable_simulation: false     # 是否启用仿真模式

  # 高精度机器人节点配置示例
  - node_name: "precision_robot_1"
    endpoint: "localhost"
    namespace: "precision"
    node_class: "RobotNode"
    robot_class: "ABBRobot"
    description: "高精度机器人节点"
    
    # 高精度网络配置
    ip_addr: "*************"
    port: 502
    timeout: 3.0
    retry_count: 5
    connection_type: "tcp"
    
    # 高精度运动参数
    rapid_rate: 0.6              # 较低的速率以提高精度
    validate_rapid_rate: 0.3
    max_speed: 20                # 较低的最大速度
    
    # 精密夹爪参数
    gripper_delay: 0.5
    alt_gripper_delay: 1.0
    
    # 精密工具坐标
    tool_coordinate:
      - 5.0    # 精密工具的偏移
      - 2.0
      - 10.0
      - 0.1
      - 0.05
      - 0.0
    
    # 高精度安全参数
    collision_detection: true
    force_limit: 50.0            # 更低的力限制
    torque_limit: 25.0
    position_tolerance: 0.1      # 位置容差 (mm)
    orientation_tolerance: 0.01  # 姿态容差 (弧度)

  # 高速机器人节点配置示例
  - node_name: "speed_robot_1"
    endpoint: "localhost"
    namespace: "speed"
    node_class: "RobotNode"
    robot_class: "KukaRobot"
    description: "高速机器人节点"
    
    # 高速网络配置
    ip_addr: "*************"
    port: 502
    timeout: 2.0
    retry_count: 2
    connection_type: "tcp"
    
    # 高速运动参数
    rapid_rate: 0.95             # 高速率
    validate_rapid_rate: 0.8
    max_speed: 100               # 高速度
    
    # 快速夹爪参数
    gripper_delay: 0.2
    alt_gripper_delay: 0.5
    
    # 标准工具坐标
    tool_coordinate:
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
      - 0.0
    
    # 高速安全参数
    collision_detection: true
    force_limit: 200.0           # 更高的力限制
    torque_limit: 100.0
    acceleration_limit: 5.0      # 加速度限制 (m/s²)

  # PLC节点配置
  - node_name: "plc_1"
    endpoint: "localhost"
    namespace: "ns"
    node_class: "PLCNode"
    plc_class: "FakePLC"
    description: "本地总控PLC通讯节点"
    ip_addr: "*************"
    con_type: 1

  # 视觉识别节点配置
  - node_name: "visual_1"
    endpoint: "localhost"
    namespace: "ns"
    node_class: "VisualRecognizerNode"
    recognizer_class: "GeneralRecognizer"
    description: "视觉识别节点"
    model_path: "detection_model.engine"
    input_shape: [640, 640, 3]
    engine_type: "paddle"

# 全局配置参数
GLOBAL:
  # 系统级参数
  system_timeout: 30.0
  max_retry_attempts: 3
  log_rotation_size: "10MB"
  log_retention_days: 30
  
  # 默认机器人参数（当节点配置中未指定时使用）
  default_robot_params:
    rapid_rate: 0.7
    validate_rapid_rate: 0.4
    max_speed: 25
    gripper_delay: 1.0
    alt_gripper_delay: 2.0
    timeout: 5.0
    retry_count: 3
    
  # 安全配置
  safety:
    emergency_stop_enabled: true
    collision_detection_enabled: true
    force_monitoring_enabled: true
    workspace_limits:
      x_min: -1000.0
      x_max: 1000.0
      y_min: -1000.0
      y_max: 1000.0
      z_min: 0.0
      z_max: 1000.0
