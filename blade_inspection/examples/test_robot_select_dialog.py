#!/usr/bin/python
# -*- coding: utf-8 -*-

"""
RobotSelectDialog测试脚本

这个脚本用于测试修改后的RobotSelectDialog是否能正确获取RobotNode实例
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt
from templates.robot_select_dialog import RobotSelectDialog


class TestMainWindow(QMainWindow):
    """测试主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("RobotSelectDialog 测试")
        self.setGeometry(100, 100, 400, 300)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 创建测试按钮
        self.test_dialog_button = QPushButton("测试 RobotSelectDialog")
        self.test_dialog_button.clicked.connect(self.test_robot_select_dialog)
        layout.addWidget(self.test_dialog_button)
        
        self.test_modify_button = QPushButton("测试修改模式")
        self.test_modify_button.clicked.connect(self.test_modify_mode)
        layout.addWidget(self.test_modify_button)
        
        self.test_insert_button = QPushButton("测试插入模式")
        self.test_insert_button.clicked.connect(self.test_insert_mode)
        layout.addWidget(self.test_insert_button)
        
        # 创建RobotSelectDialog实例
        self.robot_dialog = None
        
    def test_robot_select_dialog(self):
        """测试RobotSelectDialog基本功能"""
        print("=== 测试RobotSelectDialog基本功能 ===")
        
        try:
            # 创建对话框实例
            self.robot_dialog = RobotSelectDialog(parent=self)
            
            # 检查RobotNode是否正确初始化
            if self.robot_dialog._robot_node:
                print("✅ RobotNode初始化成功")
                print(f"   节点类型: {type(self.robot_dialog._robot_node).__name__}")
            else:
                print("❌ RobotNode初始化失败")
            
            # 检查Robotics是否正确初始化
            if self.robot_dialog._robotics:
                print("✅ Robotics初始化成功")
            else:
                print("❌ Robotics初始化失败")
            
            # 检查工具信息是否正确初始化
            if self.robot_dialog._util_info:
                print("✅ 工具信息初始化成功")
                print(f"   机器人名称: {self.robot_dialog._util_info.robot_name}")
                print(f"   机器人类型: {self.robot_dialog._util_info.robot_kind}")
            else:
                print("❌ 工具信息初始化失败")
            
            # 显示对话框
            self.robot_dialog.show_dialog(request="modify")
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
    
    def test_modify_mode(self):
        """测试修改模式"""
        print("=== 测试修改模式 ===")
        
        try:
            if not self.robot_dialog:
                self.robot_dialog = RobotSelectDialog(parent=self)
            
            # 显示修改模式对话框
            self.robot_dialog.show_dialog(
                request="modify",
                speed=50.0,
                accuracy=1,
                is_absolute=True,
                is_joint=True
            )
            
        except Exception as e:
            print(f"❌ 修改模式测试失败: {e}")
    
    def test_insert_mode(self):
        """测试插入模式"""
        print("=== 测试插入模式 ===")
        
        try:
            if not self.robot_dialog:
                self.robot_dialog = RobotSelectDialog(parent=self)
            
            # 显示插入模式对话框
            self.robot_dialog.show_dialog(
                request="insert",
                speed=30.0,
                accuracy=-1,
                is_absolute=False,
                is_joint=False,
                is_insert_front=True
            )
            
        except Exception as e:
            print(f"❌ 插入模式测试失败: {e}")


def test_robot_node_initialization():
    """测试RobotNode初始化"""
    print("=== 测试RobotNode初始化 ===")
    
    try:
        # 直接测试RobotSelectDialog的初始化
        dialog = RobotSelectDialog()
        
        print("📋 初始化结果:")
        print(f"   RobotNode: {'✅' if dialog._robot_node else '❌'}")
        print(f"   Robotics: {'✅' if dialog._robotics else '❌'}")
        print(f"   工具信息: {'✅' if dialog._util_info else '❌'}")
        
        if dialog._robot_node:
            print(f"   RobotNode类型: {type(dialog._robot_node).__name__}")
            print(f"   RobotNode ID: {id(dialog._robot_node)}")
        
        if dialog._util_info:
            print(f"   机器人名称: {dialog._util_info.robot_name}")
            print(f"   机器人类型: {dialog._util_info.robot_kind}")
        
        return dialog
        
    except Exception as e:
        print(f"❌ RobotNode初始化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_robot_node_availability_check():
    """测试RobotNode可用性检查"""
    print("\n=== 测试RobotNode可用性检查 ===")
    
    try:
        dialog = RobotSelectDialog()
        
        # 测试可用性检查方法
        is_available = dialog._check_robot_node_available()
        print(f"RobotNode可用性: {'✅' if is_available else '❌'}")
        
        return is_available
        
    except Exception as e:
        print(f"❌ 可用性检查测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 RobotSelectDialog测试开始")
    print("=" * 60)
    
    # 创建QApplication
    app = QApplication(sys.argv)
    
    try:
        # 1. 测试RobotNode初始化
        dialog = test_robot_node_initialization()
        
        # 2. 测试可用性检查
        is_available = test_robot_node_availability_check()
        
        # 3. 如果初始化成功，创建测试窗口
        if dialog and is_available:
            print("\n=== 创建测试窗口 ===")
            test_window = TestMainWindow()
            test_window.show()
            
            print("✅ 测试窗口创建成功")
            print("💡 点击按钮测试不同功能")
            
            # 运行应用程序
            sys.exit(app.exec_())
        else:
            print("\n❌ 初始化失败，无法创建测试窗口")
            print("请检查以下项目:")
            print("1. InspectService是否正确初始化")
            print("2. Robotics系统是否正确配置")
            print("3. RobotNode配置是否正确")
            
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("✅ RobotSelectDialog测试完成")


if __name__ == "__main__":
    main()
