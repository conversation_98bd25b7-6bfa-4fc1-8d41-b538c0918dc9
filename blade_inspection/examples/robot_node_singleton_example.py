#!/usr/bin/python
# -*- coding: utf-8 -*-

"""
RobotNode单例参数传入示例

这个文件展示了如何在blade_inspection工程中为RobotNode单例传入相应的参数
"""

import sys
import os
import typing as t

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from hdmtv.core.cdi import get_instance
from hdmtv.core.config import Config
from hd_robotics.robotics import Robotics
from blade_inspection.core.inspect_service import InspectService
from blade_inspection.utils import get_robot_node


def show_current_robot_config():
    """显示当前机器人节点的配置参数"""
    print("=== 当前机器人节点配置参数 ===")
    
    try:
        # 获取Config实例
        config = get_instance(clazz=Config)
        
        # 获取NODES配置
        nodes_config = config.get("NODES", [])
        
        # 查找robot节点配置
        robot_configs = [node for node in nodes_config if node.get("node_class") == "RobotNode"]
        
        if robot_configs:
            for robot_config in robot_configs:
                print(f"\n📋 节点名称: {robot_config.get('node_name')}")
                print(f"   节点类: {robot_config.get('node_class')}")
                print(f"   机器人类: {robot_config.get('robot_class')}")
                print(f"   端点: {robot_config.get('endpoint')}")
                print(f"   命名空间: {robot_config.get('namespace')}")
                print(f"   IP地址: {robot_config.get('ip_addr')}")
                print(f"   快速运行速率: {robot_config.get('rapid_rate')}")
                print(f"   验证快速运行速率: {robot_config.get('validate_rapid_rate')}")
                print(f"   最大速度: {robot_config.get('max_speed')}")
                print(f"   夹爪延迟: {robot_config.get('gripper_delay')}")
                print(f"   备用夹爪延迟: {robot_config.get('alt_gripper_delay')}")
                print(f"   工具坐标: {robot_config.get('tool_coordinate')}")
                print(f"   描述: {robot_config.get('description')}")
        else:
            print("❌ 未找到RobotNode配置")
            
    except Exception as e:
        print(f"❌ 获取配置失败: {e}")


def create_robot_node_with_custom_params():
    """演示如何创建带有自定义参数的RobotNode"""
    print("\n=== 创建带有自定义参数的RobotNode示例 ===")
    
    # 自定义机器人节点参数
    custom_robot_params = {
        "node_name": "custom_robot_1",
        "endpoint": "localhost", 
        "namespace": "custom_ns",
        "node_class": "RobotNode",
        "robot_class": "FakeRobot",  # 或者 "ABBRobot", "KukaRobot" 等
        "description": "自定义机器人通讯节点",
        "ip_addr": "*************",
        "rapid_rate": 0.9,
        "validate_rapid_rate": 0.6,
        "max_speed": 50,
        "gripper_delay": 1.5,
        "alt_gripper_delay": 2.5,
        "tool_coordinate": [10.0, 20.0, 30.0, 0.1, 0.2, 0.3],
        # 其他可能的参数
        "port": 502,
        "timeout": 5.0,
        "retry_count": 3,
        "connection_type": "tcp"
    }
    
    print("🔧 自定义参数:")
    for key, value in custom_robot_params.items():
        print(f"   {key}: {value}")
    
    return custom_robot_params


def demonstrate_robotics_initialization_with_custom_config():
    """演示如何使用自定义配置初始化Robotics"""
    print("\n=== 使用自定义配置初始化Robotics示例 ===")
    
    try:
        # 获取当前项目路径
        root_path = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
        
        # 定义路径
        log_path = os.path.join(root_path, "logs")
        config_file = os.path.join(root_path, "configs/config.yaml")
        node_paths = [os.path.join(root_path, "nodes")]
        action_paths = [os.path.join(root_path, "actions")]
        device_paths = [os.path.join(root_path, "devices")]
        
        # 确保日志目录存在
        os.makedirs(log_path, exist_ok=True)
        
        print(f"📁 日志路径: {log_path}")
        print(f"📄 配置文件: {config_file}")
        print(f"📂 节点路径: {node_paths}")
        print(f"📂 动作路径: {action_paths}")
        print(f"📂 设备路径: {device_paths}")
        
        # 初始化Robotics（这会加载配置文件中的所有节点）
        print("🔄 初始化Robotics...")
        Robotics.initialize(
            log_path=log_path,
            config_file=config_file,
            node_paths=node_paths,
            action_paths=action_paths,
            device_paths=device_paths
        )
        print("✅ Robotics初始化完成")
        
        # 创建Robotics实例
        robotics = Robotics(name="custom_blade_inspect")
        print("✅ Robotics实例创建完成")
        
        return robotics
        
    except Exception as e:
        print(f"❌ Robotics初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def get_robot_node_singleton_example():
    """获取RobotNode单例的示例"""
    print("\n=== 获取RobotNode单例示例 ===")
    
    try:
        # 方法1: 通过InspectService获取（推荐方式）
        print("方法1: 通过InspectService获取RobotNode")
        inspect_service = get_instance(clazz=InspectService)
        robotics = inspect_service._robotics
        
        # 获取robot_node实例（这是一个单例）
        robot_node = robotics.get_node("robot_1")
        
        if robot_node:
            print(f"✅ 成功获取RobotNode单例: {type(robot_node).__name__}")
            print(f"   节点ID: {id(robot_node)}")
            
            # 检查节点属性
            if hasattr(robot_node, '_robot'):
                print(f"   机器人设备类型: {type(robot_node._robot).__name__}")
            
            # 再次获取同一个节点，验证是否为单例
            robot_node_2 = robotics.get_node("robot_1")
            if robot_node_2:
                print(f"   第二次获取节点ID: {id(robot_node_2)}")
                print(f"   是否为同一个实例: {robot_node is robot_node_2}")
            
            return robot_node
        else:
            print("❌ 获取RobotNode失败")
            return None
            
    except Exception as e:
        print(f"❌ 获取RobotNode单例失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def demonstrate_robot_node_parameters(robot_node):
    """演示RobotNode的参数使用"""
    print("\n=== RobotNode参数使用示例 ===")
    
    if not robot_node:
        print("❌ robot_node为空，无法演示参数使用")
        return
    
    try:
        # 检查节点的配置参数
        print("🔍 检查节点配置参数:")
        
        # 常见的节点属性
        attributes_to_check = [
            '_node_name', '_endpoint', '_namespace', '_prefix',
            '_robot', '_ip_addr', '_rapid_rate', '_validate_rapid_rate',
            '_max_speed', '_gripper_delay', '_alt_gripper_delay', '_tool_coordinate'
        ]
        
        for attr in attributes_to_check:
            if hasattr(robot_node, attr):
                value = getattr(robot_node, attr)
                print(f"   {attr}: {value}")
        
        # 如果有机器人设备，检查其参数
        if hasattr(robot_node, '_robot') and robot_node._robot:
            print("\n🤖 机器人设备参数:")
            robot_device = robot_node._robot
            
            device_attributes = [
                '_ip_addr', '_port', '_timeout', '_is_connected',
                '_rapid_rate', '_validate_rapid_rate', '_max_speed'
            ]
            
            for attr in device_attributes:
                if hasattr(robot_device, attr):
                    value = getattr(robot_device, attr)
                    print(f"   {attr}: {value}")
        
    except Exception as e:
        print(f"❌ 检查节点参数失败: {e}")


def modify_robot_node_parameters_example():
    """演示如何修改RobotNode参数的示例"""
    print("\n=== 修改RobotNode参数示例 ===")
    
    try:
        # 获取robot_node
        robot_node = get_robot_node("robot_1")
        
        if not robot_node:
            print("❌ 无法获取robot_node")
            return
        
        print("🔧 修改参数前的状态:")
        if hasattr(robot_node, '_robot') and robot_node._robot:
            robot_device = robot_node._robot
            print(f"   当前最大速度: {getattr(robot_device, '_max_speed', 'N/A')}")
            print(f"   当前快速运行速率: {getattr(robot_device, '_rapid_rate', 'N/A')}")
        
        # 注意：实际修改参数需要谨慎，这里只是演示
        print("\n⚠️ 注意：实际生产环境中修改参数需要谨慎！")
        print("   建议通过配置文件修改参数，然后重新初始化")
        
        # 演示如何通过配置文件修改参数
        print("\n📝 推荐的参数修改方式:")
        print("1. 修改 blade_inspection/configs/config.yaml 文件")
        print("2. 在NODES配置中找到对应的robot节点")
        print("3. 修改相应的参数值")
        print("4. 重新启动应用程序或重新初始化Robotics")
        
    except Exception as e:
        print(f"❌ 修改参数示例失败: {e}")


def main():
    """主函数"""
    print("🚀 RobotNode单例参数传入示例开始")
    print("=" * 60)
    
    try:
        # 1. 显示当前配置
        show_current_robot_config()
        
        # 2. 创建自定义参数示例
        custom_params = create_robot_node_with_custom_params()
        
        # 3. 演示Robotics初始化
        robotics = demonstrate_robotics_initialization_with_custom_config()
        
        # 4. 获取RobotNode单例
        robot_node = get_robot_node_singleton_example()
        
        # 5. 演示参数使用
        demonstrate_robot_node_parameters(robot_node)
        
        # 6. 演示参数修改
        modify_robot_node_parameters_example()
        
        print("\n" + "=" * 60)
        print("✅ RobotNode单例参数传入示例完成")
        
    except Exception as e:
        print(f"❌ 示例执行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
