#!/usr/bin/python
# -*- coding: utf-8 -*-

"""
robot_node获取和使用示例

这个文件展示了如何在blade_inspection工程中获取和使用robot_node实例
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from blade_inspection.utils import (
    get_robot_node,
    check_robot_node_status,
    get_all_nodes,
    get_robot_tcp_position,
    get_robot_tcp_position_simple
)
from hdmtv.core.node import Request


def example_get_robot_node():
    """示例：获取robot_node实例"""
    print("=== 获取robot_node实例示例 ===")
    
    # 方法1: 获取默认的robot_1节点
    robot_node = get_robot_node()
    if robot_node:
        print(f"✅ 成功获取robot_node: {type(robot_node).__name__}")
        return robot_node
    else:
        print("❌ 获取robot_node失败")
        return None


def example_use_robot_node(robot_node):
    """示例：使用robot_node实例"""
    if not robot_node:
        print("❌ robot_node为空，无法使用")
        return
    
    print("\n=== 使用robot_node实例示例 ===")
    
    try:
        # 示例1: 检查连接状态
        if hasattr(robot_node, 'is_connected'):
            print(f"机器人连接状态: {robot_node.is_connected}")
        
        # 示例2: 获取机器人信息
        if hasattr(robot_node, 'get_info'):
            request = Request(data={})
            response = robot_node.get_info(request)
            print(f"获取信息响应: {response}")
        
        # 示例3: 连接机器人
        if hasattr(robot_node, 'connect'):
            request = Request(data={})
            response = robot_node.connect(request)
            print(f"连接响应: {response}")
        
        # 示例4: 获取当前位置
        if hasattr(robot_node, 'get_tcp_position'):
            request = Request(data={})
            response = robot_node.get_tcp_position(request)
            print(f"当前位置: {response}")
        
        # 示例5: 获取关节位置
        if hasattr(robot_node, 'get_joint_position'):
            request = Request(data={})
            response = robot_node.get_joint_position(request)
            print(f"关节位置: {response}")
            
    except Exception as e:
        print(f"❌ 使用robot_node时发生错误: {e}")


def example_robot_movement(robot_node):
    """示例：机器人运动控制"""
    if not robot_node:
        print("❌ robot_node为空，无法进行运动控制")
        return
    
    print("\n=== 机器人运动控制示例 ===")
    
    try:
        # 示例1: 关节运动
        if hasattr(robot_node, 'joint_move'):
            joint_positions = [0, 0, 0, 0, 0, 0]  # 示例关节位置
            request = Request(data={
                "joint_pos": joint_positions,
                "speed": 50.0,
                "is_block": False,
                "move_mode": 0,
                "radian": False
            })
            response = robot_node.joint_move(request)
            print(f"关节运动响应: {response}")
        
        # 示例2: 直线运动
        if hasattr(robot_node, 'linear_move'):
            end_position = [100, 100, 100, 0, 0, 0]  # 示例目标位置
            request = Request(data={
                "end_pos": end_position,
                "speed": 30.0,
                "is_block": False,
                "move_mode": 1
            })
            response = robot_node.linear_move(request)
            print(f"直线运动响应: {response}")
            
    except Exception as e:
        print(f"❌ 机器人运动控制时发生错误: {e}")


def example_get_tcp_position():
    """示例：获取TCP位置"""
    print("\n=== 获取TCP位置示例 ===")

    try:
        # 方法1: 完整版本获取TCP位置
        print("方法1: 完整版本获取TCP位置")
        tcp_result = get_robot_tcp_position("robot_1")
        if tcp_result:
            print(f"TCP位置结果: {tcp_result}")
            if tcp_result.get("code") == 0:
                tcp_pos = tcp_result.get("tcp_position", [])
                if tcp_pos and len(tcp_pos) >= 6:
                    print(f"TCP坐标: x={tcp_pos[0]:.3f}, y={tcp_pos[1]:.3f}, z={tcp_pos[2]:.3f}")
                    print(f"TCP姿态: rx={tcp_pos[3]:.3f}, ry={tcp_pos[4]:.3f}, rz={tcp_pos[5]:.3f}")
            else:
                print(f"获取失败: {tcp_result.get('message', '未知错误')}")
        else:
            print("❌ 获取TCP位置失败")

        # 方法2: 简化版本获取TCP位置
        print("\n方法2: 简化版本获取TCP位置")
        tcp_pos = get_robot_tcp_position_simple()
        if tcp_pos:
            x, y, z, rx, ry, rz = tcp_pos
            print(f"✅ TCP位置: x={x:.3f}, y={y:.3f}, z={z:.3f}")
            print(f"✅ TCP姿态: rx={rx:.3f}, ry={ry:.3f}, rz={rz:.3f}")
        else:
            print("❌ 简化版本获取TCP位置失败")

    except Exception as e:
        print(f"❌ TCP位置获取示例失败: {e}")


def example_check_all_nodes():
    """示例：检查所有节点"""
    print("\n=== 检查所有节点示例 ===")

    # 获取所有节点
    all_nodes = get_all_nodes()

    if all_nodes:
        print(f"发现 {len(all_nodes)} 个节点:")
        for name, node in all_nodes.items():
            print(f"  - {name}: {type(node).__name__}")

            # 检查每个节点的状态
            if "robot" in name.lower():
                status = check_robot_node_status(name)
                print(f"    状态: {status}")
    else:
        print("❌ 未发现任何节点")


def main():
    """主函数"""
    print("🚀 robot_node获取和使用示例开始")
    
    try:
        # 1. 获取robot_node实例
        robot_node = example_get_robot_node()
        
        # 2. 使用robot_node实例
        example_use_robot_node(robot_node)
        
        # 3. 获取TCP位置示例
        example_get_tcp_position()

        # 4. 机器人运动控制示例
        example_robot_movement(robot_node)

        # 5. 检查所有节点
        example_check_all_nodes()

        # 6. 检查robot_node状态
        print("\n=== 检查robot_node状态 ===")
        status = check_robot_node_status("robot_1")
        print(f"robot_1状态: {status}")
        
    except Exception as e:
        print(f"❌ 示例执行失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n✅ robot_node示例执行完成")


if __name__ == "__main__":
    main()
