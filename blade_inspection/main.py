#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import sys
import json
import os
from datetime import datetime
from PyQt5 import QtWidgets, uic, QtGui
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QPixmap
from PyQt5.QtWidgets import (QMessageBox, QFileDialog,
                             QListWidgetItem, QProgressDialog)
from templates.blade_dialogs import DynamicTabWidget
from templates.recipe_import_module import RecipeImportModule


class BladeInsightMainWindow(QtWidgets.QMainWindow):
    def __init__(self):
        super().__init__()
        self.dynamic_tab_widget = None
        self.recipe_storage_path = None
        self.recipe_table = None
        self.program_table = None
        self.task_queue_table = None
        self.content_stack = None
        # 初始化数据存储
        self.recipe_data_list = []  # 存储所有配方数据
        self.hidden_recipes = set()  # 存储被隐藏的配方名称
        self.recipes_data_file = "recipes_data.json"  # 配方数据持久化文件
        self.program_data_list = []  # 存储程序编辑列表数据
        self.programs_data_file = "programs_data.json"  # 程序数据持久化文件

        # 机器人程序步骤控制相关属性已迁移到 program_module.py

        # 初始化校准模块
        from templates.calibration_module import CalibrationModule
        self.calibration_module = CalibrationModule(self)

        # 初始化程序编辑模块
        from templates.program_module import ProgramModule
        self.program_module = ProgramModule(self)

        # 初始化任务管理模块
        from templates.task_management_module import TaskManagementModule
        self.task_management_module = TaskManagementModule(self)

        # 初始化界面顶部模块
        from templates.header_modules import HeaderModulesManager
        self.header_modules = HeaderModulesManager(self)

        try:
            # 初始化配方导入模块
            self.recipe_import_module = RecipeImportModule(self)

            # 保持向后兼容性
            self.cache_path = self.recipe_import_module.cache_path
            self.recipe_storage_path = self.recipe_import_module.recipe_storage_path

            # 加载UI文件
            ui_path = "templates/blade.ui"
            if not os.path.exists(ui_path):
                # 尝试其他可能的路径
                alternative_paths = [
                    "blade_inspection/templates/blade.ui",
                    "blade.ui",
                    "blade_inspection/blade.ui"
                ]
                for alt_path in alternative_paths:
                    if os.path.exists(alt_path):
                        ui_path = alt_path
                        break
                else:
                    raise FileNotFoundError(f"UI文件不存在，已尝试路径: {[ui_path] + alternative_paths}")

            uic.loadUi(ui_path, self)

            # 初始化UI组件
            self.init_ui_components()

            # 连接信号槽
            self.connect_signals()

            # 设置默认状态
            self.setup_default_state()

            # 加载配方数据
            self.load_recipes_from_storage()

            # 机器人控制初始化已迁移到 program_module.py

        except Exception as e:
            print(f"初始化失败: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(None, "初始化错误", f"应用程序初始化失败:\n{e}")



    def init_ui_components(self):
        """初始化UI组件"""
        try:
            # 顶部按钮
            self.hardware_config_btn = self.findChild(QtWidgets.QPushButton, "hardware_config_btn")
            self.operator_btn = self.findChild(QtWidgets.QPushButton, "operator_btn")
            self.connect_status = self.findChild(QtWidgets.QPushButton, "connect_status")
            self.alarm_btn = self.findChild(QtWidgets.QPushButton, "alarm_btn")
            self.monitor_btn = self.findChild(QtWidgets.QPushButton, "monitor_btn")

            # 左侧功能按钮
            self.recipe_import_btn = self.findChild(QtWidgets.QPushButton, "recipe_import_btn")
            self.program_edit_btn = self.findChild(QtWidgets.QPushButton, "program_edit_btn")
            self.detection_task_btn = self.findChild(QtWidgets.QPushButton, "detection_task_btn")
            self.device_calibrate_btn = self.findChild(QtWidgets.QPushButton, "device_calibrate_btn")

            # 主要内容区域
            self.content_stack = self.findChild(QtWidgets.QStackedWidget, "content_stack")
            self.tabs_container = self.findChild(QtWidgets.QWidget, "tabs_container")

            # 配方相关组件
            self.recipe_table = self.findChild(QtWidgets.QTableWidget, "recipe_table")

            if self.recipe_table and self.recipe_table.columnCount() == 0:
                self.recipe_table.setColumnCount(11)
                headers = ["任务名", "产品名称", "批号", "工单名称", "产线名称", "订单名称", "工序名称", "任务数量",
                           "上传时间", "上传人", "操作"]
                self.recipe_table.setHorizontalHeaderLabels(headers)

            # 配方导入按钮
            self.recipe_add_btn = self.findChild(QtWidgets.QPushButton, "recipe_add_btn")
            self.recipe_back_btn = self.findChild(QtWidgets.QPushButton, "recipe_back_btn")

            # 程序编辑相关组件
            self.program_table = self.findChild(QtWidgets.QTableWidget, "program_table")
            self.program_add_btn = self.findChild(QtWidgets.QPushButton, "program_add_btn")
            self.program_save_btn = self.findChild(QtWidgets.QPushButton, "program_save_btn")
            self.program_cancel_btn = self.findChild(QtWidgets.QPushButton, "program_cancel_btn")
            self.program_jog_btn = self.findChild(QtWidgets.QPushButton, "program_jog_btn")
            self.program_content_edit = self.findChild(QtWidgets.QTextEdit, "program_content_edit")
            self.program_tab_widget = self.findChild(QtWidgets.QTabWidget, "program_tab_widget")

            # 隐藏程序编辑模块的工具栏，让表单顶格显示
            self.program_list_toolbar = self.findChild(QtWidgets.QWidget, "program_list_toolbar")
            if self.program_list_toolbar:
                self.program_list_toolbar.setVisible(False)
                print("✅ 已隐藏程序编辑模块的工具栏，表单现在顶格显示")
            elif self.program_add_btn:
                # 如果找不到工具栏，就只隐藏新增按钮
                self.program_add_btn.setVisible(False)
                print("✅ 已隐藏程序编辑模块的新增按钮")

            # 优化表单操作栏：只保留编辑按钮，隐藏其他按钮
            if self.program_save_btn:
                self.program_save_btn.setVisible(False)
                print("✅ 已隐藏程序编辑的保存按钮")
            if self.program_cancel_btn:
                # 将取消按钮改为关闭编辑按钮
                self.program_cancel_btn.setText("关闭编辑")
                self.program_cancel_btn.setVisible(True)
                print("✅ 已将取消按钮改为关闭编辑按钮")
            if self.program_jog_btn:
                self.program_jog_btn.setVisible(False)
                print("✅ 已隐藏程序编辑的点动按钮")

            # 检测任务相关组件
            self.part_number_edit = self.findChild(QtWidgets.QLineEdit, "part_number_edit")
            self.part_number_select_btn = self.findChild(QtWidgets.QPushButton, "part_number_select_btn")
            self.program_number_combo = self.findChild(QtWidgets.QComboBox, "program_number_combo")
            self.task_number_edit = self.findChild(QtWidgets.QLineEdit, "task_number_edit")
            self.task_info_edit = self.findChild(QtWidgets.QTextEdit, "task_info_edit")
            self.task_start_btn = self.findChild(QtWidgets.QPushButton, "task_start_btn")
            self.task_status_btn = self.findChild(QtWidgets.QPushButton, "task_status_btn")
            self.task_tab_widget = self.findChild(QtWidgets.QTabWidget, "task_tab_widget")
            self.task_queue_table = self.findChild(QtWidgets.QTableWidget, "task_queue_table")

            # 任务详情相关组件
            self.task_detail_tab = self.findChild(QtWidgets.QWidget, "task_detail_tab")
            self.untitled_ui_container = self.findChild(QtWidgets.QWidget, "untitled_ui_container")
            self.untitled_placeholder = self.findChild(QtWidgets.QLabel, "untitled_placeholder")

            # 设备校准按钮
            self.sensor1_calibrate_btn = self.findChild(QtWidgets.QPushButton, "sensor1_calibrate_btn")
            self.sensor2_calibrate_btn = self.findChild(QtWidgets.QPushButton, "sensor2_calibrate_btn")
            self.sensor3_calibrate_btn = self.findChild(QtWidgets.QPushButton, "sensor3_calibrate_btn")
            self.camera_calibrate_btn = self.findChild(QtWidgets.QPushButton, "camera_calibrate_btn")
            self.robot_calibrate_btn = self.findChild(QtWidgets.QPushButton, "robot_calibrate_btn")
            self.gripper_calibrate_btn = self.findChild(QtWidgets.QPushButton, "gripper_calibrate_btn")
            self.auto_calibrate_btn = self.findChild(QtWidgets.QPushButton, "auto_calibrate_btn")

            # 打印调试信息
            print(f"task_tab_widget: {self.task_tab_widget}")
            print(f"task_detail_tab: {self.task_detail_tab}")
            print(f"untitled_ui_container: {self.untitled_ui_container}")
            self.optimize_main_layout()

        except Exception as e:
            print(f"初始化UI组件失败: {e}")
            import traceback
            traceback.print_exc()

    # init_robot_control 方法已迁移到 program_module.py

    # connect_robot_control_signals 方法已迁移到 program_module.py

    # init_robot_program_steps 方法已迁移到 program_module.py

    # update_step_highlight 和 update_step_display 方法已迁移到 program_module.py

    # connect_resize_events, update_robot_control_layout, connect_container_resize 方法已迁移到 program_module.py

    def connect_signals(self):
        """连接信号槽"""
        # 界面顶部模块信号连接
        if hasattr(self, 'header_modules'):
            self.header_modules.setup_header_signals()
        else:
            if self.hardware_config_btn:
                self.hardware_config_btn.clicked.connect(self.show_hardware_config)
            if self.operator_btn:
                self.operator_btn.clicked.connect(self.show_operator_login)
            if self.connect_status:
                self.connect_status.clicked.connect(lambda: QMessageBox.information(self, "连接状态", "连接正常"))
            if self.alarm_btn:
                self.alarm_btn.clicked.connect(self.show_alarm_info)
            if self.monitor_btn:
                self.monitor_btn.clicked.connect(self.show_real_time_monitor)

            # 左侧功能按钮信号
        if self.recipe_import_btn:
            self.recipe_import_btn.clicked.connect(lambda: self.switch_page(1))
        if self.program_edit_btn:
            self.program_edit_btn.clicked.connect(lambda: self.switch_page(2))
        if self.detection_task_btn:
            self.detection_task_btn.clicked.connect(lambda: self.switch_page(3))
        if self.device_calibrate_btn:
            self.device_calibrate_btn.clicked.connect(lambda: self.switch_page(4))

            # 配方导入按钮信号
        if self.recipe_add_btn:
            self.setup_recipe_add_menu()
        if self.recipe_back_btn:
            self.recipe_back_btn.clicked.connect(lambda: self.switch_page(0))

        # 程序编辑按钮信号 - 使用程序模块
        if hasattr(self, 'program_module'):
            if self.program_save_btn:
                self.program_save_btn.clicked.connect(self.program_module.save_program)
            if self.program_cancel_btn:
                self.program_cancel_btn.clicked.connect(self.close_program_edit)
        if self.program_jog_btn:
            self.program_jog_btn.clicked.connect(self.open_jog_control)

        # 检测任务信号 - 使用任务管理模块
        if hasattr(self, 'task_management_module'):
            if self.program_number_combo:
                self.program_number_combo.currentTextChanged.connect(self.task_management_module.on_program_selected)
            if self.part_number_select_btn:
                self.part_number_select_btn.clicked.connect(self.task_management_module.select_task_folder)
            if self.task_start_btn:
                self.task_start_btn.clicked.connect(self.task_management_module.start_task)
            if self.task_status_btn:
                self.task_status_btn.clicked.connect(self.task_management_module.show_task_status)

        # 设备校准按钮信号 - 使用校准模块
        if hasattr(self, 'calibration_module'):
            self.calibration_module.setup_calibration_signals()

    def init_recipe_table(self):
        """初始化配方表格"""
        try:
            if not self.recipe_table:
                return

            # 确保表格有正确的列数和标题
            if self.recipe_table.columnCount() != 11:
                self.recipe_table.setColumnCount(11)
                headers = ["任务名", "产品名称", "批号", "工单名称", "产线名称", "订单名称", "工序名称", "任务数量",
                           "上传时间", "上传人", "操作"]
                self.recipe_table.setHorizontalHeaderLabels(headers)

            # 设置列宽 - 恢复响应式布局
            header = self.recipe_table.horizontalHeader()
            if header:
                # 设置前10列为拉伸模式，自动适应窗口大小
                for i in range(10):
                    header.setSectionResizeMode(i, QtWidgets.QHeaderView.Stretch)

                # 最后一列（操作列）设置为固定宽度
                header.setSectionResizeMode(10, QtWidgets.QHeaderView.Fixed)
                self.recipe_table.setColumnWidth(10, 200)  # 操作列固定宽度

            # 设置表格属性
            self.recipe_table.setAlternatingRowColors(True)
            self.recipe_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
            self.recipe_table.verticalHeader().setVisible(False)

            # 禁用表格编辑
            self.recipe_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)

            # 立即加载配方数据
            self.load_recipes_from_storage()

        except Exception as e:
            print(f"初始化配方表格失败: {e}")
            import traceback
            traceback.print_exc()

    def load_recipes_from_storage(self):
        """从本地存储加载配方"""
        try:
            self.refresh_recipe_table()
        except Exception as e:
            print(f"加载配方失败: {e}")
            import traceback
            traceback.print_exc()

    def refresh_recipe_table(self):
        """刷新配方表格显示 - 使用配方导入模块"""
        if not self.recipe_table:
            return

        try:
            # 使用配方导入模块加载配方
            all_recipes = self.recipe_import_module.load_recipes()
            # 使用模块的表格更新功能
            self.recipe_import_module.update_recipe_table(all_recipes)

        except Exception as e:
            print(f"刷新配方表格失败: {e}")
            # 显示错误信息
            error_recipes = [
                {
                    "name": "加载失败",
                    "product_name": f"错误: {str(e)}",
                    "lot_sn": "请检查配置",
                    "mo_name": "请检查配置",
                    "product_line_name": "请检查配置",
                    "order_name": "请检查配置",
                    "specification_name": "请检查配置",
                    "task_qty": "0",
                    "create_time": "错误",
                    "operator": "系统"
                }
            ]
            self.recipe_import_module.update_recipe_table(error_recipes)

    def refresh_program_table(self):
        """刷新程序表格显示 - 使用程序模块"""
        if not self.program_table:
            return

        try:
            # 使用程序模块刷新表格
            if hasattr(self, 'program_module'):
                self.program_module.load_programs_data()
                self.program_module.update_program_table()

        except Exception as e:
            print(f"刷新程序表格失败: {e}")

    def delete_recipe(self, recipe_name):
        """删除配方（提供多种删除选项）"""
        # 创建自定义对话框
        dialog = QMessageBox(self)
        dialog.setWindowTitle("删除配方")
        dialog.setText(f"请选择删除配方 '{recipe_name}' 的方式：")
        dialog.setInformativeText("• 仅删除队列：只从表格中移除，保留源文件\n• 源文件也删除：从表格和文件系统中彻底删除")

        # 添加自定义按钮
        cancel_btn = dialog.addButton("取消", QMessageBox.RejectRole)
        queue_only_btn = dialog.addButton("仅删除队列", QMessageBox.ActionRole)
        delete_file_btn = dialog.addButton("源文件也删除", QMessageBox.DestructiveRole)

        # 设置按钮样式
        cancel_btn.setStyleSheet("QPushButton { background-color: #95a5a6; color: white; padding: 8px 16px; }")
        queue_only_btn.setStyleSheet("QPushButton { background-color: #f39c12; color: white; padding: 8px 16px; }")
        delete_file_btn.setStyleSheet("QPushButton { background-color: #e74c3c; color: white; padding: 8px 16px; }")

        # 设置默认按钮
        dialog.setDefaultButton(cancel_btn)

        # 显示对话框并获取结果
        dialog.exec_()
        clicked_button = dialog.clickedButton()

        if clicked_button == cancel_btn:
            # 用户取消操作
            return
        elif clicked_button == queue_only_btn:
            # 仅从队列中删除
            self.delete_from_queue_only(recipe_name)
            # 同时删除对应的程序
            self.delete_programs_by_recipe(recipe_name)
        elif clicked_button == delete_file_btn:
            # 同时删除源文件
            self.delete_with_source_file(recipe_name)
            # 同时删除对应的程序
            self.delete_programs_by_recipe(recipe_name)

    def delete_from_queue_only(self, recipe_name):
        """仅从队列中删除配方"""
        try:
            # 从内存数据列表中删除
            recipe_deleted = False
            for i, recipe in enumerate(self.recipe_data_list):
                if recipe["name"] == recipe_name:
                    del self.recipe_data_list[i]
                    recipe_deleted = True
                    break

            if recipe_deleted:
                # 检查文件系统中是否存在同名文件
                file_exists_in_system = False
                if hasattr(self, 'recipe_storage_path') and self.recipe_storage_path:
                    file_system_path = os.path.join(self.recipe_storage_path, recipe_name)
                    file_exists_in_system = os.path.exists(file_system_path)

                if file_exists_in_system and recipe_name in self.hidden_recipes:
                    # 如果原文件已被隐藏（说明是覆盖上传的），直接删除不询问
                    QMessageBox.information(self, "删除成功",
                                            f"配方 '{recipe_name}' 已从队列中删除\n原始文件保持隐藏状态")
                elif file_exists_in_system:
                    # 如果文件系统中存在同名文件且未被隐藏，询问用户是否要显示原文件
                    reply = QMessageBox.question(
                        self,
                        "发现原文件",
                        f"检测到文件系统中存在原始配方文件 '{recipe_name}'。\n\n"
                        f"是否要在表格中显示原始文件？\n\n"
                        f"• 是：显示原始文件内容\n"
                        f"• 否：完全隐藏该配方",
                        QMessageBox.Yes | QMessageBox.No,
                        QMessageBox.No
                    )

                    if reply == QMessageBox.No:
                        # 用户选择不显示原文件，将其添加到隐藏列表
                        self.hidden_recipes.add(recipe_name)
                        QMessageBox.information(self, "删除成功",
                                                f"配方 '{recipe_name}' 已从队列中删除\n原始文件已隐藏")
                    else:
                        # 用户选择显示原文件，从隐藏列表中移除（如果存在）
                        self.hidden_recipes.discard(recipe_name)
                        QMessageBox.information(self, "删除成功",
                                                f"配方 '{recipe_name}' 已从队列中删除\n将显示原始文件内容")
                else:
                    QMessageBox.information(self, "删除成功", f"配方 '{recipe_name}' 已从队列中删除\n无原始文件")
            else:
                # 如果不在内存列表中，添加到隐藏列表
                self.hidden_recipes.add(recipe_name)
                QMessageBox.information(self, "删除成功", f"配方 '{recipe_name}' 已从队列中隐藏\n源文件已保留")

            # 刷新表格显示
            self.refresh_recipe_table()

        except Exception as e:
            QMessageBox.warning(self, "删除失败", f"从队列删除配方失败: {e}")

    def delete_with_source_file(self, recipe_name):
        """删除配方并删除源文件"""
        try:
            # 先从队列中删除
            queue_deleted = False
            for i, recipe in enumerate(self.recipe_data_list):
                if recipe["name"] == recipe_name:
                    del self.recipe_data_list[i]
                    queue_deleted = True
                    break

            # 如果不在内存中，从隐藏列表中移除
            if not queue_deleted:
                self.hidden_recipes.discard(recipe_name)

            # 尝试删除源文件
            file_deleted = False
            if hasattr(self, 'recipe_storage_path') and self.recipe_storage_path:
                file_path = os.path.join(self.recipe_storage_path, recipe_name)
                if os.path.exists(file_path):
                    os.remove(file_path)
                    file_deleted = True

            # 显示删除结果
            if queue_deleted and file_deleted:
                QMessageBox.information(self, "删除成功", f"配方 '{recipe_name}' 已从队列和文件系统中彻底删除")
            elif queue_deleted:
                QMessageBox.information(self, "删除成功",
                                        f"配方 '{recipe_name}' 已从队列中删除\n源文件未找到或已不存在")
            elif file_deleted:
                QMessageBox.information(self, "删除成功", f"配方 '{recipe_name}' 的源文件已删除\n已从队列中移除")
            else:
                QMessageBox.information(self, "删除成功", f"配方 '{recipe_name}' 已从队列中移除\n源文件未找到")

            # 刷新表格显示
            self.refresh_recipe_table()

        except Exception as e:
            QMessageBox.warning(self, "删除失败", f"删除配方和源文件失败: {e}")

    def setup_recipe_add_menu(self):
        """设置配方新增按钮（移除上传功能，只保留新增）"""
        if not self.recipe_add_btn:
            return
        # 直接连接到配方导入模块的新增功能
        self.recipe_add_btn.clicked.connect(self.add_new_recipe)

    def add_new_recipe(self):
        """新增配方 - 调用配方导入模块"""
        try:
            # 调用配方导入模块的新增功能
            success = self.recipe_import_module.add_new_recipe()

            if success:
                # 刷新配方表格
                self.refresh_recipe_table()

        except Exception as e:
            QMessageBox.warning(self, "新增失败", f"新增配方失败: {e}")
            print(f"新增配方失败: {e}")
            import traceback
            traceback.print_exc()

    def upload_recipe(self):
        """上传task_info.json配方文件"""
        # 确保recipes文件夹存在
        recipes_dir = os.path.join(os.getcwd(), "recipes")
        if not os.path.exists(recipes_dir):
            os.makedirs(recipes_dir)
            print(f"创建recipes文件夹: {recipes_dir}")

        default_path = r"D:\hd_automation\task\叶片检测任务"
        if not os.path.exists(default_path):
            default_path = os.getcwd()

        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择task_info.json配方文件",
            default_path,
            "task_info.json文件 (task_info.json);;JSON文件 (*.json);;所有文件 (*.*)"
        )
        if file_path:
            try:
                # 验证是否为task_info.json文件
                filename = os.path.basename(file_path)
                if filename != "task_info.json":
                    QMessageBox.warning(self, "文件类型错误",
                                        f"请选择task_info.json文件！\n\n当前选择的文件: {filename}")
                    return
                # 将文件复制到recipes文件夹
                import shutil
                recipes_dir = os.path.join(os.getcwd(), "recipes")
                target_file_path = os.path.join(recipes_dir, filename)

                # 如果目标文件已存在，询问是否覆盖
                if os.path.exists(target_file_path):
                    reply = QMessageBox.question(self, "文件已存在",
                                                 f"recipes文件夹中已存在 {filename}，是否覆盖？",
                                                 QMessageBox.Yes | QMessageBox.No)
                    if reply != QMessageBox.Yes:
                        return

                # 复制文件
                shutil.copy2(file_path, target_file_path)
                print(f"文件已复制到: {target_file_path}")

                # 读取复制后的文件
                with open(target_file_path, 'r', encoding='utf-8') as f:
                    task_info_data = json.load(f)

                # 解析task_info.json内容（使用复制后的文件路径）
                recipe_data = self.parse_task_info(task_info_data, target_file_path)
                recipe_name = recipe_data.get('recipe_name', 'task_info')
                recipe_file_name = f"{recipe_name}.json"

                # 检查是否已存在同名配方
                recipe_exists = False

                # 检查内存中是否存在
                for recipe in self.recipe_data_list:
                    if recipe["name"] == recipe_file_name:
                        recipe_exists = True
                        break

                # 检查文件系统中是否存在（且未被隐藏）
                if not recipe_exists and hasattr(self, 'recipe_storage_path') and self.recipe_storage_path:
                    file_system_path = os.path.join(self.recipe_storage_path, recipe_file_name)
                    if os.path.exists(file_system_path) and recipe_file_name not in self.hidden_recipes:
                        recipe_exists = True

                # 如果配方已存在，询问是否覆盖
                if recipe_exists:
                    reply = QMessageBox.question(
                        self,
                        "配方已存在",
                        f"配方 '{recipe_name}' 已存在。\n\n是否要覆盖现有配方？\n\n"
                        f"• 是：覆盖现有配方，完全替换\n"
                        f"• 否：取消上传操作",
                        QMessageBox.Yes | QMessageBox.No,
                        QMessageBox.No
                    )

                    if reply != QMessageBox.Yes:
                        return  # 用户选择不覆盖，取消上传

                # 添加到配方表格（如果是覆盖，会完全替换）
                self.add_recipe_to_table(recipe_name, file_path, recipe_data, overwrite=recipe_exists)

                if recipe_exists:
                    QMessageBox.information(self, "覆盖成功",
                                            f"配方 '{recipe_name}' 已成功覆盖！\n\n文件: {os.path.basename(file_path)}")
                else:
                    QMessageBox.information(self, "上传成功",
                                            f"配方文件上传成功！\n\n文件: {os.path.basename(file_path)}\n配方名称: {recipe_name}")

            except json.JSONDecodeError as e:
                QMessageBox.critical(self, "文件格式错误", f"JSON文件格式错误：\n{str(e)}")
            except Exception as e:
                QMessageBox.critical(self, "上传失败", f"文件上传失败：\n{str(e)}")

    def parse_task_info(self, task_info_data, file_path):
        """解析task_info.json文件内容"""
        try:
            # 获取文件名
            file_name = os.path.basename(file_path)

            # 提取基本信息
            product_line_name = task_info_data.get("ProductLineName", "未知产线")
            mo_task_name = task_info_data.get("MOTaskName", "未知任务")
            mo_name = task_info_data.get("MOName", "未知工单")
            site_code = task_info_data.get("SiteCode", "未知站点")
            order_name = task_info_data.get("OrderName", "未知订单")
            product_name = task_info_data.get("ProductName", "未知产品")
            lot_sn = task_info_data.get("LotSN", "未知批号")
            specification_name = task_info_data.get("SpecificationName", "未知工序")
            mo_type = task_info_data.get("MOType", "0")
            task_qty = task_info_data.get("TaskQty", 0)

            # 查找关联的程序文件
            program_files = self.find_associated_program_files(task_info_data, file_path)

            # 查找程序文件
            program_file = None
            file_dir = os.path.dirname(file_path)
            for filename in os.listdir(file_dir):
                if filename.endswith('.json') and filename != 'task_info.json':
                    # 检查是否符合程序文件格式（如S88.88.8888.json）
                    if '.' in filename and filename.count('.') >= 3:
                        program_file = filename
                        break

            if not program_file:
                program_file = "未找到程序文件"

            # 构建配方数据
            recipe_data = {
                'recipe_name': f"{product_name}_{lot_sn}",
                'task_file_name': file_name,  # 添加任务文件名
                'task_info': task_info_data,
                'product_line_name': product_line_name,
                'mo_task_name': mo_task_name,
                'mo_name': mo_name,
                'site_code': site_code,
                'order_name': order_name,
                'product_name': product_name,
                'lot_sn': lot_sn,
                'specification_name': specification_name,
                'mo_type': mo_type,
                'task_qty': task_qty,
                'program_file': program_file,
                'program_files': program_files,  # 添加关联的程序文件列表
                'file_directory': file_dir
            }

            return recipe_data

        except Exception as e:
            print(f"解析task_info.json失败: {e}")
            return {
                'recipe_name': 'task_info_error',
                'error': str(e),
                'raw_data': task_info_data
            }

    def find_associated_program_files(self, task_info_data, task_file_path):
        """查找与task_info.json关联的程序文件"""
        try:
            program_files = []

            # 获取task_info.json所在的目录
            task_dir = os.path.dirname(task_file_path)

            # 从task_info.json中获取程序文件信息
            program_files_info = task_info_data.get("ProgramFiles", [])

            for program_info in program_files_info:
                program_name = program_info.get("ProgramName", "")
                program_file_url = program_info.get("ProgramFileURL", "")

                if program_name and program_file_url:
                    # 构建程序文件的完整路径
                    program_file_path = os.path.join(task_dir, program_file_url)

                    # 检查文件是否存在
                    if os.path.exists(program_file_path):
                        program_files.append({
                            'name': program_name,
                            'file': program_file_url,
                            'path': program_file_path,
                            'equipment': program_info.get("EquipmentName", "")
                        })
                        print(f"找到关联程序文件: {program_file_url}")
                    else:
                        print(f"程序文件不存在: {program_file_path}")

            return program_files

        except Exception as e:
            print(f"查找关联程序文件失败: {e}")
            return []

    def add_recipe_to_table(self, recipe_name, file_path, recipe_data, overwrite=False):
        """将上传的配方添加到数据列表并刷新表格"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M")
        recipe_file_name = f"{recipe_name}.json"

        # 如果是task_info数据，使用详细信息
        if 'task_info' in recipe_data:
            display_name = f"{recipe_data.get('product_name', '未知产品')} - {recipe_data.get('lot_sn', '未知批号')}"
            count_info = f"工单:{recipe_data.get('mo_name', '未知')}"
            ip_info = f"产线:{recipe_data.get('product_line_name', '未知')}"

            recipe_info = {
                "name": display_name,
                "count": count_info,
                "ip": ip_info,
                "create_time": current_time,
                "operator": "当前用户",
                "file_path": file_path,
                "data": recipe_data,
                # 额外的task_info字段
                "product_line_name": recipe_data.get('product_line_name', ''),
                "mo_task_name": recipe_data.get('mo_task_name', ''),
                "mo_name": recipe_data.get('mo_name', ''),
                "site_code": recipe_data.get('site_code', ''),
                "order_name": recipe_data.get('order_name', ''),
                "product_name": recipe_data.get('product_name', ''),
                "lot_sn": recipe_data.get('lot_sn', ''),
                "specification_name": recipe_data.get('specification_name', ''),
                "mo_type": recipe_data.get('mo_type', ''),
                "task_qty": recipe_data.get('task_qty', 0),
                "task_file_name": recipe_data.get('task_file_name', ''),
                "program_file": recipe_data.get('program_file', ''),
                "program_files": recipe_data.get('program_files', []),
                "file_directory": recipe_data.get('file_directory', '')
            }
        else:
            # 传统配方数据格式
            recipe_info = {
                "name": recipe_file_name,
                "count": str(len(recipe_data.get('items', []))) if 'items' in recipe_data else "未知",
                "ip": "本地上传",
                "create_time": current_time,
                "operator": "当前用户",
                "file_path": file_path,
                "data": recipe_data
            }

        if overwrite:
            # 覆盖模式：完全替换原配方
            # 1. 从内存中删除原配方（如果存在）
            for i, recipe in enumerate(self.recipe_data_list):
                if recipe["name"] == recipe_file_name:
                    del self.recipe_data_list[i]
                    break

            # 2. 将文件系统中的同名文件永久标记为隐藏
            if hasattr(self, 'recipe_storage_path') and self.recipe_storage_path:
                file_system_path = os.path.join(self.recipe_storage_path, recipe_file_name)
                if os.path.exists(file_system_path):
                    self.hidden_recipes.add(recipe_file_name)

            # 3. 添加新的配方数据
            self.recipe_data_list.append(recipe_info)

        else:
            # 正常添加模式
            # 检查是否已存在同名配方
            existing_index = -1
            for i, recipe in enumerate(self.recipe_data_list):
                if recipe["name"] == recipe_file_name:
                    existing_index = i
                    break

            if existing_index >= 0:
                # 更新现有配方
                self.recipe_data_list[existing_index] = recipe_info
            else:
                # 添加新配方
                self.recipe_data_list.append(recipe_info)

            # 如果文件系统中存在同名文件，将其添加到隐藏列表
            if hasattr(self, 'recipe_storage_path') and self.recipe_storage_path:
                file_system_path = os.path.join(self.recipe_storage_path, recipe_file_name)
                if os.path.exists(file_system_path):
                    self.hidden_recipes.add(recipe_file_name)

        # 自动保存配方数据
        self.save_recipes_data()

        # 添加关联的程序文件到程序编辑列表
        self.add_programs_from_recipe(recipe_data)

        # 刷新表格显示
        self.refresh_recipe_table()

    def save_recipes_data(self):
        """保存配方数据到文件"""
        try:
            data = {
                'recipes': self.recipe_data_list,
                'hidden_recipes': list(self.hidden_recipes)
            }
            with open(self.recipes_data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print("✅ 配方数据已保存")
        except Exception as e:
            print(f"保存配方数据失败: {e}")

    def load_recipes_data(self):
        """从文件加载配方数据"""
        try:
            if not os.path.exists(self.recipes_data_file):
                print("配方数据文件不存在，跳过加载")
                return

            with open(self.recipes_data_file, 'r', encoding='utf-8') as f:
                save_data = json.load(f)

            # 恢复配方数据
            self.recipe_data_list = save_data.get('recipes', [])
            self.hidden_recipes = set(save_data.get('hidden_recipes', []))
            last_updated = save_data.get('last_updated', '未知')

            print(f"已加载 {len(self.recipe_data_list)} 个配方数据，最后更新时间: {last_updated}")

            # 更新表格显示
            if self.recipe_data_list:
                self.recipe_import_module.update_recipe_table(self.recipe_data_list)

        except Exception as e:
            print(f"加载配方数据失败: {e}")
            self.recipe_data_list = []

    def add_programs_from_recipe(self, recipe_data):
        """从配方数据中添加程序到程序编辑列表 - 读取指定目录下的JSON文件"""
        try:
            if not recipe_data:
                return

            # 获取配方文件夹路径
            folder_path = recipe_data.get('folder_path', '')
            if not folder_path or not os.path.exists(folder_path):
                print(f"配方文件夹路径不存在: {folder_path}")
                return

            print(f"🎯 开始从配方文件夹读取JSON文件: {folder_path}")

            # 扫描文件夹中的所有JSON文件
            json_files = []
            for file_name in os.listdir(folder_path):
                if file_name.endswith('.json'):
                    json_path = os.path.join(folder_path, file_name)
                    json_files.append((file_name, json_path))
                    print(f"   发现JSON文件: {file_name}")

            if len(json_files) < 2:
                print(f"文件夹中JSON文件数量不足: {len(json_files)}，需要至少2个JSON文件")
                return

            # 找到task_info.json和另一个JSON文件
            task_info_file = None
            program_json_file = None

            for file_name, file_path in json_files:
                if file_name == 'task_info.json':
                    task_info_file = (file_name, file_path)
                else:
                    program_json_file = (file_name, file_path)

            if not task_info_file:
                print("未找到task_info.json文件")
                return

            if not program_json_file:
                print("未找到程序配置JSON文件")
                return

            print(f"✅ 找到配方文件: {task_info_file[0]}")
            print(f"✅ 找到程序文件: {program_json_file[0]}")

            # 读取程序JSON文件内容
            try:
                with open(program_json_file[1], 'r', encoding='utf-8') as f:
                    program_data = json.load(f)

                # 检查程序是否已经存在
                existing_program = any(p.get('file_name') == program_json_file[0] for p in self.program_data_list)
                if existing_program:
                    print(f"程序 {program_json_file[0]} 已存在，跳过添加")
                    return

                current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                program_name = recipe_data.get('task_name', '未知任务')
                equipment = recipe_data.get('equipment', '未知设备')

                # 创建新的程序数据
                new_program = {
                    "program_type": "检测程序",
                    "recipe_name": program_name,
                    "program_id": f"P{len(self.program_data_list) + 1:03d}",
                    "description": f"{program_name} - 从配方导入",
                    "file_name": program_json_file[0],
                    "file_path": program_json_file[1],
                    "equipment": equipment,
                    "created_time": current_time,
                    "source": "recipe_import",
                    "program_content": program_data  # 保存JSON内容
                }

                # 添加到程序列表
                self.program_data_list.append(new_program)
                print(f"✅ 已添加程序: {program_json_file[0]} 到程序编辑列表")

                # 保存程序数据
                if hasattr(self, 'program_module'):
                    self.program_module.save_programs_data()
                    # 更新程序表格显示
                    self.program_module.update_program_table()

                print(f"🎉 成功从配方导入程序文件: {program_json_file[0]}")

            except Exception as e:
                print(f"读取程序JSON文件失败 {program_json_file[1]}: {e}")

        except Exception as e:
            print(f"从配方添加程序失败: {e}")
            import traceback
            traceback.print_exc()

    def delete_programs_by_recipe(self, recipe_name):
        """根据配方名称删除对应的程序"""
        try:
            print(f"🎯 开始删除配方 '{recipe_name}' 对应的程序")

            # 查找并删除对应的程序
            programs_to_delete = []
            for i, program in enumerate(self.program_data_list):
                if (program.get('recipe_name') == recipe_name or
                    program.get('source') == 'recipe_import'):
                    # 检查是否是从这个配方导入的程序
                    if program.get('recipe_name') == recipe_name:
                        programs_to_delete.append(i)
                        print(f"   找到对应程序: {program.get('file_name', '未知文件')}")

            # 从后往前删除，避免索引变化问题
            for i in reversed(programs_to_delete):
                deleted_program = self.program_data_list.pop(i)
                print(f"✅ 已删除程序: {deleted_program.get('file_name', '未知文件')}")

            if programs_to_delete:
                # 保存程序数据
                if hasattr(self, 'program_module'):
                    self.program_module.save_programs_data()
                    # 更新程序表格显示
                    self.program_module.update_program_table()

                print(f"🎉 成功删除了 {len(programs_to_delete)} 个对应的程序")
            else:
                print(f"ℹ️ 配方 '{recipe_name}' 没有对应的程序需要删除")

        except Exception as e:
            print(f"删除配方对应程序失败: {e}")
            import traceback
            traceback.print_exc()

    def setup_default_state(self):
        """设置默认状态"""
        try:
            # 设置顶部按钮样式
            if hasattr(self, 'header_modules'):
                self.header_modules.setup_header_buttons_style()
            else:
                self.setup_header_buttons_style()

            # 初始化动态标签页
            self.init_dynamic_tabs()

            # 初始化表格数据
            self.init_recipe_table()
            if hasattr(self, 'program_module'):
                self.program_module.init_program_table()
            self.init_task_data()
            if hasattr(self, 'task_management_module'):
                self.task_management_module.init_task_queue_table()

            # 加载已保存的配方数据
            self.load_recipes_data()

            # 设置默认页面为检测任务页面（任务状态栏）
            self.switch_page(3)
            print("默认显示检测任务页面（任务状态栏）")

        except Exception as e:
            print(f"设置默认状态失败: {e}")
            import traceback
            traceback.print_exc()

    def switch_page(self, page_index):
        """切换页面"""
        if self.content_stack:
            self.content_stack.setCurrentIndex(page_index)

        # 更新左侧按钮状态
        buttons = [self.recipe_import_btn, self.program_edit_btn,
                   self.detection_task_btn, self.device_calibrate_btn]

        for i, btn in enumerate(buttons):
            if btn:
                btn.setChecked(i == page_index - 1)

        # 如果切换到配方导入页面，重新加载配方数据
        if page_index == 1:
            print("切换到配方导入页面，重新加载配方...")
            self.load_recipes_from_storage()

        # 如果切换到程序编辑页面，重新加载程序数据
        elif page_index == 2:
            print("切换到程序编辑页面，重新加载程序...")
            self.refresh_program_table()

    # 程序编辑功能已迁移到 program_module.py

    def open_jog_control(self):
        """打开点动控制"""
        # 创建点动控制标签页（标签页切换事件会自动处理界面切换）
        tab_id = "jog_control"
        if self.dynamic_tab_widget:
            self.dynamic_tab_widget.add_tab(tab_id, "点动控制")

    def on_tab_closed(self, tab_id):
        """标签页关闭处理"""
        print(f"标签页 {tab_id} 已关闭")
        # 如果是程序编辑标签页，回到程序列表
        if tab_id.startswith("program_edit_") and self.program_tab_widget:
            self.program_tab_widget.setCurrentIndex(0)

    def on_tab_switched(self, tab_id):
        """标签页切换处理"""
        print(f"切换到标签页 {tab_id}")

        # 根据标签页类型切换相应的界面
        if tab_id.startswith("program_edit_"):
            # 程序编辑标签页
            self.switch_page(2)  # 切换到程序编辑页面
            if self.program_tab_widget:
                self.program_tab_widget.setCurrentIndex(1)  # 切换到编辑标签页

        elif tab_id == "jog_control":
            # 点动控制标签页（现在合并到程序编辑页面）
            self.switch_page(2)  # 切换到程序编辑页面
            if self.program_tab_widget:
                self.program_tab_widget.setCurrentIndex(1)  # 切换到程序编辑与点动控制标签页

        elif tab_id.startswith("task_detail_"):
            # 任务详情标签页
            self.switch_page(3)  # 切换到检测任务页面
            if self.task_tab_widget:
                self.task_tab_widget.setCurrentIndex(1)  # 切换到任务详情标签页

        else:
            print(f"未知的标签页类型: {tab_id}")

    def init_task_data(self):
        """初始化任务数据"""
        # 简单直接的解决方案：隐藏有问题的下拉框
        if self.program_number_combo:
            try:
                print("检测到主界面下拉框，由于存在兼容性问题，将其隐藏")
                print("请使用'启动任务'按钮打开新的任务创建界面")

                # 隐藏有问题的下拉框
                self.program_number_combo.setVisible(False)

                # 如果有对应的标签，也可以隐藏
                if hasattr(self, 'program_label'):
                    self.program_label.setVisible(False)

                print("✅ 已隐藏有问题的下拉框")

            except Exception as e:
                print(f"隐藏下拉框失败: {e}")

        # 确保启动任务按钮可用
        if hasattr(self, 'start_task_btn'):
            self.start_task_btn.setEnabled(True)
            print("✅ 启动任务按钮已启用")

    def return_to_task_list(self):
        """返回任务列表"""
        try:
            if hasattr(self, 'task_management_module'):
                # 使用任务管理模块的方法
                if hasattr(self, 'task_tab_widget') and self.task_tab_widget:
                    self.task_tab_widget.setCurrentIndex(1)
                print("已返回任务列表")
        except Exception as e:
            print(f"返回任务列表失败: {e}")
            QMessageBox.warning(self, "错误", f"返回任务列表失败: {e}")

    def optimize_main_layout(self):
        """优化主界面布局，隐藏不必要的空间占用组件"""
        try:
            tabs_scroll_area = self.findChild(QtWidgets.QScrollArea, "tabs_scroll_area")
            if tabs_scroll_area:
                tabs_scroll_area.hide()
                print("已隐藏tabs_scroll_area")
            else:
                print("未找到tabs_scroll_area组件")
            scroll_areas = self.findChildren(QtWidgets.QScrollArea)
            for scroll_area in scroll_areas:
                object_name = scroll_area.objectName()
                if any(keyword in object_name.lower() for keyword in ["tab", "spacer", "empty", "unused"]):
                    widget = scroll_area.widget()
                    if widget:
                        layout = widget.layout()
                        if layout and layout.count() <= 1:
                            scroll_area.hide()
                            print(f"隐藏空白滚动区域: {object_name}")
            self.optimize_space_wasting_components()
        except Exception as e:
            print(f"主界面布局优化失败: {e}")
            import traceback
            traceback.print_exc()

    def optimize_space_wasting_components(self):
        """优化其他可能浪费空间的组件"""
        try:
            spacers = self.findChildren(QtWidgets.QSpacerItem)
            for spacer in spacers:
                if hasattr(spacer, 'sizeHint') and spacer.sizeHint().height() > 100:
                    spacer.changeSize(0, 0)
                    print("优化了一个大型spacer")
            frames = self.findChildren(QtWidgets.QFrame)
            for frame in frames:
                object_name = frame.objectName()
                if any(keyword in object_name.lower() for keyword in ["spacer", "empty", "blank", "unused"]):
                    if frame.geometry().height() > 50:
                        frame.hide()
                        print(f"隐藏空白frame: {object_name}")
            widgets = self.findChildren(QtWidgets.QWidget)
            for widget in widgets:
                object_name = widget.objectName()
                if any(keyword in object_name.lower() for keyword in ["spacer", "empty", "blank", "unused"]):
                    children = widget.findChildren(QtWidgets.QWidget)
                    if len(children) <= 1 and widget.geometry().height() > 30:
                        widget.hide()
                        print(f"隐藏空白widget: {object_name}")
            print("空间优化完成")
        except Exception as e:
            print(f"空间组件优化失败: {e}")
            # detail_container.update()
            # detail_widget.show()
            print("任务详情界面加载完成")
        except ImportError as e:
            print(f"导入 ConMainWindow 失败: {e}")
            QMessageBox.warning(self, "错误", f"无法导入任务详情界面模块: {e}")
        except Exception as e:
            print(f"加载任务详情界面失败: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.warning(self, "错误", f"无法加载任务详情界面: {e}")

    def show_hardware_config(self):
        """显示硬件配置对话框 - 已迁移到header_modules"""
        if hasattr(self, 'header_modules'):
            self.header_modules.show_hardware_config()
        else:
            QMessageBox.warning(self, "错误", "界面顶部模块未初始化")

    def show_connection_monitor(self):
        """显示连接状态监控对话框 - 已迁移到header_modules"""
        if hasattr(self, 'header_modules'):
            self.header_modules.show_connection_monitor()
        else:
            QMessageBox.warning(self, "错误", "界面顶部模块未初始化")

    def show_operator_login(self):
        """显示操作人员登录对话框 - 已迁移到header_modules"""
        if hasattr(self, 'header_modules'):
            self.header_modules.show_operator_login()
        else:
            QMessageBox.warning(self, "错误", "界面顶部模块未初始化")

    def show_alarm_info(self):
        """显示报警信息 - 已迁移到header_modules"""
        if hasattr(self, 'header_modules'):
            self.header_modules.show_alarm_info()
        else:
            QMessageBox.warning(self, "报警信息", "当前系统状态正常\n无报警信息")

    def show_real_time_monitor(self):
        """显示实时监控窗口 - 已迁移到header_modules"""
        if hasattr(self, 'header_modules'):
            self.header_modules.show_real_time_monitor()
        else:
            QMessageBox.information(self, "实时监控", "打开相机采集图像显示窗口")

    def on_login_success(self, role, username):
        """登录成功处理"""
        QMessageBox.information(self, "登录成功", f"欢迎 {role} {username}！")

    def init_dynamic_tabs(self):
        """初始化动态标签页"""
        try:
            if not hasattr(self, 'tabs_container') or not self.tabs_container:
                return

            # 清空现有布局
            layout = self.tabs_container.layout()
            if layout:
                # 移除spacer
                for i in reversed(range(layout.count())):
                    item = layout.itemAt(i)
                    if item and item.spacerItem():
                        layout.removeItem(item)

            # 创建动态标签页组件
            self.dynamic_tab_widget = DynamicTabWidget()
            self.dynamic_tab_widget.tab_closed.connect(self.on_tab_closed)
            self.dynamic_tab_widget.tab_switched.connect(self.on_tab_switched)

            # 添加到布局
            if layout:
                layout.addWidget(self.dynamic_tab_widget)
                layout.addStretch()

        except Exception as e:
            print(f"初始化动态标签页失败: {e}")
            import traceback
            traceback.print_exc()

    def create_icon_text_button(self, button, icon_path, text):
        """创建图标在上文字在下的按钮"""
        if not button:
            return

        try:
            # 清除原有文字和图标
            button.setText("")
            button.setIcon(QtGui.QIcon())

            # 设置按钮固定大小
            button.setFixedSize(60, 60)

            # 创建垂直布局的widget
            widget = QtWidgets.QWidget()
            layout = QtWidgets.QVBoxLayout(widget)
            layout.setContentsMargins(5, 5, 5, 5)
            layout.setSpacing(3)

            # 创建图标标签
            icon_label = QtWidgets.QLabel()
            icon_label.setFixedSize(24, 24)
            icon_label.setAlignment(Qt.AlignCenter)

            # 设置图标
            if os.path.exists(icon_path):
                pixmap = QPixmap(icon_path)
                scaled_pixmap = pixmap.scaled(24, 24, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                icon_label.setPixmap(scaled_pixmap)
            else:
                icon_label.setText("⚙")  # 默认图标
                icon_label.setStyleSheet("font-size: 18px; color: white;")

            # 创建文字标签
            text_label = QtWidgets.QLabel(text)
            text_label.setAlignment(Qt.AlignCenter)
            text_label.setFixedHeight(20)  # 固定文字标签高度
            text_label.setStyleSheet("""
                QLabel {
                    color: white;
                    font-size: 10px;
                    font-weight: bold;
                }
            """)
            text_label.setWordWrap(True)

            # 添加到布局，确保垂直居中对齐
            layout.addStretch()  # 上方弹性空间
            layout.addWidget(icon_label, 0, Qt.AlignCenter)
            layout.addWidget(text_label, 0, Qt.AlignCenter)
            layout.addStretch()  # 下方弹性空间

            # 设置按钮样式
            button.setStyleSheet("""
                QPushButton {
                    background-color: #34495e;
                    border: 1px solid #4a6741;
                    border-radius: 6px;
                    padding: 0px;
                }
                QPushButton:hover {
                    background-color: #4a6741;
                }
                QPushButton:pressed {
                    background-color: #2c3e50;
                }
            """)

            # 清空按钮原有布局
            if button.layout():
                QtWidgets.QWidget().setLayout(button.layout())

            # 设置新布局
            button.setLayout(layout)

        except Exception as e:
            print(f"设置按钮样式失败: {e}")
            import traceback
            traceback.print_exc()
            # 设置基本样式作为后备
            button.setText(text)
            button.setStyleSheet("""
                QPushButton {
                    background-color: #34495e;
                    color: white;
                    border: 1px solid #4a6741;
                    border-radius: 4px;
                    padding: 5px;
                    font-size: 10px;
                }
            """)

    def setup_header_buttons_style(self):
        """设置顶部按钮样式，图标在上文字在下"""
        try:
            # 设置各个按钮
            if self.hardware_config_btn:
                self.create_icon_text_button(
                    self.hardware_config_btn,
                    "blade/static/ui/settings.png",
                    "硬件配置"
                )

            if self.operator_btn:
                self.create_icon_text_button(
                    self.operator_btn,
                    "blade/static/ui/user.png",
                    "操作人员"
                )

            if self.connect_status:
                self.create_icon_text_button(
                    self.connect_status,
                    "blade/static/ui/connection.png",
                    "连接状态"
                )

            if self.alarm_btn:
                self.create_icon_text_button(
                    self.alarm_btn,
                    "blade/static/ui/warning.png",
                    "报警信息"
                )

            if self.monitor_btn:
                self.create_icon_text_button(
                    self.monitor_btn,
                    "blade/static/ui/camera.png",
                    "实时监控"
                )

        except Exception as e:
            print(f"设置头部按钮样式失败: {e}")

    def show_material_frame_dialog(self):
        """显示接头上料配置对话框"""
        try:
            from PyQt5 import uic
            from PyQt5.QtWidgets import QDialog, QVBoxLayout
            ui_file_path = os.path.join("templates", "material_frame.ui")
            if not os.path.exists(ui_file_path):
                QMessageBox.warning(self, "错误", f"找不到界面文件: {ui_file_path}")
                return
            dialog = QDialog(self)
            dialog.setWindowTitle("接头上料配置")
            dialog.setModal(True)
            material_widget = uic.loadUi(ui_file_path)
            row_priority_btn = material_widget.findChild(QtWidgets.QPushButton, "pushButton_6")
            col_priority_btn = material_widget.findChild(QtWidgets.QPushButton, "pushButton_7")
            if row_priority_btn:
                row_priority_btn.setEnabled(False)
            if col_priority_btn:
                col_priority_btn.setEnabled(False)
            layout = QVBoxLayout(dialog)
            layout.setContentsMargins(0, 0, 0, 0)
            layout.addWidget(material_widget)
            if hasattr(material_widget, 'size'):
                dialog.resize(material_widget.size())
            else:
                dialog.resize(800, 600)
            result = dialog.exec_()
            if result == QDialog.Accepted:
                print("接头上料配置完成")
            else:
                print("接头上料配置已取消")
        except Exception as e:
            print(f"显示接头上料配置对话框失败: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.warning(self, "错误", f"显示接头上料配置对话框失败: {str(e)}")

    def close_program_edit(self):
        """关闭程序编辑"""
        try:
            if hasattr(self, 'program_module') and self.program_module:
                # 关闭程序模块的编辑模式
                self.program_module.close_edit_mode()

                # 切换回主页面（完全退出程序编辑页面）
                if hasattr(self, 'content_stack') and self.content_stack:
                    self.content_stack.setCurrentIndex(0)  # 切换到主页面（索引0）
                    print("✅ 已切换回主页面")
                elif hasattr(self, 'switch_page'):
                    self.switch_page(0)  # 使用switch_page方法切换到主页面
                    print("✅ 已通过switch_page切换回主页面")

                QMessageBox.information(self, "关闭编辑", "已关闭程序编辑界面")
                print("✅ 已关闭程序编辑界面")

        except Exception as e:
            print(f"关闭程序编辑失败: {e}")
            QMessageBox.warning(self, "错误", f"关闭程序编辑失败: {str(e)}")

    def closeEvent(self, event):
        """窗口关闭事件处理"""
        try:
            print("🔄 开始处理窗口关闭事件...")

            # 停止所有定时器和清理资源
            self.cleanup_resources()

            # 保存数据
            self.save_all_data()

            print("✅ 窗口关闭事件处理完成")
            event.accept()  # 接受关闭事件

            # 强制退出应用程序
            from PyQt5.QtWidgets import QApplication
            QApplication.instance().quit()

        except Exception as e:
            print(f"❌ 窗口关闭事件处理失败: {e}")
            import traceback
            traceback.print_exc()
            # 即使出错也要关闭窗口
            event.accept()

            # 强制退出应用程序
            from PyQt5.QtWidgets import QApplication
            QApplication.instance().quit()

    def cleanup_resources(self):
        """清理所有资源"""
        try:
            print("🧹 开始清理资源...")

            # 清理任务管理模块
            if hasattr(self, 'task_management_module') and self.task_management_module:
                if hasattr(self.task_management_module, 'cleanup'):
                    self.task_management_module.cleanup()
                elif hasattr(self.task_management_module, 'stop_progress_timer'):
                    self.task_management_module.stop_progress_timer()
                print("✅ 已清理任务管理模块资源")

            # 清理程序模块
            if hasattr(self, 'program_module') and self.program_module:
                if hasattr(self.program_module, 'cleanup'):
                    self.program_module.cleanup()
                print("✅ 已清理程序模块资源")

            # 清理校准模块
            if hasattr(self, 'calibration_module') and self.calibration_module:
                if hasattr(self.calibration_module, 'cleanup'):
                    self.calibration_module.cleanup()
                print("✅ 已清理校准模块资源")

            # 清理头部模块
            if hasattr(self, 'header_modules') and self.header_modules:
                if hasattr(self.header_modules, 'cleanup'):
                    self.header_modules.cleanup()
                print("✅ 已清理头部模块资源")

            # 清理动态标签页
            if hasattr(self, 'dynamic_tab_widget') and self.dynamic_tab_widget:
                # 关闭所有标签页
                try:
                    for i in range(self.dynamic_tab_widget.count()):
                        widget = self.dynamic_tab_widget.widget(i)
                        if widget and hasattr(widget, 'close'):
                            widget.close()
                    print("✅ 已清理动态标签页")
                except Exception as e:
                    print(f"⚠️ 清理动态标签页失败: {e}")

            # 强制垃圾回收
            import gc
            gc.collect()
            print("✅ 已执行垃圾回收")

            print("✅ 资源清理完成")

        except Exception as e:
            print(f"❌ 清理资源失败: {e}")
            import traceback
            traceback.print_exc()

    def save_all_data(self):
        """保存所有数据"""
        try:
            print("💾 开始保存数据...")

            # 保存配方数据
            if hasattr(self, 'recipe_data_list'):
                self.save_recipes_data()
                print("✅ 已保存配方数据")

            # 保存程序数据
            if hasattr(self, 'program_module') and self.program_module:
                if hasattr(self.program_module, 'save_programs_data'):
                    self.program_module.save_programs_data()
                    print("✅ 已保存程序数据")

            print("✅ 数据保存完成")

        except Exception as e:
            print(f"❌ 保存数据失败: {e}")
            import traceback
            traceback.print_exc()


def main():
    app = QtWidgets.QApplication(sys.argv)

    # 设置应用程序退出策略
    app.setQuitOnLastWindowClosed(True)

    try:
        window = BladeInsightMainWindow()
        window.showMaximized()  # 修改为最大化显示

        # 运行应用程序
        exit_code = app.exec_()

        print(f"应用程序退出，退出码: {exit_code}")
        sys.exit(exit_code)

    except Exception as e:
        print(f"启动失败: {e}")
        import traceback
        traceback.print_exc()
        QMessageBox.critical(None, "错误", f"无法启动应用程序:\n{e}")
        sys.exit(-1)


if __name__ == "__main__":
    main()
