#!/usr/bin/python
# -*- coding: utf-8 -*-

import typing as t
from hdmtv.core.node.data import Request, Response
from hd_robotics.core.flags import Codes
from hd_robotics.actions.abstract import Action



class VisualAction(Action):
    """ visual action. """

    def __init__(
        self,
        **options
    ) -> None:
        super(VisualAction, self).__init__(**options)

    def _pause_function(self) -> bool:
        """ 暂停执行函数，每当调用Action.pause()的时候系统会将pause=True写入blackboard中，当Action在每次tick接收到pause=True
            的时候会调用此函数，默认返回True，若返回False，则update直接返回Status.Failure。
        """
        response: Response = self.call(
            srv_name=self._prefix + "/abort", request=Request(), op_name="视觉动作暂停")
        if response.code == Codes.SUCCESS:
            return True
        return False

    def _resume_function(self) -> bool:
        """ 继续执行函数，每当调用Action.resume()的时候系统会将pause=False写入blackboard中，当Action在每次tick接收到pause由True
            变为False的时候会调用此函数，默认返回True，若返回False，则update直接返回Status.Failure。
        """
        return True

    def supported_commands(self) -> t.Iterable[str]:
        """ Set the action's supported commands. """
        return {
            "recognize",
            "initialize",
            "abort",
            "identify_furnace_batch_number",
            "visual_inspection_one",
            "visual_inspection_two",
            "visual_inspection_three"
        }
