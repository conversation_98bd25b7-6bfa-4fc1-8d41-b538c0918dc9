#!/usr/bin/python
# -*- coding: utf-8 -*-

import os
from PyQt5 import uic
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QPixmap
from PyQt5 import QtWidgets
from PyQt5.QtWidgets import (QMessageBox, QPushButton)

class FunctionalTaskDetailWidget(QtWidgets.QMainWindow):
    def __init__(self, ui_file_path, task_id, task_data, parent=None, task_management_module=None):
        # 如果parent不是QWidget类型，则设置为None
        if parent is not None and not isinstance(parent, QtWidgets.QWidget):
            task_management_module = parent
            parent = None

        super().__init__(parent)
        self.task_id = task_id
        self.task_data = task_data

        # 获取父级任务管理模块的引用
        self.task_management_module = task_management_module or (parent if hasattr(parent, 'inspect_service') else None)

        # 加载UI文件
        uic.loadUi(ui_file_path, self)

        # 隐藏菜单栏和状态栏
        if self.menuBar():
            self.menuBar().hide()
        if self.statusBar():
            self.statusBar().hide()

        # 设置为普通控件
        self.setWindowFlags(Qt.Widget)

        # 初始化功能
        self.init_task_functionality()
        self.setup_control_buttons()
        self.optimize_ui_display()

    def init_task_functionality(self):
        """初始化任务功能"""
        try:
            # 查找并设置任务相关控件
            self.operator_2 = self.findChild(QtWidgets.QLineEdit, "operator_2")
            self.number = self.findChild(QtWidgets.QLineEdit, "number")
            self.id = self.findChild(QtWidgets.QLineEdit, "id")
            # 进度条已移除
            # self.progressBar = self.findChild(QtWidgets.QProgressBar, "progressBar")

            # 查找图片显示控件
            self.graphicsView = self.findChild(QtWidgets.QGraphicsView, "graphicsView")
            self.graphicsView_2 = self.findChild(QtWidgets.QGraphicsView, "graphicsView_2")

            # 设置任务信息
            if self.id:
                self.id.setText(self.task_id)
            if self.number:
                self.number.setText(self.task_data.get("part_number", ""))
            if self.operator_2:
                task_info = self.task_data.get("task_info", {})
                self.operator_2.setText(task_info.get("operator", "当前用户"))
            # 进度条显示已移除
            # if self.progressBar:
            #     progress = self.task_data.get("progress", 0)
            #     self.progressBar.setValue(progress)

            # 初始化72个叶片控件
            self.image_labels = []
            self.btns = []
            self.decided_flags = [False] * 72
            self.selected_flags = [False] * 72
            self.scrapped_flags = [False] * 72
            self.current_selected_index = None

            for i in range(1, 73):
                # 查找叶片控件
                widget = self.findChild(QtWidgets.QWidget, f"widget{i}")
                if widget:
                    label = QtWidgets.QLabel(widget)
                    label.setGeometry(0, 0, widget.width(), widget.height())

                    # 设置默认图片
                    pixmap_path = os.path.join("blade", "static", "ui", "10.png")
                    if os.path.exists(pixmap_path):
                        pixmap = QPixmap(pixmap_path)
                        scaled_pixmap = pixmap.scaled(
                            int(label.width() * 0.8),
                            int(label.height() * 0.8),
                            Qt.KeepAspectRatio,
                            Qt.SmoothTransformation
                        )
                        label.setPixmap(scaled_pixmap)
                        label.setAlignment(Qt.AlignCenter)
                        label.setScaledContents(False)

                    self.image_labels.append(label)
                else:
                    self.image_labels.append(None)

                # 查找按钮
                btn = self.findChild(QtWidgets.QPushButton, f"btn{i}")
                if btn:
                    self.btns.append(btn)
                    btn.clicked.connect(lambda checked=False, idx=i: self.label_clicked(idx))
                    btn.setStyleSheet("""
                        background: transparent;
                        color: black;
                        font-weight: bold;
                        font-size: 24px;
                        padding-left: 10px;
                        text-align: left;
                        margin-left: -5px;
                    """)
                    btn.raise_()

            # 查找其他控件
            self.scrap = self.findChild(QtWidgets.QPushButton, "scrap")
            self.decision_making = self.findChild(QtWidgets.QPushButton, "decision_making")

            # 连接信号
            if self.scrap:
                self.scrap.clicked.connect(self.on_scrap_clicked)
            if self.decision_making:
                self.decision_making.clicked.connect(self.on_decision_making_clicked)

            # 查找并保存UI控件引用，用于后续更新
            self.number_detected_label = self.findChild(QtWidgets.QLabel, "number_detected")
            self.blade_id_label = self.findChild(QtWidgets.QLabel, "blade_id")
            self.blade_id_1_label = self.findChild(QtWidgets.QLabel, "blade_id_1")

            # 连接inspect_service信号
            self.connect_inspect_service_signals()

            # 初始化图片显示
            # 获取当前的current_num，如果有inspect_service的话
            initial_current_num = 1
            if (self.task_management_module and
                hasattr(self.task_management_module, 'inspect_service') and
                self.task_management_module.inspect_service):
                try:
                    initial_current_num = max(1, self.task_management_module.inspect_service.current_num)
                    print(f"📷 使用当前current_num初始化图片: {initial_current_num}")
                except:
                    print("📷 无法获取current_num，使用默认值1")

            self.update_graphics_views(initial_current_num)

            print(f"任务详情功能初始化完成: {self.task_id}")

        except Exception as e:
            print(f"任务功能初始化失败: {e}")
            import traceback
            traceback.print_exc()

    def label_clicked(self, index):
        """叶片点击处理"""
        print(f"叶片 {index} 被点击")
        idx = index - 1

        # 取消之前选中的叶片
        if self.current_selected_index is not None and self.current_selected_index != idx:
            last_label = self.image_labels[self.current_selected_index]
            if last_label:
                if self.scrapped_flags[self.current_selected_index]:
                    pixmap_name = "7.png"
                elif self.decided_flags[self.current_selected_index]:
                    pixmap_name = "6.png"
                else:
                    pixmap_name = "10.png"
                self.update_label_pixmap(last_label, pixmap_name)
                self.selected_flags[self.current_selected_index] = False

        # 处理当前点击的叶片
        current_label = self.image_labels[idx]
        if not current_label:
            return

        if self.selected_flags[idx]:
            # 取消选中
            if self.scrapped_flags[idx]:
                pixmap_name = "7.png"
            elif self.decided_flags[idx]:
                pixmap_name = "6.png"
            else:
                pixmap_name = "10.png"
            self.selected_flags[idx] = False
            self.current_selected_index = None
        else:
            # 选中
            pixmap_name = "8.png"
            self.selected_flags[idx] = True
            self.current_selected_index = idx

        self.update_label_pixmap(current_label, pixmap_name)

    def update_label_pixmap(self, label, pixmap_name):
        """更新标签图片"""
        pixmap_path = os.path.join("blade", "static", "ui", pixmap_name)
        if os.path.exists(pixmap_path):
            pixmap = QPixmap(pixmap_path)
            scaled_pixmap = pixmap.scaled(
                int(label.width() * 0.8),
                int(label.height() * 0.8),
                Qt.KeepAspectRatio,
                Qt.SmoothTransformation
            )
            label.setPixmap(scaled_pixmap)

    def on_scrap_clicked(self):
        """报废按钮点击处理"""
        if self.current_selected_index is None:
            print("未选中叶片，无法报废")
            return

        idx = self.current_selected_index
        label = self.image_labels[idx]
        if label:
            self.update_label_pixmap(label, "7.png")
            self.scrapped_flags[idx] = True
            print(f"叶片 {idx + 1} 已报废")

    def on_decision_making_clicked(self):
        """决策按钮点击处理"""
        if self.current_selected_index is None:
            print("未选中叶片，无法决策")
            return

        idx = self.current_selected_index
        label = self.image_labels[idx]
        if label:
            self.update_label_pixmap(label, "6.png")
            self.decided_flags[idx] = True
            self.scrapped_flags[idx] = False
            print(f"叶片 {idx + 1} 决策完成")



    def setup_control_buttons(self):
        """设置控制按钮（从UI文件中查找并连接信号）"""
        try:
            # 查找UI文件中的控制按钮
            self.stop_button = self.findChild(QPushButton, "task_stop_button")
            self.pause_button = self.findChild(QPushButton, "task_pause_button")
            self.resume_button = self.findChild(QPushButton, "task_resume_button")

            # 检查按钮是否找到
            buttons_found = []
            if self.stop_button:
                buttons_found.append("停止按钮")
            if self.pause_button:
                buttons_found.append("暂停按钮")
            if self.resume_button:
                buttons_found.append("继续按钮")

            if buttons_found:
                print(f"🔍 找到控制按钮: {', '.join(buttons_found)}")
            else:
                print("❌ 未在UI文件中找到任何控制按钮")
                print("请在UI文件中添加以下按钮:")
                print("- task_stop_button (停止按钮)")
                print("- task_pause_button (暂停按钮)")
                print("- task_resume_button (继续按钮)")
                return

            # 设置按钮初始状态
            if self.resume_button:
                self.resume_button.setEnabled(False)  # 初始状态禁用继续按钮

            # 设置按钮样式
            self.set_control_button_styles()

            # 连接按钮信号
            if self.stop_button:
                self.stop_button.clicked.connect(self.on_stop_button_clicked)
                print("✅ 停止按钮信号已连接")

            if self.pause_button:
                self.pause_button.clicked.connect(self.on_pause_button_clicked)
                print("✅ 暂停按钮信号已连接")

            if self.resume_button:
                self.resume_button.clicked.connect(self.on_resume_button_clicked)
                print("✅ 继续按钮信号已连接")

            print("✅ 控制按钮设置完成")

        except Exception as e:
            print(f"❌ 设置控制按钮失败: {e}")
            import traceback
            traceback.print_exc()

    def set_control_button_styles(self):
        """设置控制按钮的样式"""
        # 停止按钮样式（红色）
        stop_style = """
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 16px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:pressed {
                background-color: #a93226;
            }
        """

        # 暂停按钮样式（橙色）
        pause_style = """
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 16px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
            QPushButton:pressed {
                background-color: #d68910;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
                color: #7f8c8d;
            }
        """

        # 继续按钮样式（绿色）
        resume_style = """
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 16px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
                color: #7f8c8d;
            }
        """

        # 安全地设置按钮样式
        if hasattr(self, 'stop_button') and self.stop_button:
            self.stop_button.setStyleSheet(stop_style)

        if hasattr(self, 'pause_button') and self.pause_button:
            self.pause_button.setStyleSheet(pause_style)

        if hasattr(self, 'resume_button') and self.resume_button:
            self.resume_button.setStyleSheet(resume_style)

    def on_stop_button_clicked(self):
        """停止按钮点击处理"""
        try:
            # 弹出确认对话框
            reply = QMessageBox.question(
                self,
                "确认停止",
                "确定要停止当前运行的任务吗？\n\n此操作将中断正在进行的检测流程。",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # 用户确认停止，调用inspect_service的stop函数
                if (hasattr(self, 'task_management_module') and
                    self.task_management_module and
                    hasattr(self.task_management_module, 'inspect_service') and
                    self.task_management_module.inspect_service):

                    try:
                        print("🔄 调用inspect_service.stop()...")
                        result = self.task_management_module.inspect_service.stop()

                        if result == 0:
                            print("✅ 任务停止成功")
                            QMessageBox.information(self, "停止成功", "任务已成功停止！")

                            # 可以在这里添加更新UI状态的逻辑
                            self.update_task_status_after_stop()
                        else:
                            print(f"❌ 任务停止失败，错误码: {result}")
                            QMessageBox.warning(self, "停止失败", f"任务停止失败，错误码: {result}")

                    except Exception as e:
                        print(f"❌ 调用stop函数失败: {e}")
                        QMessageBox.critical(self, "停止错误", f"停止任务时发生错误：\n{str(e)}")
                else:
                    print("❌ InspectService不可用")
                    QMessageBox.warning(self, "服务不可用", "检测服务不可用，无法停止任务！")
            else:
                print("用户取消了停止操作")

        except Exception as e:
            print(f"❌ 停止按钮处理失败: {e}")
            import traceback
            traceback.print_exc()

    def update_task_status_after_stop(self):
        """停止任务后更新任务状态"""
        try:
            # 这里可以添加停止任务后的状态更新逻辑
            # 进度条显示已移除
            pass

            print("✅ 任务状态更新完成")

        except Exception as e:
            print(f"❌ 更新任务状态失败: {e}")

    def on_pause_button_clicked(self):
        """暂停按钮点击处理"""
        try:
            # 调用inspect_service的pause函数
            if (hasattr(self, 'task_management_module') and
                self.task_management_module and
                hasattr(self.task_management_module, 'inspect_service') and
                self.task_management_module.inspect_service):

                try:
                    print("🔄 调用inspect_service.pause()...")
                    result = self.task_management_module.inspect_service.pause()

                    if result == 0:
                        print("✅ 任务暂停成功")
                        QMessageBox.information(self, "暂停成功", "任务已成功暂停！")

                        # 更新按钮状态：禁用暂停按钮，启用继续按钮
                        self.pause_button.setEnabled(False)
                        self.resume_button.setEnabled(True)

                    else:
                        print(f"❌ 任务暂停失败，错误码: {result}")
                        QMessageBox.warning(self, "暂停失败", f"任务暂停失败，错误码: {result}")

                except Exception as e:
                    print(f"❌ 调用pause函数失败: {e}")
                    QMessageBox.critical(self, "暂停错误", f"暂停任务时发生错误：\n{str(e)}")
            else:
                print("❌ InspectService不可用")
                QMessageBox.warning(self, "服务不可用", "检测服务不可用，无法暂停任务！")

        except Exception as e:
            print(f"❌ 暂停按钮处理失败: {e}")
            import traceback
            traceback.print_exc()

    def on_resume_button_clicked(self):
        """继续按钮点击处理"""
        try:
            # 调用inspect_service的resume函数
            if (hasattr(self, 'task_management_module') and
                self.task_management_module and
                hasattr(self.task_management_module, 'inspect_service') and
                self.task_management_module.inspect_service):

                try:
                    print("🔄 调用inspect_service.resume()...")
                    result = self.task_management_module.inspect_service.resume()

                    if result == 0:
                        print("✅ 任务继续成功")
                        QMessageBox.information(self, "继续成功", "任务已成功继续！")

                        # 更新按钮状态：启用暂停按钮，禁用继续按钮
                        self.pause_button.setEnabled(True)
                        self.resume_button.setEnabled(False)

                    else:
                        print(f"❌ 任务继续失败，错误码: {result}")
                        QMessageBox.warning(self, "继续失败", f"任务继续失败，错误码: {result}")

                except Exception as e:
                    print(f"❌ 调用resume函数失败: {e}")
                    QMessageBox.critical(self, "继续错误", f"继续任务时发生错误：\n{str(e)}")
            else:
                print("❌ InspectService不可用")
                QMessageBox.warning(self, "服务不可用", "检测服务不可用，无法继续任务！")

        except Exception as e:
            print(f"❌ 继续按钮处理失败: {e}")
            import traceback
            traceback.print_exc()

    def connect_inspect_service_signals(self):
        """连接inspect_service的信号"""
        if (hasattr(self, 'task_management_module') and
            self.task_management_module and
            hasattr(self.task_management_module, 'inspect_service') and
            self.task_management_module.inspect_service):

            inspect_service = self.task_management_module.inspect_service
            print(f"🔗 找到inspect_service: {inspect_service}")

            # 连接信号到槽函数
            inspect_service.current_num_changed.connect(self.on_current_num_changed)
            inspect_service.task_progress_updated.connect(self.on_progress_updated)
            inspect_service.batch_completed.connect(self.on_batch_completed)

            print("✅ inspect_service信号连接成功")
            print(f"🔍 当前状态 - current_num: {inspect_service.current_num}, batch_num: {inspect_service.batch_num}")

    def on_current_num_changed(self, current_num, batch_num):
        """当前数量变化的槽函数"""
        try:
            print(f"🔄 收到current_num变化信号: {current_num}/{batch_num}")

            # 更新已检测数量显示
            if self.number_detected_label:
                self.number_detected_label.setText(f"{current_num}/{batch_num}")

            # 更新叶片号显示
            if self.blade_id_label:
                self.blade_id_label.setText(str(current_num))

            # 更新第二个叶片号显示
            if self.blade_id_1_label:
                self.blade_id_1_label.setText(str(current_num))

            # 更新叶片图标状态（将当前完成的叶片设为绿色）
            if current_num > 0 and current_num <= len(self.image_labels):
                self.update_blade_status(current_num - 1, "completed")  # current_num-1因为索引从0开始

            # 更新图片显示
            self.update_graphics_views(current_num)

        except Exception as e:
            print(f"❌ 处理current_num变化失败: {e}")
            import traceback
            traceback.print_exc()

    def on_progress_updated(self, progress):
        """进度更新的槽函数 - 已禁用进度显示"""
        try:
            print(f"🔄 收到进度更新信号: {progress}% (进度显示已禁用)")
            # 进度条显示已被移除，不再更新UI

        except Exception as e:
            print(f"❌ 处理进度更新失败: {e}")
            import traceback
            traceback.print_exc()

    def on_batch_completed(self):
        """批次完成的槽函数"""
        try:
            print("🎉 收到批次完成信号")

            # 将当前运行中的任务状态更新为完成
            if (self.task_management_module and
                hasattr(self.task_management_module, 'complete_running_task')):

                success = self.task_management_module.complete_running_task()
                if success:
                    print("✅ 任务状态已更新为完成，状态变为绿色")
                else:
                    print("❌ 任务状态更新失败")
            else:
                print("❌ 无法访问任务管理模块")

        except Exception as e:
            print(f"❌ 处理批次完成失败: {e}")
            import traceback
            traceback.print_exc()

    def update_blade_status(self, blade_index, status):
        """更新叶片状态"""
        try:
            if blade_index < 0 or blade_index >= len(self.image_labels):
                return

            label = self.image_labels[blade_index]
            if not label:
                return

            # 根据状态选择图片
            if status == "completed":
                pixmap_name = "6.png"  # 绿色图标表示完成
                self.decided_flags[blade_index] = True
            elif status == "current":
                pixmap_name = "8.png"  # 选中状态图标
            else:
                pixmap_name = "10.png"  # 默认图标

            self.update_label_pixmap(label, pixmap_name)
            print(f"✅ 更新叶片 {blade_index + 1} 状态为: {status}")

        except Exception as e:
            print(f"❌ 更新叶片状态失败: {e}")
            import traceback
            traceback.print_exc()

    def update_graphics_views(self, current_num):
        """更新graphicsView和graphicsView_2的图片显示"""
        try:
            from PyQt5.QtGui import QPixmap
            from PyQt5.QtWidgets import QGraphicsScene, QGraphicsPixmapItem
            import os

            # 确保current_num至少为1
            if current_num < 1:
                current_num = 1

            # 构建图片路径 - 使用更可靠的路径策略
            image_filename = f"{current_num}.png"

            # 获取当前文件的目录，然后向上找到blade_inspection目录
            current_file_dir = os.path.dirname(os.path.abspath(__file__))
            print(f"🔍 当前文件目录: {current_file_dir}")

            # 向上查找blade_inspection目录
            blade_inspection_dir = current_file_dir
            while blade_inspection_dir and not blade_inspection_dir.endswith('blade_inspection'):
                parent_dir = os.path.dirname(blade_inspection_dir)
                if parent_dir == blade_inspection_dir:  # 到达根目录
                    break
                blade_inspection_dir = parent_dir

            print(f"🔍 blade_inspection目录: {blade_inspection_dir}")

            # 构建photo目录路径
            photo_dir = os.path.join(blade_inspection_dir, "test", "photo")
            print(f"🔍 photo目录: {photo_dir}")

            # 构建图片路径
            image_path = os.path.join(photo_dir, image_filename)
            print(f"🔍 尝试加载图片: {image_path}")

            # 如果找不到对应的图片，尝试默认的1.png
            if not os.path.exists(image_path):
                print(f"⚠️ 找不到图片: {image_path}，尝试使用默认图片1.png")
                image_path = os.path.join(photo_dir, "1.png")
                print(f"🔍 默认图片路径: {image_path}")

                # 如果默认图片也不存在，设置为None
                if not os.path.exists(image_path):
                    image_path = None

            # 如果连默认图片都找不到，创建一个占位符显示
            if image_path is None or not os.path.exists(image_path):
                print(f"⚠️ 所有路径都找不到图片，创建占位符显示")
                self.create_placeholder_display(current_num)
                return

            # 加载图片
            pixmap = QPixmap(image_path)
            if pixmap.isNull():
                print(f"❌ 无法加载图片: {image_path}，创建占位符显示")
                self.create_placeholder_display(current_num)
                return

            print(f"✅ 加载图片: {image_path}")

            # 更新graphicsView
            if self.graphicsView:
                scene1 = QGraphicsScene()
                pixmap_item1 = QGraphicsPixmapItem(pixmap)
                scene1.addItem(pixmap_item1)
                self.graphicsView.setScene(scene1)
                self.graphicsView.fitInView(pixmap_item1, Qt.KeepAspectRatio)
                print("✅ 更新graphicsView成功")

            # 更新graphicsView_2
            if self.graphicsView_2:
                scene2 = QGraphicsScene()
                pixmap_item2 = QGraphicsPixmapItem(pixmap)
                scene2.addItem(pixmap_item2)
                self.graphicsView_2.setScene(scene2)
                self.graphicsView_2.fitInView(pixmap_item2, Qt.KeepAspectRatio)
                print("✅ 更新graphicsView_2成功")

        except Exception as e:
            print(f"❌ 更新图片显示失败: {e}")
            import traceback
            traceback.print_exc()

    def create_placeholder_display(self, current_num):
        """创建占位符显示"""
        try:
            from PyQt5.QtGui import QPixmap, QPainter, QFont, QColor
            from PyQt5.QtWidgets import QGraphicsScene, QGraphicsPixmapItem
            from PyQt5.QtCore import Qt

            # 创建一个简单的占位符图片
            pixmap = QPixmap(400, 300)
            pixmap.fill(QColor(240, 240, 240))  # 浅灰色背景

            painter = QPainter(pixmap)
            painter.setPen(QColor(100, 100, 100))
            painter.setFont(QFont("Arial", 16))

            text = f"图片 {current_num}.png\n未找到"
            painter.drawText(pixmap.rect(), Qt.AlignCenter, text)
            painter.end()

            # 更新graphicsView
            if self.graphicsView:
                scene1 = QGraphicsScene()
                pixmap_item1 = QGraphicsPixmapItem(pixmap)
                scene1.addItem(pixmap_item1)
                self.graphicsView.setScene(scene1)
                self.graphicsView.fitInView(pixmap_item1, Qt.KeepAspectRatio)

            # 更新graphicsView_2
            if self.graphicsView_2:
                scene2 = QGraphicsScene()
                pixmap_item2 = QGraphicsPixmapItem(pixmap)
                scene2.addItem(pixmap_item2)
                self.graphicsView_2.setScene(scene2)
                self.graphicsView_2.fitInView(pixmap_item2, Qt.KeepAspectRatio)

            print(f"✅ 创建占位符显示: 图片 {current_num}.png 未找到")

        except Exception as e:
            print(f"❌ 创建占位符显示失败: {e}")
            import traceback
            traceback.print_exc()

    def optimize_ui_display(self):
        """优化UI显示"""
        try:
            # 隐藏不需要的面板
            panels_to_hide = [
                "基础信息", "操作信息", "运行信息", "结果导出",
                "left_panel", "side_panel", "info_panel"
            ]

            hidden_count = 0
            for panel_name in panels_to_hide:
                panel = self.findChild(QtWidgets.QWidget, panel_name)
                if panel:
                    panel.hide()
                    hidden_count += 1

            print(f"UI显示优化完成，隐藏了 {hidden_count} 个面板")

        except Exception as e:
            print(f"UI优化失败: {e}")
