#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json
import yaml
from datetime import datetime
from PyQt5.QtWidgets import (QMessageBox, QTableWidgetItem, QPushButton, QWidget, QHBoxLayout,
                             QListWidgetItem, QProgressDialog, QDialog, QMainWindow)
from PyQt5.QtCore import Qt, pyqtSignal, QObject, QTimer
from hdmtv.core.node import Request, Response
from PyQt5 import QtWidgets, uic, QtGui
from blade_inspection.core.flags import Codes
from hd_robotics.utils.data_io import dump_json_file

# 导入InspectService
try:
    from blade_inspection.core.inspect_service import InspectService
    from hdmtv.core.cdi import get_instance
except ImportError as e:
    InspectService = None


class ProgramModule(QObject):
    """程序编辑功能模块"""

    # 定义信号
    program_edited = pyqtSignal(int, dict)  # 程序编辑信号

    def __init__(self, main_window):
        """初始化程序模块"""
        super().__init__()
        self.main_window = main_window

        # 程序相关属性
        self.programs_data_file = "programs_data.json"  # 程序数据持久化文件
        self.program_data_list = []  # 存储所有程序数据
        self.current_editing_program = None  # 当前正在编辑的程序

        # 编辑状态管理
        self.is_editing_mode = False  # 是否处于编辑模式
        self.current_editing_row = None  # 当前编辑的行索引

        # 机器人程序步骤控制
        self.current_program_steps = [
            "1. 机器人配置",
            "2. PLC配置",
            "3. 视觉定位配置",
            "4. 3D扫描配置",
            "5. 接头上料配置",
            "6. 机器人关节运动"
        ]  # 当前程序的步骤列表
        self.current_step_index = 0  # 当前步骤索引
        self.program_list_widget = None  # 程序列表控件
        self.prev_step_list_widget = None  # 上一步显示控件
        self.current_step_list_widget = None  # 当前步显示控件
        self.next_step_list_widget = None  # 下一步显示控件

        # 机器人步长控制
        self.tcp_step_value = 1.0  # TCP步长默认值
        self.joint_step_value = 1.0  # 关节步长默认值
        self.tcp_step_widget = None  # TCP步长输入控件
        self.joint_step_widget = None  # 关节步长输入控件

        # 机器人控制界面控件
        self.robot_control_widget = None  # 机器人控制界面控件

        # 清理标志位
        self._is_cleaning_up = False

        # 初始化路径配置
        self.cache_path = self.load_cache_path_from_config()
        self.task_storage_path = os.path.join(self.cache_path, "task")

        # 程序类型映射（保留向后兼容）
        self.program_types = {
            "detection": "检测程序",
            "calibration": "校准程序",
            "maintenance": "维护程序",
            "test": "测试程序",
            "custom": "自定义程序"
        }

        # 初始化InspectService
        self.inspect_service = None
        self.inspect_service_available = False
        try:
            if InspectService:
                self.inspect_service = get_instance(clazz=InspectService)
                self.inspect_service_available = True
            else:
                self.inspect_service = None
                self.inspect_service_available = False
        except Exception as e:
            import traceback
            traceback.print_exc()
            self.inspect_service = None
            self.inspect_service_available = False

        # 当前加载的任务数据
        self.current_task_data = None
        self.current_task_process = []

        # 加载程序数据
        self.load_programs_data()

    def init_robot_control(self):
        """初始化机器人控制界面"""
        try:
            # 查找程序编辑标签页容器
            if self.main_window.program_tab_widget:
                robot_control_tab = None
                for i in range(self.main_window.program_tab_widget.count()):
                    if self.main_window.program_tab_widget.tabText(i) in ["程序编辑", "程序编辑与点动控制"]:
                        robot_control_tab = self.main_window.program_tab_widget.widget(i)
                        # 更新标签页名称
                        self.main_window.program_tab_widget.setTabText(i, "程序编辑与点动控制")
                        break

                if robot_control_tab:
                    # 查找robot_control_container容器
                    robot_control_container = robot_control_tab.findChild(QtWidgets.QWidget, "robot_control_container")

                    if robot_control_container:
                        # 设置容器为响应式大小
                        robot_control_container.setMinimumSize(1200, 700)
                        robot_control_container.setSizePolicy(
                            QtWidgets.QSizePolicy.Expanding,
                            QtWidgets.QSizePolicy.Expanding
                        )

                        # 清空容器中的所有子组件
                        for child in robot_control_container.findChildren(QtWidgets.QWidget):
                            if child.parent() == robot_control_container:
                                child.deleteLater()

                        print(f"容器大小: {robot_control_container.size()}")
                        robot_control_ui_path = "templates/robot_control.ui"
                        if os.path.exists(robot_control_ui_path):
                            # 直接创建QWidget并加载UI
                            self.robot_control_widget = QtWidgets.QWidget(robot_control_container)
                            uic.loadUi(robot_control_ui_path, self.robot_control_widget)

                            # 设置robot_control_widget为响应式尺寸
                            self.robot_control_widget.setSizePolicy(
                                QtWidgets.QSizePolicy.Expanding,
                                QtWidgets.QSizePolicy.Expanding
                            )

                            # 直接设置几何位置填满容器
                            self.robot_control_widget.setGeometry(0, 0,
                                                                  robot_control_container.width(),
                                                                  robot_control_container.height())

                            # 连接容器大小变化事件
                            self.connect_container_resize(robot_control_container)

                            # 确保widget可见
                            self.robot_control_widget.setVisible(True)
                            self.robot_control_widget.show()

                            # 连接机器人控制信号
                            self.connect_robot_control_signals()

                            # 连接窗口大小变化事件
                            self.connect_resize_events()

                            print("机器人控制界面(robot_control.ui)加载成功")
                        else:
                            print(f"未找到robot_control.ui文件: {robot_control_ui_path}")
                    else:
                        print("未找到robot_control_container容器，尝试直接加载到标签页")

                        # 清空原有内容
                        layout = robot_control_tab.layout()
                        if layout:
                            while layout.count():
                                child = layout.takeAt(0)
                                if child.widget():
                                    child.widget().deleteLater()
                        else:
                            # 创建新的布局
                            layout = QtWidgets.QVBoxLayout(robot_control_tab)
                            layout.setContentsMargins(0, 0, 0, 0)
                            layout.setSpacing(0)

                        robot_control_ui_path = "blade/static/ui/robot_control.ui"
                        if os.path.exists(robot_control_ui_path):
                            # 创建一个临时的QMainWindow来加载UI
                            temp_main_window = QtWidgets.QMainWindow()
                            uic.loadUi(robot_control_ui_path, temp_main_window)

                            # 获取中央组件并重新设置父级
                            central_widget = temp_main_window.centralwidget
                            if central_widget:
                                # 将中央组件从临时窗口中移除
                                temp_main_window.setCentralWidget(QtWidgets.QWidget())

                                # 设置新的父级和大小
                                central_widget.setParent(robot_control_tab)
                                self.robot_control_widget = central_widget

                                # 设置合适的最小尺寸和大小策略
                                self.robot_control_widget.setMinimumSize(1200, 600)
                                self.robot_control_widget.setSizePolicy(
                                    QtWidgets.QSizePolicy.Expanding,
                                    QtWidgets.QSizePolicy.Expanding
                                )

                                # 添加到布局
                                layout.addWidget(self.robot_control_widget)

                                # 确保widget可见并更新布局
                                self.robot_control_widget.setVisible(True)
                                robot_control_tab.updateGeometry()
                                QtWidgets.QApplication.processEvents()

                                # 连接机器人控制信号
                                self.connect_robot_control_signals()

                                print("机器人控制界面(robot_control.ui)直接加载到标签页成功")
                            else:
                                print("robot_control.ui中未找到centralwidget")
                                # 创建错误提示
                                error_label = QtWidgets.QLabel("robot_control.ui格式错误")
                                error_label.setStyleSheet("""
                                    QLabel {
                                        color: #e74c3c;
                                        font-size: 14px;
                                        padding: 20px;
                                        background-color: #fdf2f2;
                                        border: 1px solid #e74c3c;
                                        border-radius: 5px;
                                    }
                                """)
                                layout.addWidget(error_label)
                        else:
                            print(f"未找到robot_control.ui文件: {robot_control_ui_path}")
                            # 创建错误提示
                            error_label = QtWidgets.QLabel("robot_control.ui文件未找到")
                            error_label.setStyleSheet("""
                                QLabel {
                                    color: #e74c3c;
                                    font-size: 14px;
                                    padding: 20px;
                                    background-color: #fdf2f2;
                                    border: 1px solid #e74c3c;
                                    border-radius: 5px;
                                }
                            """)
                            layout.addWidget(error_label)
                else:
                    print("未找到程序编辑标签页")
            else:
                print("未找到程序标签页容器")

            # 初始化机器人程序步骤
            if hasattr(self, 'robot_control_widget') and self.robot_control_widget:
                self.init_robot_program_steps()

        except Exception as e:
            print(f"初始化机器人控制界面失败: {e}")
            import traceback
            traceback.print_exc()

    def load_cache_path_from_config(self):
        """从config.yaml配置文件中读取cache_path"""
        try:
            config_path = os.path.join("configs", "config.yaml")
            if not os.path.exists(config_path):
                print(f"配置文件不存在: {config_path}，使用默认路径")
                return "D:/blade_inspect"

            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)

            cache_path = config_data.get('INSPECT', {}).get('cache_path', 'D:/blade_inspect')
            print(f"程序模块从配置文件读取cache_path: {cache_path}")
            return cache_path

        except Exception as e:
            print(f"读取配置文件失败: {e}，使用默认路径")
            return "D:/blade_inspect"

    def connect_robot_control_signals(self):
        """连接机器人控制界面的信号"""
        try:
            if hasattr(self, 'robot_control_widget') and self.robot_control_widget:
                # 坐标控制按钮
                axes = ['x', 'y', 'z', 'rx', 'ry', 'rz']
                for axis in axes:
                    # 减少按钮
                    minus_btn = self.robot_control_widget.findChild(QtWidgets.QPushButton, f"{axis}_minus")
                    if minus_btn:
                        minus_btn.clicked.connect(lambda checked, a=axis: self.on_robot_coordinate_minus(a))

                    # 增加按钮
                    plus_btn = self.robot_control_widget.findChild(QtWidgets.QPushButton, f"{axis}_plus")
                    if plus_btn:
                        plus_btn.clicked.connect(lambda checked, a=axis: self.on_robot_coordinate_plus(a))

                    # 值输入框
                    value_edit = self.robot_control_widget.findChild(QtWidgets.QLineEdit, f"{axis}_value")
                    if value_edit:
                        value_edit.editingFinished.connect(lambda a=axis: self.on_robot_coordinate_changed(a))

                # 关节控制按钮
                joint_axes = ['a1', 'a2', 'a3', 'a4', 'a5', 'a6']
                for joint in joint_axes:
                    # 减少按钮
                    minus_btn = self.robot_control_widget.findChild(QtWidgets.QPushButton, f"{joint}_minus")
                    if minus_btn:
                        minus_btn.clicked.connect(lambda checked, j=joint: self.on_robot_joint_minus(j))

                    # 增加按钮
                    plus_btn = self.robot_control_widget.findChild(QtWidgets.QPushButton, f"{joint}_plus")
                    if plus_btn:
                        plus_btn.clicked.connect(lambda checked, j=joint: self.on_robot_joint_plus(j))

                    # 值输入框
                    value_edit = self.robot_control_widget.findChild(QtWidgets.QLineEdit, f"{joint}_value")
                    if value_edit:
                        value_edit.editingFinished.connect(lambda j=joint: self.on_robot_joint_changed(j))

                # 点动控制按钮
                jog_buttons = ['jog_up', 'jog_down', 'jog_left', 'jog_right']
                for btn_name in jog_buttons:
                    btn = self.robot_control_widget.findChild(QtWidgets.QPushButton, btn_name)
                    if btn:
                        direction = btn_name.replace('jog_', '')
                        btn.pressed.connect(lambda d=direction: self.on_robot_jog_start(d))
                        btn.released.connect(lambda d=direction: self.on_robot_jog_stop(d))

                # 控制按钮
                control_buttons = {
                    'prev_step_btn': self.on_robot_prev_step,
                    'next_step_btn': self.on_robot_next_step,
                    'execute_btn': self.on_robot_execute,
                    'save_btn': self.on_robot_save,
                    'modify_btn': self.on_robot_modify,
                    'insert_btn': self.on_robot_insert,  # 添加插入按钮处理
                    'delete_btn': self.on_robot_delete,  # 添加删除按钮处理
                    'exit_btn': self.on_robot_exit  # 添加退出按钮处理
                }

                for btn_name, handler in control_buttons.items():
                    btn = self.robot_control_widget.findChild(QtWidgets.QPushButton, btn_name)
                    if btn:
                        btn.clicked.connect(handler)

                # 程序列表
                self.program_list_widget = self.robot_control_widget.findChild(QtWidgets.QListWidget, "program_list")
                if self.program_list_widget:
                    # 允许单选，这样我们可以控制选中状态来实现高亮
                    self.program_list_widget.setSelectionMode(QtWidgets.QAbstractItemView.SingleSelection)
                    # 但仍然连接点击事件用于提示
                    self.program_list_widget.itemClicked.connect(self.on_robot_program_clicked)

                # 步骤显示控件
                self.prev_step_list_widget = self.robot_control_widget.findChild(QtWidgets.QListWidget,
                                                                                 "prev_step_list")
                self.current_step_list_widget = self.robot_control_widget.findChild(QtWidgets.QListWidget,
                                                                                    "current_step_list")
                self.next_step_list_widget = self.robot_control_widget.findChild(QtWidgets.QListWidget,
                                                                                 "next_step_list")

                # 配置步骤显示控件的滚动条
                if self.prev_step_list_widget:
                    self.prev_step_list_widget.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
                    self.prev_step_list_widget.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)

                if self.current_step_list_widget:
                    self.current_step_list_widget.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
                    self.current_step_list_widget.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)

                if self.next_step_list_widget:
                    self.next_step_list_widget.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
                    self.next_step_list_widget.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)

                # 步长控制控件
                self.tcp_step_widget = self.robot_control_widget.findChild(QtWidgets.QLineEdit, "tcp_step_value")
                self.joint_step_widget = self.robot_control_widget.findChild(QtWidgets.QLineEdit, "joint_step_value")

                # 连接步长控件信号
                if self.tcp_step_widget:
                    self.tcp_step_widget.editingFinished.connect(self.on_tcp_step_changed)
                    self.tcp_step_widget.setText(str(self.tcp_step_value))  # 设置默认值

                if self.joint_step_widget:
                    self.joint_step_widget.editingFinished.connect(self.on_joint_step_changed)
                    self.joint_step_widget.setText(str(self.joint_step_value))  # 设置默认值

                # 初始化程序步骤数据
                self.init_robot_program_steps()

                print("机器人控制信号连接成功")
        except Exception as e:
            print(f"连接机器人控制信号失败: {e}")
            import traceback
            traceback.print_exc()

    def connect_container_resize(self, container):
        """连接容器大小变化事件"""
        try:
            # 重写容器的resizeEvent
            original_resize_event = container.resizeEvent

            def new_container_resize_event(event):
                # 调用原始的resizeEvent
                original_resize_event(event)

                # 更新robot_control_widget的大小
                if hasattr(self, 'robot_control_widget') and self.robot_control_widget:
                    new_size = event.size()
                    self.robot_control_widget.resize(new_size)
                    self.robot_control_widget.setGeometry(0, 0, new_size.width(), new_size.height())
                    print(f"容器大小变化: {new_size.width()}x{new_size.height()}")

            container.resizeEvent = new_container_resize_event
            print("容器大小变化事件连接成功")

        except Exception as e:
            print(f"连接容器大小变化事件失败: {e}")

    def connect_resize_events(self):
        """连接窗口大小变化事件"""
        try:
            # 重写resizeEvent方法来处理窗口大小变化
            original_resize_event = self.main_window.resizeEvent

            def new_resize_event(event):
                # 调用原始的resizeEvent
                original_resize_event(event)

                # 更新robot_control界面
                self.update_robot_control_layout()

            self.main_window.resizeEvent = new_resize_event
            print("窗口大小变化事件连接成功")

        except Exception as e:
            print(f"连接窗口大小变化事件失败: {e}")

    def update_robot_control_layout(self):
        """更新robot_control界面布局"""
        try:
            if hasattr(self, 'robot_control_widget') and self.robot_control_widget:
                # 强制更新布局
                self.robot_control_widget.updateGeometry()

                # 获取父级容器并更新
                parent = self.robot_control_widget.parent()
                if parent:
                    parent.updateGeometry()
                    parent.update()

                # 处理事件队列
                QtWidgets.QApplication.processEvents()

        except Exception as e:
            print(f"更新robot_control布局失败: {e}")

    def init_program_table(self):
        """初始化程序编辑表格"""
        try:
            if not self.main_window.program_table:
                print("程序表格组件未找到")
                return

            # 设置表格列数和标题
            if self.main_window.program_table.columnCount() != 5:
                self.main_window.program_table.setColumnCount(5)
                headers = ["任务名", "产品名称", "产线名称", "工序名称", "操作"]
                self.main_window.program_table.setHorizontalHeaderLabels(headers)

            # 设置列宽
            header = self.main_window.program_table.horizontalHeader()
            if header:
                # 设置前4列为拉伸模式
                for i in range(4):
                    header.setSectionResizeMode(i, QtWidgets.QHeaderView.Stretch)

                # 最后一列（操作列）设置为固定宽度
                header.setSectionResizeMode(4, QtWidgets.QHeaderView.Fixed)
                self.main_window.program_table.setColumnWidth(4, 200)

            # 设置表格属性
            self.main_window.program_table.setAlternatingRowColors(True)
            self.main_window.program_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
            self.main_window.program_table.verticalHeader().setVisible(False)

            # 禁用表格编辑
            self.main_window.program_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)

            # 更新程序表格显示
            self.update_program_table()

            print("程序表格初始化完成")

        except Exception as e:
            print(f"程序表格初始化失败: {e}")
            import traceback
            traceback.print_exc()

    def load_programs_data(self):
        """加载程序数据 - 从task文件夹中读取task_info.json文件"""
        try:
            # 从task文件夹扫描程序数据
            self.program_data_list = self.scan_task_folders_for_programs()

            print(f"已从task文件夹加载 {len(self.program_data_list)} 个程序")

        except Exception as e:
            print(f"加载程序数据失败: {e}")
            self.create_default_programs()

    def scan_task_folders_for_programs(self):
        """扫描task文件夹下的所有子文件夹，查找task_info.json文件"""
        programs = []

        if not os.path.exists(self.task_storage_path):
            print(f"任务存储路径不存在: {self.task_storage_path}")
            return programs

        try:
            # 遍历task文件夹下的所有子文件夹
            for folder_name in os.listdir(self.task_storage_path):
                folder_path = os.path.join(self.task_storage_path, folder_name)

                # 只处理文件夹
                if not os.path.isdir(folder_path):
                    continue

                # 查找task_info.json文件
                task_info_path = os.path.join(folder_path, "task_info.json")
                if os.path.exists(task_info_path):
                    try:
                        # 读取task_info.json文件
                        with open(task_info_path, 'r', encoding='utf-8') as f:
                            task_info_data = json.load(f)

                        # 解析task_info.json数据并映射到程序字段
                        program_info = self.parse_task_info_to_program(task_info_data, folder_name, task_info_path)
                        programs.append(program_info)

                    except Exception as e:
                        print(f"读取task_info.json文件失败 {task_info_path}: {e}")

        except Exception as e:
            print(f"扫描task文件夹失败: {e}")

        return programs

    def parse_task_info_to_program(self, task_info_data, folder_name, file_path):
        """解析task_info.json数据并映射到程序字段"""
        try:
            stat = os.stat(file_path)
            default_create_time = datetime.fromtimestamp(stat.st_mtime).strftime("%Y-%m-%d %H:%M")

            # 字段映射：task_info.json字段 -> 程序表格字段
            program_info = {
                "task_name": task_info_data.get("MOTaskName", folder_name),  # 任务名
                "product_name": task_info_data.get("ProductName", "未知产品"),  # 产品名称
                "product_line_name": task_info_data.get("ProductLineName", "未知产线"),  # 产线名称
                "specification_name": task_info_data.get("SpecificationName", "未知工序"),  # 工序名称
                "folder_name": folder_name,  # 保存文件夹名称用于标识
                "file_path": file_path,  # 保存文件路径
                "task_info_data": task_info_data,  # 保存原始数据
                "created_time": task_info_data.get("create_time", default_create_time),  # 创建时间
                "operator": task_info_data.get("operator", "未知用户"),  # 操作员

                # 保留向后兼容的字段
                "program_type": "检测程序",
                "recipe_name": task_info_data.get("ProductName", "未知产品"),
                "program_id": folder_name,
                "description": f"任务: {task_info_data.get('MOTaskName', folder_name)}"
            }

            print(f"解析程序task_info.json: {folder_name} -> {program_info['task_name']}")
            return program_info

        except Exception as e:
            print(f"解析程序task_info.json失败 {file_path}: {e}")
            # 返回默认数据
            return {
                "task_name": folder_name,
                "product_name": "解析失败",
                "product_line_name": "解析失败",
                "specification_name": "解析失败",
                "folder_name": folder_name,
                "file_path": file_path,
                "created_time": "解析失败",
                "operator": "系统",

                # 保留向后兼容的字段
                "program_type": "检测程序",
                "recipe_name": "解析失败",
                "program_id": folder_name,
                "description": f"解析失败: {folder_name}"
            }

    def create_default_programs(self):
        """创建默认程序数据（当没有找到task文件夹时使用）"""
        self.program_data_list = [
            {
                "task_name": "未找到程序",
                "product_name": "请检查task文件夹",
                "product_line_name": "无数据",
                "specification_name": "无数据",
                "folder_name": "default",
                "file_path": "",
                "created_time": "无数据",
                "operator": "系统"
            }
        ]

    def save_programs_data(self):
        """保存程序数据到文件"""
        try:
            data = {
                'programs': self.program_data_list,
                'last_updated': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

            with open(self.programs_data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)


        except Exception as e:
            print(f"保存程序数据失败: {e}")

    def update_program_table(self):
        """更新程序表格显示"""
        try:
            if not self.main_window.program_table:
                return

            # 设置行数
            self.main_window.program_table.setRowCount(len(self.program_data_list))

            # 填充数据
            for row, program_data in enumerate(self.program_data_list):
                # 设置行高
                self.main_window.program_table.setRowHeight(row, 80)

                # 填充列数据 - 使用新的字段映射
                data = [
                    program_data.get("task_name", "未知任务"),  # 任务名
                    program_data.get("product_name", "未知产品"),  # 产品名称
                    program_data.get("product_line_name", "未知产线"),  # 产线名称
                    program_data.get("specification_name", "未知工序")  # 工序名称
                ]

                for col, value in enumerate(data):
                    item = QTableWidgetItem(str(value))
                    item.setTextAlignment(Qt.AlignCenter)
                    item.setFlags(item.flags() & ~Qt.ItemIsEditable)
                    self.main_window.program_table.setItem(row, col, item)

                # 添加操作按钮
                self.add_program_action_buttons(row)

            print(f"程序表格已更新，显示 {len(self.program_data_list)} 个程序")

        except Exception as e:
            print(f"更新程序表格失败: {e}")
            import traceback
            traceback.print_exc()

    def add_program_action_buttons(self, row):
        """添加程序操作按钮（只保留编辑按钮）"""
        try:
            # 创建按钮容器
            widget = QtWidgets.QWidget()
            layout = QtWidgets.QHBoxLayout(widget)
            layout.setContentsMargins(5, 5, 5, 5)
            layout.setSpacing(5)

            # 只保留编辑按钮
            edit_btn = QPushButton("编辑")
            edit_btn.setFixedSize(60, 30)
            edit_btn.setStyleSheet("""
                QPushButton {
                    background-color: #3498db;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    font-size: 10px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #2980b9;
                }
            """)

            # 连接信号
            edit_btn.clicked.connect(lambda: self.edit_program(row))

            # 添加编辑按钮到布局中央
            layout.addStretch()  # 左侧弹性空间
            layout.addWidget(edit_btn)
            layout.addStretch()  # 右侧弹性空间

            # 设置到表格的最后一列
            self.main_window.program_table.setCellWidget(row, 4, widget)


        except Exception as e:
            print(f"添加程序操作按钮失败: {e}")
            import traceback
            traceback.print_exc()

    def init_robot_program_steps(self):
        """初始化机器人程序步骤"""
        try:
            # 如果已经从JSON加载了步骤，则使用JSON数据
            if hasattr(self, 'current_task_process') and self.current_task_process:
                # 步骤已经在update_program_steps_from_json中设置
                pass
            else:
                # 使用默认示例程序步骤数据
                self.current_program_steps = [
                    "1. 机器人配置",
                    "2. PLC配置",
                    "3. 视觉定位配置",
                    "4. 3D扫描配置",
                    "5. 接头上料配置",
                    "6. 机器人关节运动"
                ]

            # 默认选中第一步
            self.current_step_index = 0

            # 填充程序列表
            if self.program_list_widget:
                self.program_list_widget.clear()
                for i, step in enumerate(self.current_program_steps):
                    item = QListWidgetItem(step)
                    # 设置不可编辑和不可选择
                    item.setFlags(item.flags() & ~Qt.ItemIsEditable)
                    self.program_list_widget.addItem(item)

                # 高亮显示当前步骤
                self.update_step_highlight()

            # 更新步骤显示
            self.update_step_display()

            print(f"初始化了 {len(self.current_program_steps)} 个程序步骤")

        except Exception as e:
            print(f"初始化机器人程序步骤失败: {e}")
            import traceback
            traceback.print_exc()

    def update_step_highlight(self):
        """更新步骤高亮显示（使用橙色边框）"""
        try:
            if not self.program_list_widget or not self.current_program_steps:
                return


            # 重新创建所有列表项，使用橙色边框标识当前步骤
            self.program_list_widget.clear()

            for i, step in enumerate(self.current_program_steps):
                item = QListWidgetItem(step)

                if i == self.current_step_index:
                    # 当前步骤：使用橙色文字和特殊标记
                    item.setForeground(QtGui.QColor(255, 95, 0))  # 橙色文字
                    # 在文字前添加橙色标记符号
                    item.setText(f"► {step}")

                else:
                    # 其他步骤：普通黑色文字
                    item.setForeground(QtGui.QColor(0, 0, 0))  # 黑色文字
                    item.setText(f"  {step}")  # 前面加空格对齐

                # 设置不可编辑
                item.setFlags(item.flags() & ~Qt.ItemIsEditable)
                self.program_list_widget.addItem(item)

            # 设置整个列表的样式，添加边框效果
            list_style = """
                QListWidget {
                    background-color: white;
                    border: 1px solid #ccc;
                    outline: none;
                }
                QListWidget::item {
                    padding: 8px;
                    border-bottom: 1px solid #eee;
                }
                QListWidget::item:hover {
                    background-color: #f5f5f5;
                }
            """
            self.program_list_widget.setStyleSheet(list_style)

            # 强制刷新显示
            self.program_list_widget.update()
            self.program_list_widget.repaint()

        except Exception as e:
            print(f"更新步骤高亮失败: {e}")
            import traceback
            traceback.print_exc()

    def format_step_content(self, step_data):
        """格式化步骤内容为可读的显示格式 - 直接显示JSON格式"""
        if not step_data:
            return ["无数据"]

        try:
            # 直接将整个step_data转换为格式化的JSON字符串
            json_str = json.dumps(step_data, ensure_ascii=False, indent=2)

            # 将JSON字符串按行分割，便于在列表控件中显示
            content_lines = json_str.split('\n')

            return content_lines

        except Exception as e:
            print(f"格式化步骤内容失败: {e}")
            return [f"格式化失败: {str(e)}"]

    def update_step_display(self):
        """更新步骤显示区域 - 显示current_task_process中的实际内容"""
        try:
            # 检查current_task_process
            if not hasattr(self, 'current_task_process'):
                return

            if not self.current_task_process:
                return

            total_steps = len(self.current_task_process)

            # 如果UI控件存在，更新UI显示
            if self.prev_step_list_widget is not None or self.current_step_list_widget is not None or self.next_step_list_widget is not None:
                try:
                    if self.prev_step_list_widget is not None:
                        self.prev_step_list_widget.clear()
                    else:
                        print("🔍 上一步骤控件为None")
                except Exception as e:
                    print(f"❌ 清空上一步骤显示区域失败: {e}")
                    import traceback
                    traceback.print_exc()

                print("🔍 检查当前步骤控件...")
                try:
                    if self.current_step_list_widget is not None:
                        self.current_step_list_widget.clear()
                    else:
                        print("🔍 当前步骤控件为None")
                except Exception as e:
                    print(f"❌ 清空当前步骤显示区域失败: {e}")
                try:
                    if self.next_step_list_widget is not None:
                        self.next_step_list_widget.clear()
                    else:
                        print("🔍 下一步骤控件为None")
                except Exception as e:
                    print(f"❌ 清空下一步骤显示区域失败: {e}")

                # 显示上一步骤内容
                if self.current_step_index > 0 and self.prev_step_list_widget is not None:
                    prev_step_data = self.current_task_process[self.current_step_index - 1]
                    prev_content = self.format_step_content(prev_step_data)
                    for line in prev_content:
                        self.prev_step_list_widget.addItem(line)

                # 显示当前步骤内容
                if 0 <= self.current_step_index < total_steps and self.current_step_list_widget is not None:
                    current_step_data = self.current_task_process[self.current_step_index]
                    current_content = self.format_step_content(current_step_data)
                    for line in current_content:
                        self.current_step_list_widget.addItem(line)

                # 显示下一步骤内容
                if self.current_step_index < total_steps - 1 and self.next_step_list_widget is not None:
                    next_step_data = self.current_task_process[self.current_step_index + 1]
                    next_content = self.format_step_content(next_step_data)
                    for line in next_content:
                        self.next_step_list_widget.addItem(line)
            else:
                print("❌ 没有可用的UI控件")

        except Exception as e:
            print(f"更新步骤显示失败: {e}")



    def edit_program(self, row):
        """编辑程序 - 跳转到程序编辑与点动控制页面"""
        try:
            if row >= len(self.program_data_list):
                QMessageBox.warning(self.main_window, "错误", "程序索引超出范围")
                return

            program_data = self.program_data_list[row]
            program_name = program_data.get("task_name", f"程序{row + 1}")

            # 检查是否已经在编辑模式
            if self.is_editing_mode:
                if self.current_editing_row == row:
                    # 重复点击同一个程序的编辑按钮
                    QMessageBox.information(
                        self.main_window,
                        "提示",
                        f"程序 '{program_name}' 已经在编辑中，请先关闭编辑界面后再重新编辑。"
                    )
                    return
                else:
                    # 点击其他程序的编辑按钮
                    current_program_name = self.program_data_list[self.current_editing_row].get("task_name", f"程序{self.current_editing_row + 1}")
                    QMessageBox.information(
                        self.main_window,
                        "提示",
                        f"当前正在编辑程序 '{current_program_name}'，请先关闭编辑界面后再编辑其他程序。"
                    )
                    return

            # 设置编辑状态
            self.is_editing_mode = True
            self.current_editing_row = row

            # 设置当前编辑的程序
            self.current_editing_program = {
                "row": row,
                "data": program_data.copy()
            }

            # 调试主窗口属性
            print(f"\n--- 主窗口属性检查 ---")
            print(f"content_stack存在: {hasattr(self.main_window, 'content_stack')}")
            print(f"program_tab_widget存在: {hasattr(self.main_window, 'program_tab_widget')}")
            print(f"switch_page方法存在: {hasattr(self.main_window, 'switch_page')}")

            if hasattr(self.main_window, 'content_stack'):
                print(f"content_stack对象: {self.main_window.content_stack}")
                if self.main_window.content_stack:
                    print(f"content_stack当前索引: {self.main_window.content_stack.currentIndex()}")
                    print(f"content_stack页面数量: {self.main_window.content_stack.count()}")

            if hasattr(self.main_window, 'program_tab_widget'):
                print(f"program_tab_widget对象: {self.main_window.program_tab_widget}")
                if self.main_window.program_tab_widget:
                    print(f"program_tab_widget当前索引: {self.main_window.program_tab_widget.currentIndex()}")
                    print(f"program_tab_widget标签页数量: {self.main_window.program_tab_widget.count()}")

            # 跳转到程序编辑与点动控制页面
            print(f"\n--- 开始页面跳转 ---")
            success = self.jump_to_program_edit_page(program_name, program_data)

            if success:

                # 加载对应的JSON任务文件
                self.load_task_json(program_data)

                # 初始化机器人控制界面
                self.init_robot_control()
            else:
                # 跳转失败时重置编辑状态
                self.close_edit_mode()



        except Exception as e:
            import traceback
            traceback.print_exc()
            # 发生异常时重置编辑状态
            self.close_edit_mode()
            QMessageBox.critical(self.main_window, "编辑失败", f"编辑程序时发生错误：\n{str(e)}")

    def load_task_json(self, program_data):
        """加载任务对应的JSON文件"""
        try:
            # 获取文件夹名称（这是实际的任务文件夹名）
            folder_name = program_data.get("folder_name", "")
            task_name = program_data.get("task_name", "")

            if not folder_name:
                return

            print(f"   文件夹名称: {folder_name}")
            print(f"   任务名称: {task_name}")

            # 检查InspectService是否可用
            if not self.inspect_service_available or not self.inspect_service:
                self.load_task_json_directly(program_data)
                return

            try:
                # 调用InspectService的load_request方法，使用folder_name
                result_code, json_data = self.inspect_service.load_request(folder_name)

                if result_code == 0 and json_data:  # 假设0表示成功

                    # 保存当前任务数据
                    self.current_task_data = json_data
                    self.current_task_process = json_data.get("process", [])
                    print(555, self.current_task_process)

                    print(f"📊 任务包含 {len(self.current_task_process)} 个处理步骤")

                    # 更新程序步骤显示
                    self.update_program_steps_from_json()

                else:
                    self.load_task_json_directly(program_data)

            except Exception as e:
                self.load_task_json_directly(program_data)

        except Exception as e:
            import traceback
            traceback.print_exc()
            QMessageBox.critical(
                self.main_window,
                "加载错误",
                f"加载任务JSON文件时发生错误：\n{str(e)}"
            )

    def load_task_json_directly(self, program_data):
        """直接加载JSON文件（降级方案）"""
        try:

            # 获取文件夹名称
            folder_name = program_data.get("folder_name", "")
            if not folder_name:
                return

            # 获取cache_path
            cache_path = self.load_cache_path_from_config()

            # 构建任务文件夹路径
            task_folder = os.path.join(cache_path, "task", folder_name)

            if not os.path.exists(task_folder):
                self.debug_json_file_path(program_data)
                QMessageBox.warning(
                    self.main_window,
                    "文件夹不存在",
                    f"任务文件夹不存在：\n{task_folder}"
                )
                return

            # 提取产品名称
            if folder_name.startswith("任务-"):
                product_name = folder_name[3:]  # 去掉"任务-"前缀
            else:
                product_name = folder_name

            # 构建JSON文件路径
            json_file_path = os.path.join(task_folder, f"{product_name}.json")

            if not os.path.exists(json_file_path):

                # 查找文件夹中的所有JSON文件
                json_files = [f for f in os.listdir(task_folder) if f.endswith('.json') and f != 'task_info.json']

                if json_files:
                    json_file_path = os.path.join(task_folder, json_files[0])
                else:
                    self.debug_json_file_path(program_data)
                    QMessageBox.warning(
                        self.main_window,
                        "JSON文件不存在",
                        f"在任务文件夹中找不到程序JSON文件：\n{task_folder}"
                    )
                    return

            # 读取JSON文件
            import json
            with open(json_file_path, 'r', encoding='utf-8') as f:
                json_data = json.load(f)


            # 保存当前任务数据
            self.current_task_data = json_data
            self.current_task_process = json_data.get("process", [])

            print(f"📊 任务包含 {len(self.current_task_process)} 个处理步骤")

            # 更新程序步骤显示
            self.update_program_steps_from_json()

        except Exception as e:
            import traceback
            traceback.print_exc()
            QMessageBox.critical(
                self.main_window,
                "加载失败",
                f"直接加载JSON文件失败：\n{str(e)}"
            )

    def debug_json_file_path(self, program_data):
        """调试JSON文件路径"""
        try:
            print("\n🔍 调试JSON文件路径:")

            # 获取cache_path
            cache_path = self.load_cache_path_from_config()
            print(f"   cache_path: {cache_path}")

            # 获取文件夹名称
            folder_name = program_data.get("folder_name", "")
            print(f"   folder_name: {folder_name}")

            # 构建任务文件夹路径
            task_folder = os.path.join(cache_path, "task", folder_name)
            print(f"   task_folder: {task_folder}")
            print(f"   task_folder存在: {os.path.exists(task_folder)}")

            # 提取产品名称
            if folder_name.startswith("任务-"):
                product_name = folder_name[3:]  # 去掉"任务-"前缀
            else:
                product_name = folder_name
            print(f"   product_name: {product_name}")

            # 构建JSON文件路径
            json_file_path = os.path.join(task_folder, f"{product_name}.json")
            print(f"   json_file_path: {json_file_path}")
            print(f"   json_file存在: {os.path.exists(json_file_path)}")

            # 列出文件夹内容
            if os.path.exists(task_folder):
                files = os.listdir(task_folder)
                print(f"   文件夹内容: {files}")
            else:
                print(f"   ❌ 任务文件夹不存在")

            # 检查task文件夹
            task_base_folder = os.path.join(cache_path, "task")
            if os.path.exists(task_base_folder):
                task_folders = os.listdir(task_base_folder)
                print(f"   task文件夹下的子文件夹: {task_folders}")
            else:
                print(f"   ❌ task基础文件夹不存在: {task_base_folder}")

        except Exception as e:
            print(f"调试JSON文件路径失败: {e}")

    def close_edit_mode(self):
        """关闭编辑模式"""
        try:
            self.is_editing_mode = False
            self.current_editing_row = None
            self.current_editing_program = None

            # 清理任务数据
            self.current_task_data = None
            self.current_task_process = []

        except Exception as e:
            print(f"关闭编辑模式失败: {e}")

    def update_program_steps_from_json(self, reset_cursor=True):
        """根据JSON数据更新程序步骤显示

        Args:
            reset_cursor: 是否重置光标位置到第一步，默认为True
        """
        try:
            if not self.current_task_process:
                return

            # 将JSON中的process转换为程序步骤
            self.current_program_steps = []

            for i, step in enumerate(self.current_task_process):
                step_number = i + 1
                step_description = self.generate_step_description(step, step_number)
                self.current_program_steps.append(step_description)


            # 根据参数决定是否重置当前步骤索引
            if reset_cursor:
                self.current_step_index = 0

            # 更新UI显示
            if hasattr(self, 'program_list_widget') and self.program_list_widget:
                self.update_step_highlight()
                self.update_step_display()

        except Exception as e:
            import traceback
            traceback.print_exc()

    def generate_step_description(self, step_data, step_number):
        """根据JSON步骤数据生成步骤描述"""
        try:
            # 获取基本信息
            tag = step_data.get("tag", "")
            name = step_data.get("name", "")
            kind = step_data.get("kind", "")
            action = step_data.get("action", "")
            commands_list = step_data.get("commands_list", [])

            # 根据不同的设备类型和命令生成描述
            if "robot" in kind.lower() or "robot" in name.lower():
                return self.generate_robot_step_description(step_data, step_number)
            elif "plc" in kind.lower() or "plc" in name.lower():
                return self.generate_plc_step_description(step_data, step_number)
            elif "vision" in kind.lower() or "camera" in kind.lower():
                return self.generate_vision_step_description(step_data, step_number)
            elif "gripper" in kind.lower() or "grab" in kind.lower():
                return self.generate_gripper_step_description(step_data, step_number)
            else:
                # 通用描述
                if tag:
                    return f"{step_number}. {tag}"
                elif commands_list and len(commands_list) > 0:
                    command = commands_list[0].get("command", "未知命令")
                    return f"{step_number}. {command}"
                else:
                    return f"{step_number}. 处理步骤"

        except Exception as e:
            return f"{step_number}. 处理步骤"

    def generate_robot_step_description(self, step_data, step_number):
        """生成机器人步骤描述"""
        try:
            tag = step_data.get("tag", "")
            commands_list = step_data.get("commands_list", [])

            if tag:
                return f"{step_number}. 机器人: {tag}"

            if commands_list and len(commands_list) > 0:
                command = commands_list[0].get("command", "")
                if "joint" in command:
                    return f"{step_number}. 机器人关节运动"
                elif "linear" in command or "line" in command:
                    return f"{step_number}. 机器人直线运动"
                elif "move" in command:
                    return f"{step_number}. 机器人运动"
                else:
                    return f"{step_number}. 机器人操作"

            return f"{step_number}. 机器人配置"

        except Exception as e:
            return f"{step_number}. 机器人操作"

    def generate_plc_step_description(self, step_data, step_number):
        """生成PLC步骤描述"""
        try:
            tag = step_data.get("tag", "")
            commands_list = step_data.get("commands_list", [])

            if tag:
                if "wait" in tag.lower():
                    return f"{step_number}. PLC信号等待: {tag}"
                else:
                    return f"{step_number}. PLC操作: {tag}"

            if commands_list and len(commands_list) > 0:
                command_kwargs = commands_list[0].get("kwargs", {})
                command_name = command_kwargs.get("command_name", "")
                if command_name:
                    return f"{step_number}. PLC信号: {command_name}"

            return f"{step_number}. PLC信号处理"

        except Exception as e:
            return f"{step_number}. PLC操作"

    def generate_vision_step_description(self, step_data, step_number):
        """生成视觉步骤描述"""
        try:
            tag = step_data.get("tag", "")
            if tag:
                return f"{step_number}. 视觉检测: {tag}"
            return f"{step_number}. 视觉定位配置"
        except Exception as e:
            return f"{step_number}. 视觉操作"

    def generate_gripper_step_description(self, step_data, step_number):
        """生成抓手步骤描述"""
        try:
            tag = step_data.get("tag", "")
            commands_list = step_data.get("commands_list", [])

            if tag:
                if "grab" in tag.lower() or "抓取" in tag:
                    return f"{step_number}. 托盘抓取: {tag}"
                elif "release" in tag.lower() or "释放" in tag:
                    return f"{step_number}. 托盘释放: {tag}"
                else:
                    return f"{step_number}. 抓手操作: {tag}"

            return f"{step_number}. 托盘操作"

        except Exception as e:
            return f"{step_number}. 抓手操作"

    def jump_to_program_edit_page(self, program_name, program_data):
        """跳转到程序编辑与点动控制页面"""
        try:
            success = False

            # 第一步：切换到程序编辑主页面（索引2）
            if hasattr(self.main_window, 'content_stack') and self.main_window.content_stack:
                self.main_window.content_stack.setCurrentIndex(2)
                success = True

            elif hasattr(self.main_window, 'switch_page'):
                self.main_window.switch_page(2)
                success = True

            elif hasattr(self.main_window, 'stackedWidget') and self.main_window.stackedWidget:
                self.main_window.stackedWidget.setCurrentIndex(2)
                success = True

            # 第二步：切换到程序编辑与点动控制标签页（索引1）
            if hasattr(self.main_window, 'program_tab_widget') and self.main_window.program_tab_widget:
                self.main_window.program_tab_widget.setCurrentIndex(1)
                success = True

            # 第三步：加载程序内容到编辑器
            if hasattr(self.main_window, 'program_content_edit') and self.main_window.program_content_edit:
                sample_content = self.generate_program_content(program_data)
                self.main_window.program_content_edit.setPlainText(sample_content)

            # 第四步：发送程序编辑信号
            self.program_edited.emit(0, program_data)

            if success:
                return True
            else:
                # 如果主页面切换失败，显示提示信息
                QMessageBox.information(
                    self.main_window,
                    "跳转到程序编辑页面",
                    f"正在编辑程序: {program_name}\n\n"
                    f"任务名: {program_data.get('task_name', '未知')}\n"
                    f"产品名称: {program_data.get('product_name', '未知')}\n"
                    f"产线名称: {program_data.get('product_line_name', '未知')}\n"
                    f"工序名称: {program_data.get('specification_name', '未知')}\n\n"
                    f"请手动切换到程序编辑与点动控制页面"
                )
                return False

        except Exception as e:
            import traceback
            traceback.print_exc()

            QMessageBox.warning(
                self.main_window,
                "跳转失败",
                f"无法跳转到程序编辑页面: {e}\n\n请手动切换到程序编辑与点动控制页面"
            )
            return False

    def generate_program_content(self, program_data):
        """生成程序内容 - 简化版本"""
        task_name = program_data.get("task_name", "未知任务")
        product_name = program_data.get("product_name", "未知产品")
        product_line_name = program_data.get("product_line_name", "未知产线")
        specification_name = program_data.get("specification_name", "未知工序")

        return f"""# {task_name} 程序内容
# 任务名: {task_name}
# 产品名称: {product_name}
# 产线名称: {product_line_name}
# 工序名称: {specification_name}
# 创建时间: {program_data.get('created_time', '未知')}
# 操作员: {program_data.get('operator', '未知')}

# ==========================================
# 程序开始
# ==========================================

# 1. 系统初始化
print("系统初始化...")

# 2. 设备准备
print("设备准备中...")

# 3. 主要处理逻辑
print("开始执行主要任务...")

# 4. 数据处理
print("处理数据中...")

# 5. 结果输出
print("输出结果...")

# 6. 程序结束
print("程序执行完成")

# ==========================================
# 程序结束
# ==========================================
"""

    def save_program(self):
        """保存程序 - 简化版本"""
        try:
            if not self.current_editing_program:
                QMessageBox.warning(self.main_window, "保存失败", "没有正在编辑的程序")
                return

            # 获取程序内容
            program_content = ""
            if hasattr(self.main_window, 'program_content_edit') and self.main_window.program_content_edit:
                program_content = self.main_window.program_content_edit.toPlainText()

            QMessageBox.information(self.main_window, "保存成功", f"程序内容已保存")
            print(f"程序内容已保存: {len(program_content)} 字符")

        except Exception as e:
            print(f"保存程序失败: {e}")
            QMessageBox.critical(self.main_window, "保存失败", f"保存程序时发生错误：\n{str(e)}")

    def cancel_program_edit(self):
        """取消程序编辑 - 简化版本"""
        try:
            # 清除当前编辑状态
            self.current_editing_program = None
            QMessageBox.information(self.main_window, "取消编辑", "已取消程序编辑")
            print("已取消程序编辑")

        except Exception as e:
            print(f"取消程序编辑失败: {e}")

    # ==================== 机器人控制方法 ====================

    def on_robot_coordinate_minus(self, axis):
        """机器人坐标减少（使用TCP步长）"""
        print(f"机器人坐标减少: {axis}")
        if hasattr(self, 'robot_control_widget') and self.robot_control_widget:
            value_edit = self.robot_control_widget.findChild(QtWidgets.QLineEdit, f"{axis}_value")
            if value_edit:
                try:
                    current_value = float(value_edit.text())
                    # 使用TCP步长值
                    step_value = self.tcp_step_value
                    new_value = current_value - step_value
                    value_edit.setText(str(new_value))
                    print(f"{axis.upper()}轴坐标: {current_value} -> {new_value} (步长: {step_value})")
                except ValueError:
                    print(f"无效的坐标值: {value_edit.text()}")

    def on_robot_coordinate_plus(self, axis):
        """机器人坐标增加（使用TCP步长）"""
        print(f"机器人坐标增加: {axis}")
        if hasattr(self, 'robot_control_widget') and self.robot_control_widget:
            value_edit = self.robot_control_widget.findChild(QtWidgets.QLineEdit, f"{axis}_value")
            if value_edit:
                try:
                    current_value = float(value_edit.text())
                    # 使用TCP步长值
                    step_value = self.tcp_step_value
                    new_value = current_value + step_value
                    value_edit.setText(str(new_value))
                    print(f"{axis.upper()}轴坐标: {current_value} -> {new_value} (步长: {step_value})")
                except ValueError:
                    print(f"无效的坐标值: {value_edit.text()}")

    def on_robot_coordinate_changed(self, axis):
        """机器人坐标手动更改"""
        if hasattr(self, 'robot_control_widget') and self.robot_control_widget:
            value_edit = self.robot_control_widget.findChild(QtWidgets.QLineEdit, f"{axis}_value")
            if value_edit:
                try:
                    value = float(value_edit.text())
                    print(f"机器人坐标手动更改: {axis.upper()} = {value}")
                except ValueError:
                    print(f"无效的坐标值: {value_edit.text()}")

    def on_robot_jog_start(self, direction):
        """机器人开始点动"""
        print(f"机器人开始点动: {direction}")
        # 这里可以添加实际的机器人点动控制逻辑

    def on_robot_jog_stop(self, direction):
        """机器人停止点动"""
        print(f"机器人停止点动: {direction}")
        # 这里可以添加实际的机器人点动控制逻辑

    def on_robot_joint_minus(self, joint):
        """机器人关节减少（使用关节步长）"""
        print(f"机器人关节减少: {joint}")
        if hasattr(self, 'robot_control_widget') and self.robot_control_widget:
            value_edit = self.robot_control_widget.findChild(QtWidgets.QLineEdit, f"{joint}_value")
            if value_edit:
                try:
                    current_value = float(value_edit.text())
                    # 使用关节步长值
                    step_value = self.joint_step_value
                    new_value = current_value - step_value
                    value_edit.setText(str(new_value))
                    print(f"{joint.upper()}关节: {current_value} -> {new_value} (步长: {step_value})")
                except ValueError:
                    print(f"无效的关节值: {value_edit.text()}")

    def on_robot_joint_plus(self, joint):
        """机器人关节增加（使用关节步长）"""
        print(f"机器人关节增加: {joint}")
        if hasattr(self, 'robot_control_widget') and self.robot_control_widget:
            value_edit = self.robot_control_widget.findChild(QtWidgets.QLineEdit, f"{joint}_value")
            if value_edit:
                try:
                    current_value = float(value_edit.text())
                    # 使用关节步长值
                    step_value = self.joint_step_value
                    new_value = current_value + step_value
                    value_edit.setText(str(new_value))
                    print(f"{joint.upper()}关节: {current_value} -> {new_value} (步长: {step_value})")
                except ValueError:
                    print(f"无效的关节值: {value_edit.text()}")

    def on_robot_joint_changed(self, joint):
        """机器人关节值改变"""
        print(f"机器人关节值改变: {joint}")
        if hasattr(self, 'robot_control_widget') and self.robot_control_widget:
            value_edit = self.robot_control_widget.findChild(QtWidgets.QLineEdit, f"{joint}_value")
            if value_edit:
                try:
                    new_value = float(value_edit.text())
                    print(f"{joint.upper()}关节设置为: {new_value}")
                except ValueError:
                    print(f"无效的关节值: {value_edit.text()}")
                    # 可以在这里恢复到之前的有效值

    def on_robot_prev_step(self):
        """机器人上一步"""
        try:
            if self.current_step_index <= 0:
                # 已经是第一步，显示提示
                QMessageBox.information(self.main_window, "提示", "这是第一步，无法继续向前！")
                return

            # 切换到上一步
            self.current_step_index -= 1
            self.update_step_highlight()
            self.update_step_display()

            current_step = self.current_program_steps[self.current_step_index]

        except Exception as e:
            print(f"上一步操作失败: {e}")
            QMessageBox.warning(self.main_window, "错误", f"上一步操作失败: {str(e)}")

    def on_robot_next_step(self):
        """机器人下一步"""
        try:
            if self.current_step_index >= len(self.current_program_steps) - 1:
                # 已经是最后一步，显示提示
                QMessageBox.information(self.main_window, "提示", "这是最后一步，无法继续向后！")
                return

            # 切换到下一步
            self.current_step_index += 1
            self.update_step_highlight()
            self.update_step_display()

            current_step = self.current_program_steps[self.current_step_index]

        except Exception as e:
            print(f"下一步操作失败: {e}")
            QMessageBox.warning(self.main_window, "错误", f"下一步操作失败: {str(e)}")

    def on_robot_execute(self):
        """机器人执行"""
        try:
            if not self.current_program_steps:
                QMessageBox.warning(self.main_window, "警告", "没有可执行的程序步骤！")
                return

            current_step = self.current_program_steps[self.current_step_index]

            # 确认执行
            reply = QMessageBox.question(
                self.main_window,
                "确认执行",
                f"确定要执行当前步骤吗？\n\n当前步骤: {current_step}",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # 显示执行提示
                progress = QProgressDialog("正在执行程序步骤...", "取消", 0, 0, self.main_window)
                progress.setWindowTitle("执行中")
                progress.setWindowModality(Qt.WindowModal)
                progress.show()

                print(f"开始执行步骤: {current_step}")

                # 执行实际的任务步骤
                self.execute_current_step(progress, current_step)

        except Exception as e:
            print(f"执行步骤失败: {e}")
            QMessageBox.warning(self.main_window, "错误", f"执行步骤失败: {str(e)}")

    def execute_current_step(self, progress, step_name):
        """执行当前步骤的实际逻辑"""
        try:
            # 检查是否正在清理资源
            if getattr(self, '_is_cleaning_up', False):
                return

            # 检查是否有可用的任务数据和inspect_service
            if not self.current_task_process or self.current_step_index >= len(self.current_task_process):
                # 模拟执行时间
                QTimer.singleShot(2000, lambda: self.on_execute_finished(progress, step_name, True, "模拟执行完成") if not getattr(self, '_is_cleaning_up', False) else None)
                return

            if not self.inspect_service_available or not self.inspect_service:
                # 模拟执行时间
                QTimer.singleShot(2000, lambda: self.on_execute_finished(progress, step_name, True, "模拟执行完成") if not getattr(self, '_is_cleaning_up', False) else None)
                return

            # 获取当前步骤的任务数据
            current_task_data = self.current_task_process[self.current_step_index]

            # 调用inspect_service.jog执行任务

            # 在后台线程中执行，避免阻塞UI
            QTimer.singleShot(100, lambda: self.execute_jog_task(progress, step_name, current_task_data) if not getattr(self, '_is_cleaning_up', False) else None)

        except Exception as e:
            import traceback
            traceback.print_exc()
            self.on_execute_finished(progress, step_name, False, f"执行失败: {str(e)}")

    def execute_jog_task(self, progress, step_name, task_data):
        """执行jog任务"""
        try:
            # 调用inspect_service.jog方法
            result_code = self.inspect_service.jog([task_data])

            print(f"📊 jog执行结果码: {result_code}")

            # 根据结果码判断执行是否成功
            if result_code == 0:  # 假设0表示成功
                success = True
                message = "步骤执行成功"
            else:
                success = False
                message = f"步骤执行失败，错误码: {result_code}"

            # 调用执行完成处理
            self.on_execute_finished(progress, step_name, success, message)

        except Exception as e:
            import traceback
            traceback.print_exc()
            self.on_execute_finished(progress, step_name, False, f"执行异常: {str(e)}")

    def on_execute_finished(self, progress, step_name, success=True, message=""):
        """执行完成处理"""
        try:
            progress.close()

            if success:
                # 执行成功
                QMessageBox.information(
                    self.main_window,
                    "执行完成",
                    f"步骤 '{step_name}' 执行完成！\n\n{message}"
                )

                # 执行成功后自动移动到下一步
                self.auto_move_to_next_step()
            else:
                # 执行失败
                QMessageBox.warning(
                    self.main_window,
                    "执行失败",
                    f"步骤 '{step_name}' 执行失败！\n\n{message}\n\n请检查设备状态后重试。"
                )

                # 执行失败时不自动移动到下一步，让用户手动处理

        except Exception as e:
            print(f"执行完成处理失败: {e}")
            QMessageBox.critical(
                self.main_window,
                "处理错误",
                f"执行完成处理失败: {str(e)}"
            )

    def auto_move_to_next_step(self):
        """执行完成后自动移动到下一步"""
        try:
            if not self.current_program_steps:
                return

            # 检查是否还有下一步
            if self.current_step_index >= len(self.current_program_steps) - 1:
                # 已经是最后一步，显示完成提示
                QMessageBox.information(
                    self.main_window,
                    "程序执行完成",
                    "🎉 所有程序步骤已执行完成！\n\n程序执行结束。"
                )
                return

            # 移动到下一步
            self.current_step_index += 1
            self.update_step_highlight()
            self.update_step_display()

        except Exception as e:
            print(f"自动移动到下一步失败: {e}")
            QMessageBox.warning(self.main_window, "错误", f"自动移动到下一步失败: {str(e)}")

    def on_robot_program_clicked(self, item):
        """程序列表点击提示（禁用手动选择）"""
        QMessageBox.information(
            self.main_window,
            "提示",
            "程序步骤不支持手动选择！\n\n请使用下方的 '上一步' 和 '下一步' 按钮来控制步骤切换。"
        )

    def on_robot_save(self):
        """机器人保存当前状态 - 将当前整个流程保存到对应JSON的process下面，并保存在本地"""
        try:

            # 检查是否有当前任务数据和流程
            if not hasattr(self, 'current_task_data') or not self.current_task_data:
                QMessageBox.warning(self.main_window, "保存失败", "没有当前任务数据，无法保存！")
                return

            if not hasattr(self, 'current_task_process') or not self.current_task_process:
                QMessageBox.warning(self.main_window, "保存失败", "没有当前流程数据，无法保存！")
                return

            # 更新任务数据中的process字段
            self.current_task_data['process'] = self.current_task_process

            # 检查是否有JSON文件路径
            if not self.inspect_service.json_file_path:
                QMessageBox.warning(self.main_window, "保存失败", "无法确定JSON文件路径！")
                return

            result = dump_json_file(self.inspect_service.json_file_path, self.current_task_data)
            if not result:
                QMessageBox.warning(self.main_window, "保存失败", "保存流程失败！")
                return

            QMessageBox.information(
                self.main_window,
                "保存成功",
                f"当前流程已保存到本地文件！\n\n文件路径: {self.inspect_service.json_file_path}\n步骤数量: {len(self.current_task_process)}"
            )

        except Exception as e:
            import traceback
            traceback.print_exc()
            QMessageBox.critical(
                self.main_window,
                "保存失败",
                f"保存流程时发生错误：\n\n{str(e)}"
            )

    def on_robot_modify(self):
        """机器人修改当前步骤"""
        try:
            if not self.current_program_steps or self.current_step_index < 0:
                QMessageBox.warning(self.main_window, "警告", "没有可修改的步骤！")
                return

            # 检查是否是机器人相关步骤
            result = self._check_action_type(self.current_step_index)

            if result == "RobotMoveAction":
                # 显示机器人点位选择对话框
                self._show_robot_position_dialog("modify")
            elif result == "BoxGraspAction":
                print(f"当前步骤action为BoxGraspAction，弹出material_frame.ui界面")
                self._show_material_frame_dialog()
            else:
                # 显示普通修改对话框
                QMessageBox.information(
                    self.main_window,
                    "提示",
                    "该步骤暂不支持修改"
                )

        except Exception as e:
            print(f"修改步骤失败: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.warning(self.main_window, "错误", f"修改步骤失败: {str(e)}")

    def on_robot_insert(self):
        """机器人插入新步骤"""
        try:
            if not self.current_program_steps:
                QMessageBox.warning(self.main_window, "警告", "没有可用的程序步骤！")
                return

            current_step = self.current_program_steps[self.current_step_index] if self.current_step_index >= 0 else "无"
            # 显示机器人点位选择对话框
            self._show_robot_position_dialog("insert")
            print(f"在步骤 '{current_step}' 附近插入新步骤")

        except Exception as e:
            print(f"插入步骤失败: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.warning(self.main_window, "错误", f"插入步骤失败: {str(e)}")

    def _check_action_type(self, step_index) -> str:
        """判断是否是机器人相关步骤"""
        try:
            # 检查是否有原始步骤数据
            if hasattr(self, 'current_task_process') and self.current_task_process:
                if 0 <= step_index < len(self.current_task_process):
                    step_data = self.current_task_process[step_index]

                    # 只判断action是否为RobotMoveAction
                    action = step_data.get("action", "")
                    return action

            return ""

        except Exception as e:
            return ""

    def _show_material_frame_dialog(self):
        """显示material_frame.ui界面"""
        try:
            ui_path = None
            possible_paths = [
                "templates/material_frame.ui",
                "blade_inspection/templates/material_frame.ui",
                os.path.join(os.path.dirname(__file__), "material_frame.ui")
            ]

            for path in possible_paths:
                if os.path.exists(path):
                    ui_path = path
                    break

            if not ui_path:
                QMessageBox.warning(self.main_window, "错误", "未找到material_frame.ui文件")
                return
            dialog = QMainWindow(self.main_window)
            dialog.setWindowTitle("叶片摆放配置")
            dialog.setWindowModality(Qt.ApplicationModal)
            uic.loadUi(ui_path, dialog)

            dialog.resize(800, 645)
            confirm_btn = dialog.findChild(QtWidgets.QPushButton, "pushButton_4")  # 确定按钮
            cancel_btn = dialog.findChild(QtWidgets.QPushButton, "pushButton_5")   # 取消按钮

            # 获取行方向优先和列方向优先按钮
            row_priority_btn = dialog.findChild(QtWidgets.QPushButton, "pushButton_6")  # 行方向优先
            col_priority_btn = dialog.findChild(QtWidgets.QPushButton, "pushButton_7")  # 列方向优先

            # 默认选中行方向优先
            if row_priority_btn:
                row_priority_btn.setStyleSheet("""
                    QPushButton {
                        border: 2px solid black;
                        color: white;
                        font-size: 20px;
                        background-color: rgb(255, 95, 0);  /* 默认选中状态为橙色 */
                    }
                    QPushButton:pressed {
                        background-color: rgb(255, 95, 0);
                    }
                """)
                # 添加点击事件处理
                row_priority_btn.clicked.connect(lambda: self._on_priority_button_clicked(dialog, "row"))
                print("默认选中行方向优先")

            # 为列方向优先按钮添加点击事件
            if col_priority_btn:
                col_priority_btn.clicked.connect(lambda: self._on_priority_button_clicked(dialog, "col"))

            # 获取摆放点按钮和对应的显示框
            left_top_btn = dialog.findChild(QtWidgets.QPushButton, "pushButton")      # 左上摆放点获取按钮
            right_top_btn = dialog.findChild(QtWidgets.QPushButton, "pushButton_3")   # 右上摆放点获取按钮
            left_bottom_btn = dialog.findChild(QtWidgets.QPushButton, "pushButton_2") # 左下摆放点获取按钮

            left_top_label = dialog.findChild(QtWidgets.QLabel, "label")      # 左上摆放点显示框
            right_top_label = dialog.findChild(QtWidgets.QLabel, "label_6")   # 右上摆放点显示框
            left_bottom_label = dialog.findChild(QtWidgets.QLabel, "label_3") # 左下摆放点显示框

            # 为摆放点获取按钮添加点击事件
            if left_top_btn and left_top_label:
                left_top_btn.clicked.connect(lambda: self._get_tcp_position_and_display(left_top_label, "左上摆放点"))
            if right_top_btn and right_top_label:
                right_top_btn.clicked.connect(lambda: self._get_tcp_position_and_display(right_top_label, "右上摆放点"))
            if left_bottom_btn and left_bottom_label:
                left_bottom_btn.clicked.connect(lambda: self._get_tcp_position_and_display(left_bottom_label, "左下摆放点"))

            if confirm_btn:
                confirm_btn.clicked.connect(lambda: self._on_material_frame_confirm(dialog))
            if cancel_btn:
                cancel_btn.clicked.connect(dialog.close)

            # 加载当前步骤的配置数据到对话框
            self._load_current_step_config_to_dialog(dialog)

            dialog.show()
            print("叶片摆放配置界面已打开")

        except Exception as e:
            import traceback
            traceback.print_exc()
            QMessageBox.warning(self.main_window, "错误", f"无法打开叶片摆放配置界面: {str(e)}")

    def _on_priority_button_clicked(self, dialog, priority_type):
        """处理优先级按钮点击事件"""
        try:
            row_priority_btn = dialog.findChild(QtWidgets.QPushButton, "pushButton_6")  # 行方向优先
            col_priority_btn = dialog.findChild(QtWidgets.QPushButton, "pushButton_7")  # 列方向优先

            # 定义选中和未选中的样式
            selected_style = """
                QPushButton {
                    border: 2px solid black;
                    color: white;
                    font-size: 20px;
                    background-color: rgb(255, 95, 0);  /* 选中状态为橙色 */
                }
                QPushButton:pressed {
                    background-color: rgb(255, 95, 0);
                }
            """

            unselected_style = """
                QPushButton {
                    border: 2px solid black;
                    color: white;
                    font-size: 20px;
                    background-color: transparent;  /* 未选中状态为透明 */
                }
                QPushButton:pressed {
                    background-color: rgb(255, 95, 0);
                }
            """

            if priority_type == "row":
                # 选中行方向优先，取消列方向优先
                if row_priority_btn:
                    row_priority_btn.setStyleSheet(selected_style)
                if col_priority_btn:
                    col_priority_btn.setStyleSheet(unselected_style)
                print("选中行方向优先")
            elif priority_type == "col":
                # 选中列方向优先，取消行方向优先
                if row_priority_btn:
                    row_priority_btn.setStyleSheet(unselected_style)
                if col_priority_btn:
                    col_priority_btn.setStyleSheet(selected_style)
                print("选中列方向优先")

        except Exception as e:
            print(f"处理优先级按钮点击失败: {e}")

    def _load_current_step_config_to_dialog(self, dialog):
        """从当前步骤数据中加载配置信息到对话框"""
        try:
            # 检查是否有当前步骤数据
            if not hasattr(self, 'current_task_process') or not self.current_task_process:
                print("没有可用的任务处理步骤")
                return

            if not (0 <= self.current_step_index < len(self.current_task_process)):
                print(f"当前步骤索引超出范围: {self.current_step_index}")
                return

            # 获取当前步骤数据
            current_step = self.current_task_process[self.current_step_index]
            print(f"正在加载步骤 {self.current_step_index} 的配置数据: {current_step.get('action', 'Unknown')}")

            # 提取配置数据
            config_data = self._extract_config_from_step(current_step)
            if not config_data:
                print("未找到配置数据，使用默认值")
                return

            # 填充UI控件
            self._populate_dialog_with_config(dialog, config_data)
            print("配置数据已加载到对话框")

        except Exception as e:
            print(f"加载配置数据失败: {e}")
            import traceback
            traceback.print_exc()

    def _extract_config_from_step(self, step):
        """从步骤数据中提取配置信息"""
        try:
            config_data = {}

            # 检查步骤结构，从commands_list中提取数据
            if 'commands_list' in step and len(step['commands_list']) > 0:
                command = step['commands_list'][0]
                if 'kwargs' in command:
                    kwargs = command['kwargs']

                    # 提取各项配置
                    config_data['first_pos'] = kwargs.get('first_pos', [0.0, 0.0, 0.0, 0.0, 0.0, 0.0])
                    config_data['row_space'] = kwargs.get('row_space', [0.0, 0.0, 0.0])
                    config_data['col_space'] = kwargs.get('col_space', [0.0, 0.0, 0.0])
                    config_data['row_num'] = kwargs.get('row_num', 1)
                    config_data['col_num'] = kwargs.get('col_num', 1)
                    config_data['row_first'] = kwargs.get('row_first', True)

                    print(f"从commands_list中提取到配置数据: {config_data}")
                    return config_data

            # 如果commands_list中没有数据，检查步骤顶层
            if 'first_pos' in step:
                config_data['first_pos'] = step.get('first_pos', [0.0, 0.0, 0.0, 0.0, 0.0, 0.0])
                config_data['row_space'] = step.get('row_space', [0.0, 0.0, 0.0])
                config_data['col_space'] = step.get('col_space', [0.0, 0.0, 0.0])
                config_data['row_num'] = step.get('row_num', 1)
                config_data['col_num'] = step.get('col_num', 1)
                config_data['row_first'] = step.get('row_first', True)

                print(f"从步骤顶层提取到配置数据: {config_data}")
                return config_data

            print("未找到配置数据")
            return None

        except Exception as e:
            print(f"提取配置数据失败: {e}")
            return None

    def _populate_dialog_with_config(self, dialog, config_data):
        """使用配置数据填充对话框UI控件"""
        try:
            # 1. 填充行方向数量和列方向数量
            row_spinbox = dialog.findChild(QtWidgets.QSpinBox, "spinBox")      # 行方向数量
            col_spinbox = dialog.findChild(QtWidgets.QSpinBox, "spinBox_2")    # 列方向数量

            if row_spinbox:
                row_spinbox.setValue(config_data['row_num'])
                print(f"设置行方向数量: {config_data['row_num']}")

            if col_spinbox:
                col_spinbox.setValue(config_data['col_num'])
                print(f"设置列方向数量: {config_data['col_num']}")

            # 2. 设置行方向优先/列方向优先
            row_first = config_data['row_first']
            if row_first:
                self._on_priority_button_clicked(dialog, "row")
                print("设置为行方向优先")
            else:
                self._on_priority_button_clicked(dialog, "col")
                print("设置为列方向优先")

            # 3. 显示行间距和列间距
            self._display_spacing_result(dialog, config_data['row_space'], config_data['col_space'])

            # 4. 计算并显示三个摆放点的坐标
            placement_points = self._calculate_placement_points(
                config_data['first_pos'],
                config_data['row_space'],
                config_data['col_space'],
                config_data['row_first']
            )

            if placement_points:
                self._display_placement_points(dialog, placement_points)

        except Exception as e:
            print(f"填充对话框数据失败: {e}")
            import traceback
            traceback.print_exc()

    def _calculate_placement_points(self, first_pos, row_space, col_space, row_first):
        """根据first_pos、row_space、col_space和row_first计算三个摆放点的坐标"""
        try:
            # first_pos就是左上摆放点
            left_top_pos = first_pos.copy()

            if row_first:
                # 行优先模式：
                # row_space = 左上到左下的向量（行间距）
                # col_space = 左上到右上的向量（列间距）
                # 所以：右上摆放点 = 左上摆放点 + col_space
                #      左下摆放点 = 左上摆放点 + row_space
                right_top_pos = [
                    first_pos[0] + col_space[0],
                    first_pos[1] + col_space[1],
                    first_pos[2] + col_space[2],
                    first_pos[3], first_pos[4], first_pos[5]  # 保持姿态不变
                ]

                left_bottom_pos = [
                    first_pos[0] + row_space[0],
                    first_pos[1] + row_space[1],
                    first_pos[2] + row_space[2],
                    first_pos[3], first_pos[4], first_pos[5]  # 保持姿态不变
                ]
            else:
                # 列优先模式：
                # col_space = 左上到左下的向量（列间距）
                # row_space = 左上到右上的向量（行间距）
                # 所以：右上摆放点 = 左上摆放点 + row_space
                #      左下摆放点 = 左上摆放点 + col_space
                right_top_pos = [
                    first_pos[0] + row_space[0],
                    first_pos[1] + row_space[1],
                    first_pos[2] + row_space[2],
                    first_pos[3], first_pos[4], first_pos[5]  # 保持姿态不变
                ]

                left_bottom_pos = [
                    first_pos[0] + col_space[0],
                    first_pos[1] + col_space[1],
                    first_pos[2] + col_space[2],
                    first_pos[3], first_pos[4], first_pos[5]  # 保持姿态不变
                ]

            placement_points = {
                'left_top': left_top_pos,
                'right_top': right_top_pos,
                'left_bottom': left_bottom_pos
            }

            print(f"计算得到摆放点坐标:")
            print(f"  左上摆放点: {left_top_pos}")
            print(f"  右上摆放点: {right_top_pos}")
            print(f"  左下摆放点: {left_bottom_pos}")

            return placement_points

        except Exception as e:
            print(f"计算摆放点坐标失败: {e}")
            return None

    def _display_placement_points(self, dialog, placement_points):
        """在对话框中显示三个摆放点的坐标"""
        try:
            # 获取三个摆放点的显示标签
            left_top_label = dialog.findChild(QtWidgets.QLabel, "label")       # 左上摆放点
            right_top_label = dialog.findChild(QtWidgets.QLabel, "label_6")    # 右上摆放点
            left_bottom_label = dialog.findChild(QtWidgets.QLabel, "label_3")  # 左下摆放点

            # 格式化并显示左上摆放点
            if left_top_label and 'left_top' in placement_points:
                pos = placement_points['left_top']
                position_text = f"{pos[0]:.2f}\n{pos[1]:.2f}\n{pos[2]:.2f}\n{pos[3]:.2f}\n{pos[4]:.2f}\n{pos[5]:.2f}"
                left_top_label.setText(position_text)
                print(f"左上摆放点已显示: {pos}")

            # 格式化并显示右上摆放点
            if right_top_label and 'right_top' in placement_points:
                pos = placement_points['right_top']
                position_text = f"{pos[0]:.2f}\n{pos[1]:.2f}\n{pos[2]:.2f}\n{pos[3]:.2f}\n{pos[4]:.2f}\n{pos[5]:.2f}"
                right_top_label.setText(position_text)
                print(f"右上摆放点已显示: {pos}")

            # 格式化并显示左下摆放点
            if left_bottom_label and 'left_bottom' in placement_points:
                pos = placement_points['left_bottom']
                position_text = f"{pos[0]:.2f}\n{pos[1]:.2f}\n{pos[2]:.2f}\n{pos[3]:.2f}\n{pos[4]:.2f}\n{pos[5]:.2f}"
                left_bottom_label.setText(position_text)
                print(f"左下摆放点已显示: {pos}")

        except Exception as e:
            print(f"显示摆放点坐标失败: {e}")

    def _get_tcp_position_and_display(self, label_widget, position_name):
        """获取TCP位置并显示在指定的标签控件中"""
        try:
            if not self.inspect_service_available or not self.inspect_service:
                QMessageBox.warning(self.main_window, "错误", "机器人服务不可用")
                return

            print(f"开始获取{position_name}的TCP位置...")

            # 获取TCP位置
            tcp_response: Response = self.inspect_service.robot_tcp_position_client.call(request=Request())

            if tcp_response.code != Codes.SUCCESS:
                QMessageBox.warning(self.main_window, "错误", f"获取{position_name}失败: 机器人操作错误")
                return

            # 从响应中提取坐标数据
            if hasattr(tcp_response, 'data') and tcp_response.data:
                position_data = tcp_response.data
                print(f"原始数据: {position_data}")
                print(f"数据类型: {type(position_data)}")

                # 处理不同的数据格式
                coordinates = None

                # 情况1: 数据是字典格式，包含 'tcp_position' 键
                if isinstance(position_data, dict) and 'tcp_position' in position_data:
                    coordinates = position_data['tcp_position']
                    print(f"从字典中提取tcp_position: {coordinates}")

                # 情况2: 数据直接是列表或元组格式
                elif isinstance(position_data, (list, tuple)) and len(position_data) >= 6:
                    coordinates = position_data
                    print(f"直接使用列表数据: {coordinates}")

                # 情况3: 数据是字符串格式，需要解析
                elif isinstance(position_data, str):
                    try:
                        import json
                        parsed_data = json.loads(position_data)
                        if isinstance(parsed_data, dict) and 'tcp_position' in parsed_data:
                            coordinates = parsed_data['tcp_position']
                        elif isinstance(parsed_data, (list, tuple)):
                            coordinates = parsed_data
                        print(f"从字符串解析数据: {coordinates}")
                    except:
                        print(f"字符串解析失败: {position_data}")

                # 如果成功提取到坐标数据，格式化显示
                if coordinates and isinstance(coordinates, (list, tuple)) and len(coordinates) >= 6:
                    x, y, z, rx, ry, rz = coordinates[:6]
                    # 格式化坐标显示，每行一个值，保留2位小数
                    position_text = f"{x:.2f}\n{y:.2f}\n{z:.2f}\n{rx:.2f}\n{ry:.2f}\n{rz:.2f}"
                    label_widget.setText(position_text)
                    print(f"{position_name}获取成功: X:{x:.2f}, Y:{y:.2f}, Z:{z:.2f}, RX:{rx:.2f}, RY:{ry:.2f}, RZ:{rz:.2f}")
                else:
                    # 如果无法解析，显示错误信息而不是原始数据
                    label_widget.setText("数据格式错误")
                    print(f"{position_name}获取失败，无法解析坐标数据: {position_data}")
                    QMessageBox.warning(self.main_window, "错误", f"获取{position_name}失败: 坐标数据格式不正确")
            else:
                QMessageBox.warning(self.main_window, "错误", f"获取{position_name}失败: 响应数据为空")

        except Exception as e:
            import traceback
            traceback.print_exc()
            QMessageBox.warning(self.main_window, "错误", f"获取{position_name}失败: {str(e)}")

    def _on_material_frame_confirm(self, dialog):
        """处理material_frame对话框的确认操作"""
        try:
            # 获取UI控件
            row_spinbox = dialog.findChild(QtWidgets.QSpinBox, "spinBox")      # 行方向数量
            col_spinbox = dialog.findChild(QtWidgets.QSpinBox, "spinBox_2")    # 列方向数量

            left_top_label = dialog.findChild(QtWidgets.QLabel, "label")       # 左上摆放点
            right_top_label = dialog.findChild(QtWidgets.QLabel, "label_6")    # 右上摆放点
            left_bottom_label = dialog.findChild(QtWidgets.QLabel, "label_3")  # 左下摆放点

            row_priority_btn = dialog.findChild(QtWidgets.QPushButton, "pushButton_6")  # 行方向优先
            col_priority_btn = dialog.findChild(QtWidgets.QPushButton, "pushButton_7")  # 列方向优先

            # 获取行列数量
            row_count = row_spinbox.value() if row_spinbox else 1
            col_count = col_spinbox.value() if col_spinbox else 1

            # 判断是否为行方向优先（通过按钮样式判断）
            row_first = True  # 默认行方向优先
            if row_priority_btn and col_priority_btn:
                # 检查哪个按钮被选中（橙色背景）
                row_style = row_priority_btn.styleSheet()
                col_style = col_priority_btn.styleSheet()
                if "rgb(255, 95, 0)" in row_style:
                    row_first = True
                elif "rgb(255, 95, 0)" in col_style:
                    row_first = False

            # 获取三个摆放点的坐标
            coordinates = self._extract_coordinates_from_labels(left_top_label, right_top_label, left_bottom_label)
            if not coordinates:
                QMessageBox.warning(self.main_window, "错误", "请先获取所有三个摆放点的坐标")
                return

            left_top_pos, right_top_pos, left_bottom_pos = coordinates

            # 计算行列间距
            row_space, col_space = self._calculate_spacing(left_top_pos, right_top_pos, left_bottom_pos, row_count, col_count, row_first)

            # 显示计算结果在右下角的行列间距框中
            self._display_spacing_result(dialog, row_space, col_space)

            # 保存到当前步骤
            self._save_placement_config(left_top_pos, row_space, col_space, row_count, col_count, row_first)

            print(f"叶片摆放配置已保存 - 行数: {row_count}, 列数: {col_count}, 行优先: {row_first}")

            QMessageBox.information(self.main_window, "配置成功",
                                  f"叶片摆放配置已更新并保存到当前步骤")

            # 关闭对话框 - 根据对话框类型选择合适的关闭方法
            if hasattr(dialog, 'accept'):
                dialog.accept()  # QDialog类型
            elif hasattr(dialog, 'close'):
                dialog.close()   # QMainWindow类型
            else:
                print("无法关闭对话框：未知的对话框类型")

        except Exception as e:
            import traceback
            traceback.print_exc()
            QMessageBox.warning(self.main_window, "错误", f"保存配置失败: {str(e)}")

    def _extract_coordinates_from_labels(self, left_top_label, right_top_label, left_bottom_label):
        """从标签中提取坐标数据"""
        try:
            coordinates = []
            labels = [left_top_label, right_top_label, left_bottom_label]
            label_names = ["左上摆放点", "右上摆放点", "左下摆放点"]

            for label, name in zip(labels, label_names):
                if not label:
                    print(f"找不到{name}标签")
                    return None

                text = label.text().strip()
                if not text or text == "数据格式错误":
                    print(f"{name}坐标未获取或格式错误: {text}")
                    return None

                # 解析坐标文本，格式为每行一个数值
                lines = text.split('\n')
                if len(lines) < 6:
                    print(f"{name}坐标数据不完整: {lines}")
                    return None

                try:
                    coord = [float(line.strip()) for line in lines[:6]]
                    coordinates.append(coord)
                    print(f"{name}坐标: {coord}")
                except ValueError as e:
                    print(f"{name}坐标解析失败: {e}")
                    return None

            return coordinates

        except Exception as e:
            print(f"提取坐标失败: {e}")
            return None

    def _calculate_spacing(self, left_top_pos, right_top_pos, left_bottom_pos, row_count, col_count, row_first):
        """计算行列间距，根据优先级选择不同的计算方式"""
        try:
            print(f"计算间距 - 行数: {row_count}, 列数: {col_count}, 行优先: {row_first}")

            if row_first:
                # 行优先：先排行，再排列
                # 行间距：从左上到左下的向量除以行数-1
                if row_count > 1:
                    row_space = [
                        (left_bottom_pos[0] - left_top_pos[0]) / (row_count - 1),
                        (left_bottom_pos[1] - left_top_pos[1]) / (row_count - 1),
                        (left_bottom_pos[2] - left_top_pos[2]) / (row_count - 1)
                    ]
                else:
                    row_space = [0.0, 0.0, 0.0]

                # 列间距：从左上到右上的向量除以列数-1
                if col_count > 1:
                    col_space = [
                        (right_top_pos[0] - left_top_pos[0]) / (col_count - 1),
                        (right_top_pos[1] - left_top_pos[1]) / (col_count - 1),
                        (right_top_pos[2] - left_top_pos[2]) / (col_count - 1)
                    ]
                else:
                    col_space = [0.0, 0.0, 0.0]

                print("行优先模式：行间距=左上到左下，列间距=左上到右上")

            else:
                # 列优先：先排列，再排行
                # 列间距：从左上到左下的向量除以列数-1（注意：这里和行优先不同）
                if col_count > 1:
                    col_space = [
                        (left_bottom_pos[0] - left_top_pos[0]) / (col_count - 1),
                        (left_bottom_pos[1] - left_top_pos[1]) / (col_count - 1),
                        (left_bottom_pos[2] - left_top_pos[2]) / (col_count - 1)
                    ]
                else:
                    col_space = [0.0, 0.0, 0.0]

                # 行间距：从左上到右上的向量除以行数-1（注意：这里和行优先不同）
                if row_count > 1:
                    row_space = [
                        (right_top_pos[0] - left_top_pos[0]) / (row_count - 1),
                        (right_top_pos[1] - left_top_pos[1]) / (row_count - 1),
                        (right_top_pos[2] - left_top_pos[2]) / (row_count - 1)
                    ]
                else:
                    row_space = [0.0, 0.0, 0.0]

                print("列优先模式：列间距=左上到左下，行间距=左上到右上")

            print(f"计算得到行间距: {row_space}")
            print(f"计算得到列间距: {col_space}")

            return row_space, col_space

        except Exception as e:
            print(f"计算间距失败: {e}")
            return [0.0, 0.0, 0.0], [0.0, 0.0, 0.0]

    def _display_spacing_result(self, dialog, row_space, col_space):
        """在右下角的行列间距框中显示计算结果"""
        try:
            # 根据UI文件，行间距显示控件是 label_11，列间距显示控件是 label_12
            row_spacing_label = dialog.findChild(QtWidgets.QLabel, "label_11")  # 行间距显示
            col_spacing_label = dialog.findChild(QtWidgets.QLabel, "label_12")  # 列间距显示

            # 格式化间距文本，保留2位小数
            row_spacing_text = f"X:{row_space[0]:.2f} Y:{row_space[1]:.2f} Z:{row_space[2]:.2f}"
            col_spacing_text = f"X:{col_space[0]:.2f} Y:{col_space[1]:.2f} Z:{col_space[2]:.2f}"

            # 更新显示控件
            if row_spacing_label:
                row_spacing_label.setText(row_spacing_text)
                print(f"行间距已更新显示: {row_spacing_text}")
            else:
                print("找不到行间距显示控件 label_11")

            if col_spacing_label:
                col_spacing_label.setText(col_spacing_text)
                print(f"列间距已更新显示: {col_spacing_text}")
            else:
                print("找不到列间距显示控件 label_12")

        except Exception as e:
            print(f"显示间距结果失败: {e}")

    def _save_placement_config(self, first_pos, row_space, col_space, row_num, col_num, row_first):
        """保存摆放配置到当前步骤，同时也保存到所有BoxReleaseAction步骤"""
        try:
            if not hasattr(self, 'current_task_process') or not self.current_task_process:
                print("没有可用的任务处理步骤")
                return

            if not (0 <= self.current_step_index < len(self.current_task_process)):
                print(f"当前步骤索引超出范围: {self.current_step_index}")
                return

            # 定义保存配置数据到步骤的函数
            def save_config_to_step(step, step_index):
                """将配置数据保存到指定步骤"""
                # 检查步骤结构，确定数据应该保存在哪里
                if 'commands_list' in step and len(step['commands_list']) > 0:
                    # 如果有commands_list，数据应该保存在第一个command的kwargs中
                    command = step['commands_list'][0]
                    if 'kwargs' not in command:
                        command['kwargs'] = {}

                    # 更新command的kwargs
                    command['kwargs']['first_pos'] = first_pos
                    command['kwargs']['row_space'] = row_space
                    command['kwargs']['col_space'] = col_space
                    command['kwargs']['row_num'] = row_num
                    command['kwargs']['col_num'] = col_num
                    command['kwargs']['row_first'] = row_first

                    print(f"数据已保存到步骤 {step_index} 的 commands_list[0]['kwargs']")
                else:
                    # 如果没有commands_list，保存到步骤顶层
                    step["first_pos"] = first_pos
                    step["row_space"] = row_space
                    step["col_space"] = col_space
                    step["row_num"] = row_num
                    step["col_num"] = col_num
                    step["row_first"] = row_first

                    print(f"数据已保存到步骤 {step_index} 的顶层")

            # 保存到当前步骤
            current_step = self.current_task_process[self.current_step_index]
            save_config_to_step(current_step, self.current_step_index)

            # 查找并更新所有BoxReleaseAction步骤（排除当前步骤以避免重复保存）
            box_release_count = 0
            for i, step in enumerate(self.current_task_process):
                if step.get('action') == 'BoxReleaseAction' and i != self.current_step_index:
                    save_config_to_step(step, i)
                    box_release_count += 1
            print(333, self.current_task_process[self.current_step_index])

            # 刷新程序步骤显示
            self.refresh_program_steps()
            print("已刷新程序步骤显示")

            # 更新inspect_service
            if self.inspect_service_available and self.inspect_service:
                self.inspect_service.update(process=self.current_task_process)
                print("已更新inspect_service")
            else:
                print("inspect_service不可用，跳过更新")

        except Exception as e:
            print(f"保存摆放配置失败: {e}")

    def _show_robot_position_dialog(self, request_type):
        """显示机器人点位选择对话框"""
        try:
            try:
                from templates.robot_select_dialog import RobotSelectDialog
            except ImportError as ie:
                raise ie

            # 创建对话框，传递程序模块引用
            dialog = RobotSelectDialog(self.main_window, program_module=self)

            # 设置对话框参数
            options = {
                "parent": self.main_window,
                "request": request_type,  # "insert" 或 "modify"
                "speed": 40.0,
                "accuracy": -1,
                "is_absolute": True,
                "is_joint": True,
                "is_insert_front": False
            }

            dialog.show_dialog(**options)

        except Exception as e:
            import traceback
            traceback.print_exc()
            QMessageBox.warning(self.main_window, "错误", f"无法打开机器人点位选择对话框: {str(e)}")

    def on_tcp_step_changed(self):
        """TCP步长值改变处理"""
        try:
            if self.tcp_step_widget:
                value = float(self.tcp_step_widget.text())
                # 限制范围0-5
                if 0 <= value <= 5:
                    self.tcp_step_value = value
                    print(f"TCP步长设置为: {self.tcp_step_value}")
                else:
                    raise ValueError("步长值超出范围")
        except ValueError:
            # 输入无效，恢复原值
            self.tcp_step_widget.setText(str(self.tcp_step_value))
            QMessageBox.warning(self.main_window, "输入错误", "请输入有效的数字！")
            print(f"TCP步长输入无效，恢复为: {self.tcp_step_value}")

    def on_joint_step_changed(self):
        """关节步长值改变处理"""
        try:
            if self.joint_step_widget:
                value = float(self.joint_step_widget.text())
                # 限制范围0-5
                if 0 <= value <= 5:
                    self.joint_step_value = value
                    print(f"关节步长设置为: {self.joint_step_value}")
                else:
                    raise ValueError("步长值超出范围")
        except ValueError:
            # 输入无效，恢复原值
            self.joint_step_widget.setText(str(self.joint_step_value))
            QMessageBox.warning(self.main_window, "输入错误", "请输入有效的数字！")
            print(f"关节步长输入无效，恢复为: {self.joint_step_value}")

    def on_robot_exit(self):
        """机器人控制界面退出按钮处理 - 返回程序列表"""
        try:
            print("点击了机器人控制界面的退出按钮")

            # 确认退出当前程序编辑
            reply = QMessageBox.question(
                self.main_window,
                "返回程序列表",
                "确定要退出当前程序的编辑吗？\n\n将返回到程序列表界面。",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # 清理当前编辑状态
                self.close_edit_mode()

                # 只切换回程序列表标签页，不退出程序编辑界面
                if hasattr(self.main_window, 'program_tab_widget') and self.main_window.program_tab_widget:
                    self.main_window.program_tab_widget.setCurrentIndex(0)  # 切换到程序列表标签页（索引0）

                    # 显示成功提示
                    QMessageBox.information(self.main_window, "返回成功", "已返回程序列表界面")
                else:
                    QMessageBox.warning(self.main_window, "切换失败", "无法切换到程序列表界面")
            else:
                print("用户取消退出操作")

        except Exception as e:
            print(f"返回程序列表失败: {e}")
            QMessageBox.warning(self.main_window, "错误", f"返回程序列表失败: {str(e)}")

    def cleanup(self):
        """清理模块资源"""
        try:
            print("🧹 清理程序模块资源...")

            # 停止可能的定时器
            # 注意：QTimer.singleShot无法直接停止，但我们可以设置标志位
            self._is_cleaning_up = True

            # 清理编辑状态
            self.close_edit_mode()

            # 断开inspect_service连接
            if hasattr(self, 'inspect_service') and self.inspect_service:
                try:
                    self.inspect_service.destroy()
                except Exception as e:
                    print(f"断开inspect_service连接失败: {e}")

            # 保存程序数据
            self.save_programs_data()


        except Exception as e:
            import traceback
            traceback.print_exc()

    # ==================== 步骤编辑功能实现 ====================

    def modify(self, step_process: dict, **options) -> tuple:
        """
        修改当前步骤 - 参照templates/executor.py的modify函数实现

        Args:
            step_process: 步骤处理字典，包含command_matrix信息
            **options: 其他选项参数

        Returns:
            tuple: (code, information) - 操作结果码和信息
        """
        try:

            # 检查是否有可修改的步骤
            if not self.current_task_process or self.current_step_index < 0:
                return 1, {"error": "没有可修改的步骤"}

            if self.current_step_index >= len(self.current_task_process):
                return 1, {"error": "步骤索引超出范围"}

            # 先更新本地数据
            self.current_task_process[self.current_step_index] = step_process

            # 调用refresh_program_steps刷新步骤显示和更新任务关系
            self.refresh_program_steps()

            # 通过InspectService调用update方法同步到robotics系统
            if hasattr(self, 'inspect_service') and self.inspect_service:
                code = self.inspect_service.update(process=self.current_task_process)

                if code == 0:  # 假设0表示成功
                    return 0, {"message": "步骤修改成功"}
                else:
                    return code, {"error": f"步骤修改失败，错误码: {code}"}
            else:
                return 1, {"error": "InspectService不可用"}

        except Exception as e:
            import traceback
            traceback.print_exc()
            return 1, {"error": f"修改步骤异常: {str(e)}"}

    def add(self, step_process: dict, insert: str = "front", **options) -> tuple:
        """
        添加新步骤 - 参照templates/executor.py的add函数实现

        Args:
            step_process: 步骤处理字典，包含command_matrix信息
            insert: 插入位置，"front"表示当前步骤之前，"back"表示当前步骤之后
            **options: 其他选项参数

        Returns:
            tuple: (code, information) - 操作结果码和信息
        """
        try:

            # 检查是否有可用的步骤列表
            if not self.current_task_process:
                return 1, {"error": "没有可用的任务处理步骤"}

            if self.current_step_index < 0:
                return 1, {"error": "当前步骤索引无效"}

            # 确定插入位置
            if insert == "front":
                insert_index = self.current_step_index
            else:  # "back"
                insert_index = self.current_step_index + 1

            print(f"📍 插入位置: {insert_index}")

            # 先更新本地数据
            self.current_task_process.insert(insert_index, step_process)

            # 调用refresh_program_steps刷新步骤显示和更新任务关系
            self.refresh_program_steps()

            # 通过InspectService调用update方法同步到robotics系统
            if hasattr(self, 'inspect_service') and self.inspect_service:
                code = self.inspect_service.update(process=self.current_task_process)

                if code == 0:  # 假设0表示成功
                    # 成功后，将光标指向新插入的步骤
                    self.current_step_index = insert_index

                    # 重新更新步骤高亮显示，确保箭头指向新插入的步骤
                    if hasattr(self, 'program_list_widget') and self.program_list_widget:
                        self.update_step_highlight()
                        self.update_step_display()

                    return 0, {"message": "步骤添加成功"}
                else:
                    # 如果失败，回滚本地数据
                    self.current_task_process.pop(insert_index)
                    return code, {"error": f"步骤添加失败，错误码: {code}"}
            else:
                # 回滚本地数据
                self.current_task_process.pop(insert_index)
                return 1, {"error": "InspectService不可用"}

        except Exception as e:
            import traceback
            traceback.print_exc()
            return 1, {"error": f"添加步骤异常: {str(e)}"}

    def delete(self, **options) -> tuple:
        """
        删除当前步骤 - 参照templates/executor.py的delete函数实现

        Args:
            **options: 其他选项参数

        Returns:
            tuple: (code, information) - 操作结果码和信息
        """
        try:

            # 检查是否有可删除的步骤
            if not self.current_task_process or self.current_step_index < 0:
                return 1, {"error": "没有可删除的步骤"}

            if self.current_step_index >= len(self.current_task_process):
                return 1, {"error": "步骤索引超出范围"}

            # 保存要删除的步骤（用于回滚）
            deleted_step = self.current_task_process[self.current_step_index]

            # 从本地数据中删除步骤
            self.current_task_process.pop(self.current_step_index)

            # 调用refresh_program_steps刷新步骤显示和更新任务关系
            self.refresh_program_steps()

            # 通过InspectService调用update方法同步到robotics系统
            if hasattr(self, 'inspect_service') and self.inspect_service:
                code = self.inspect_service.update(process=self.current_task_process)

                if code == 0:  # 假设0表示成功
                    # 成功后，调整当前步骤索引 - 指向下一步骤
                    if len(self.current_task_process) == 0:
                        # 如果删除后没有步骤了
                        self.current_step_index = -1
                    elif self.current_step_index >= len(self.current_task_process):
                        # 如果删除的是最后一步，指向新的最后一步
                        self.current_step_index = len(self.current_task_process) - 1
                    # 否则保持当前索引不变，这样就指向了原来的下一步

                    # 重新更新步骤高亮显示，确保箭头指向正确的步骤
                    if hasattr(self, 'program_list_widget') and self.program_list_widget:
                        self.update_step_highlight()
                        self.update_step_display()

                    return 0, {"message": "步骤删除成功"}
                else:
                    # 如果失败，回滚本地数据
                    self.current_task_process.insert(self.current_step_index, deleted_step)
                    return code, {"error": f"步骤删除失败，错误码: {code}"}
            else:
                # 回滚本地数据
                self.current_task_process.insert(self.current_step_index, deleted_step)
                return 1, {"error": "InspectService不可用"}

        except Exception as e:
            import traceback
            traceback.print_exc()
            return 1, {"error": f"删除步骤异常: {str(e)}"}

    def refresh_program_steps(self):
        """
        刷新程序步骤显示，并重新更新process中每一步的post_tasks、pre_tasks、task_id
        """
        try:

            if not self.current_task_process:
                return

            # 重新编号和更新task_id、pre_tasks、post_tasks
            self.update_process_task_relationships()

            # 重新生成程序步骤显示（不重置光标位置）
            self.update_program_steps_from_json(reset_cursor=False)

            # 更新UI显示
            if hasattr(self, 'program_list_widget') and self.program_list_widget:
                self.update_step_highlight()
                self.update_step_display()
        except Exception as e:
            import traceback
            traceback.print_exc()

    def update_process_task_relationships(self):
        """
        更新process中每一步的post_tasks、pre_tasks、task_id
        """
        try:

            if not self.current_task_process:
                return

            # 重新编号所有步骤的task_id
            for i, step in enumerate(self.current_task_process):
                # 更新task_id
                step["task_id"] = i + 1

                # 更新pre_tasks（前置任务）
                if i == 0:
                    step["pre_tasks"] = []  # 第一个任务没有前置任务
                else:
                    step["pre_tasks"] = [i]  # 前一个任务作为前置任务

                # 更新post_tasks（后置任务）
                if i == len(self.current_task_process) - 1:
                    step["post_tasks"] = []  # 最后一个任务没有后置任务
                else:
                    step["post_tasks"] = [i + 2]  # 下一个任务作为后置任务



        except Exception as e:
            import traceback
            traceback.print_exc()

    def on_robot_delete(self):
        """机器人删除当前步骤"""
        try:
            if not self.current_program_steps or self.current_step_index < 0:
                QMessageBox.warning(self.main_window, "警告", "没有可删除的步骤！")
                return

            current_step = self.current_program_steps[self.current_step_index]

            # 显示删除确认对话框
            reply = QMessageBox.question(
                self.main_window,
                "确认删除",
                f"确定要删除当前步骤吗？\n\n步骤: {current_step}\n\n此操作不可撤销！",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # 调用删除函数
                code, result = self.delete()
                if code == 0:  # 假设0表示成功
                    # delete函数内部已经调用了refresh_program_steps，无需重复调用
                    QMessageBox.information(
                        self.main_window,
                        "删除成功",
                        f"步骤已删除: {current_step}"
                    )
                else:
                    QMessageBox.warning(
                        self.main_window,
                        "删除失败",
                        f"删除步骤失败: {result.get('error', '未知错误')}"
                    )


        except Exception as e:
            print(f"删除步骤失败: {e}")
            QMessageBox.warning(self.main_window, "错误", f"删除步骤失败: {str(e)}")
