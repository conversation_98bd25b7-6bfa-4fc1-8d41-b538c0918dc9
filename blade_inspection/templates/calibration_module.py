#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json
import time
import threading
from datetime import datetime
from PyQt5.QtWidgets import (QMessageBox, QProgressDialog, QInputDialog, 
                             QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                             QPushButton, QTextEdit, QGroupBox, QGridLayout)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QObject
from PyQt5 import QtWidgets


class CalibrationModule(QObject):
    """设备校准功能模块"""
    
    # 定义信号
    calibration_started = pyqtSignal(str)  # 校准开始信号
    calibration_progress = pyqtSignal(str, int)  # 校准进度信号
    calibration_completed = pyqtSignal(str, bool, str)  # 校准完成信号 (设备名, 是否成功, 结果信息)
    
    def __init__(self, main_window):
        """初始化校准模块"""
        super().__init__()
        self.main_window = main_window
        
        # 校准相关属性
        self.calibration_data_file = "calibration_data.json"  # 校准数据持久化文件
        self.calibration_history = []  # 校准历史记录
        self.current_calibration = None  # 当前正在进行的校准
        
        # 设备校准按钮映射
        self.calibration_buttons = {
            "sensor1_calibrate_btn": "传感器1自检",
            "sensor2_calibrate_btn": "传感器2自检", 
            "sensor3_calibrate_btn": "传感器3自检",
            "camera_calibrate_btn": "相机标定",
            "robot_calibrate_btn": "机器人标定",
            "gripper_calibrate_btn": "夹爪标定",
            "auto_calibrate_btn": "自动校准"
        }
        
        # 加载校准历史数据
        self.load_calibration_data()

    def setup_calibration_signals(self):
        """设置校准按钮信号连接"""
        try:
            # 获取所有校准按钮并连接信号
            calibrate_buttons = [
                (getattr(self.main_window, "sensor1_calibrate_btn", None), "传感器1自检"),
                (getattr(self.main_window, "sensor2_calibrate_btn", None), "传感器2自检"),
                (getattr(self.main_window, "sensor3_calibrate_btn", None), "传感器3自检"),
                (getattr(self.main_window, "camera_calibrate_btn", None), "相机标定"),
                (getattr(self.main_window, "robot_calibrate_btn", None), "机器人标定"),
                (getattr(self.main_window, "gripper_calibrate_btn", None), "夹爪标定"),
                (getattr(self.main_window, "auto_calibrate_btn", None), "自动校准")
            ]

            for btn, name in calibrate_buttons:
                if btn:
                    btn.clicked.connect(lambda checked=False, device=name: self.calibrate_device(device))
                    print(f"✅ 已连接校准按钮: {name}")
                else:
                    print(f"⚠️  校准按钮未找到: {name}")
                    
        except Exception as e:
            print(f"设置校准信号失败: {e}")
            import traceback
            traceback.print_exc()

    def calibrate_device(self, device_name):
        """设备校准主方法"""
        try:
            print(f"开始校准设备: {device_name}")
            
            # 检查是否有正在进行的校准
            if self.current_calibration:
                QMessageBox.warning(
                    self.main_window, 
                    "校准进行中", 
                    f"当前正在校准 {self.current_calibration}，请等待完成后再进行其他校准。"
                )
                return
            
            # 发送校准开始信号
            self.calibration_started.emit(device_name)
            
            # 根据设备类型执行不同的校准流程
            if "传感器" in device_name:
                self.calibrate_sensor(device_name)
            elif "相机" in device_name:
                self.calibrate_camera(device_name)
            elif "机器人" in device_name:
                self.calibrate_robot(device_name)
            elif "夹爪" in device_name:
                self.calibrate_gripper(device_name)
            elif "自动校准" in device_name:
                self.auto_calibrate_all()
            else:
                self.generic_calibrate(device_name)
                
        except Exception as e:
            print(f"设备校准失败: {e}")
            QMessageBox.critical(self.main_window, "校准失败", f"设备校准过程中发生错误：\n{str(e)}")

    def calibrate_sensor(self, sensor_name):
        """传感器校准"""
        try:
            self.current_calibration = sensor_name
            
            # 创建进度对话框
            progress = QProgressDialog(f"正在校准 {sensor_name}...", "取消", 0, 100, self.main_window)
            progress.setWindowTitle("传感器校准")
            progress.setWindowModality(Qt.WindowModal)
            progress.show()
            
            # 模拟校准过程
            steps = [
                (10, "初始化传感器..."),
                (30, "检测传感器连接..."),
                (50, "读取传感器数据..."),
                (70, "校准传感器零点..."),
                (90, "验证校准结果..."),
                (100, "校准完成")
            ]
            
            for step_progress, step_desc in steps:
                if progress.wasCanceled():
                    self.current_calibration = None
                    return
                    
                progress.setValue(step_progress)
                progress.setLabelText(f"{step_desc}")
                self.calibration_progress.emit(sensor_name, step_progress)
                
                # 模拟处理时间
                time.sleep(0.5)
                QtWidgets.QApplication.processEvents()
            
            progress.close()
            
            # 记录校准结果
            result_msg = f"{sensor_name} 校准成功！\n\n校准参数：\n- 零点偏移: 0.02mm\n- 精度: ±0.01mm\n- 响应时间: 2ms"
            self.record_calibration_result(sensor_name, True, result_msg)
            
            # 显示结果
            QMessageBox.information(self.main_window, "校准完成", result_msg)
            
            # 发送完成信号
            self.calibration_completed.emit(sensor_name, True, result_msg)
            
        except Exception as e:
            error_msg = f"{sensor_name} 校准失败: {str(e)}"
            self.record_calibration_result(sensor_name, False, error_msg)
            QMessageBox.critical(self.main_window, "校准失败", error_msg)
            self.calibration_completed.emit(sensor_name, False, error_msg)
        finally:
            self.current_calibration = None

    def calibrate_camera(self, camera_name):
        """相机校准"""
        try:
            self.current_calibration = camera_name
            
            # 显示相机校准对话框
            dialog = CameraCalibrationDialog(self.main_window)
            if dialog.exec_() == QDialog.Accepted:
                calibration_params = dialog.get_calibration_params()
                
                # 执行相机校准
                result_msg = f"{camera_name} 校准成功！\n\n校准参数：\n"
                result_msg += f"- 焦距: {calibration_params.get('focal_length', 'N/A')}\n"
                result_msg += f"- 畸变系数: {calibration_params.get('distortion', 'N/A')}\n"
                result_msg += f"- 分辨率: {calibration_params.get('resolution', 'N/A')}"
                
                self.record_calibration_result(camera_name, True, result_msg)
                QMessageBox.information(self.main_window, "校准完成", result_msg)
                self.calibration_completed.emit(camera_name, True, result_msg)
            else:
                print("用户取消了相机校准")
                
        except Exception as e:
            error_msg = f"{camera_name} 校准失败: {str(e)}"
            self.record_calibration_result(camera_name, False, error_msg)
            QMessageBox.critical(self.main_window, "校准失败", error_msg)
            self.calibration_completed.emit(camera_name, False, error_msg)
        finally:
            self.current_calibration = None

    def calibrate_robot(self, robot_name):
        """机器人校准"""
        try:
            self.current_calibration = robot_name
            
            # 显示机器人校准对话框
            dialog = RobotCalibrationDialog(self.main_window)
            if dialog.exec_() == QDialog.Accepted:
                calibration_type = dialog.get_calibration_type()
                
                # 根据校准类型执行不同的校准流程
                if calibration_type == "zero_point":
                    result_msg = self.calibrate_robot_zero_point()
                elif calibration_type == "tool_center":
                    result_msg = self.calibrate_robot_tool_center()
                else:
                    result_msg = self.calibrate_robot_full()
                
                self.record_calibration_result(robot_name, True, result_msg)
                QMessageBox.information(self.main_window, "校准完成", result_msg)
                self.calibration_completed.emit(robot_name, True, result_msg)
            else:
                print("用户取消了机器人校准")
                
        except Exception as e:
            error_msg = f"{robot_name} 校准失败: {str(e)}"
            self.record_calibration_result(robot_name, False, error_msg)
            QMessageBox.critical(self.main_window, "校准失败", error_msg)
            self.calibration_completed.emit(robot_name, False, error_msg)
        finally:
            self.current_calibration = None

    def calibrate_gripper(self, gripper_name):
        """夹爪校准"""
        try:
            self.current_calibration = gripper_name
            
            # 简单的夹爪校准流程
            reply = QMessageBox.question(
                self.main_window,
                "夹爪校准",
                f"开始校准 {gripper_name}？\n\n校准过程将包括：\n1. 夹爪开合测试\n2. 力度校准\n3. 位置精度校准",
                QMessageBox.Yes | QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                # 模拟校准过程
                progress = QProgressDialog(f"正在校准 {gripper_name}...", "取消", 0, 100, self.main_window)
                progress.setWindowTitle("夹爪校准")
                progress.setWindowModality(Qt.WindowModal)
                progress.show()
                
                steps = [
                    (20, "测试夹爪开合..."),
                    (50, "校准夹持力度..."),
                    (80, "验证位置精度..."),
                    (100, "校准完成")
                ]
                
                for step_progress, step_desc in steps:
                    if progress.wasCanceled():
                        self.current_calibration = None
                        return
                        
                    progress.setValue(step_progress)
                    progress.setLabelText(step_desc)
                    time.sleep(0.8)
                    QtWidgets.QApplication.processEvents()
                
                progress.close()
                
                result_msg = f"{gripper_name} 校准成功！\n\n校准结果：\n- 最大夹持力: 50N\n- 位置精度: ±0.1mm\n- 开合速度: 100mm/s"
                self.record_calibration_result(gripper_name, True, result_msg)
                QMessageBox.information(self.main_window, "校准完成", result_msg)
                self.calibration_completed.emit(gripper_name, True, result_msg)
            else:
                print("用户取消了夹爪校准")
                
        except Exception as e:
            error_msg = f"{gripper_name} 校准失败: {str(e)}"
            self.record_calibration_result(gripper_name, False, error_msg)
            QMessageBox.critical(self.main_window, "校准失败", error_msg)
            self.calibration_completed.emit(gripper_name, False, error_msg)
        finally:
            self.current_calibration = None

    def auto_calibrate_all(self):
        """自动校准所有设备"""
        try:
            self.current_calibration = "自动校准"

            reply = QMessageBox.question(
                self.main_window,
                "自动校准",
                "开始自动校准所有设备？\n\n校准顺序：\n1. 传感器自检\n2. 相机标定\n3. 机器人标定\n4. 夹爪标定\n\n预计耗时：10-15分钟",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # 创建自动校准进度对话框
                progress = QProgressDialog("正在执行自动校准...", "停止", 0, 100, self.main_window)
                progress.setWindowTitle("自动校准")
                progress.setWindowModality(Qt.WindowModal)
                progress.show()

                # 自动校准设备列表
                devices = [
                    ("传感器1自检", 25),
                    ("传感器2自检", 25),
                    ("传感器3自检", 25),
                    ("相机标定", 25)
                ]

                total_progress = 0
                success_count = 0
                failed_devices = []

                for device_name, step_size in devices:
                    if progress.wasCanceled():
                        QMessageBox.information(self.main_window, "校准停止", "自动校准已被用户停止")
                        self.current_calibration = None
                        return

                    progress.setLabelText(f"正在校准: {device_name}")

                    # 模拟设备校准
                    try:
                        for i in range(step_size):
                            if progress.wasCanceled():
                                break
                            time.sleep(0.1)
                            total_progress += 1
                            progress.setValue(total_progress)
                            QtWidgets.QApplication.processEvents()

                        success_count += 1
                        print(f"✅ {device_name} 校准成功")

                    except Exception as e:
                        failed_devices.append(device_name)
                        print(f"❌ {device_name} 校准失败: {e}")

                progress.close()

                # 生成校准报告
                result_msg = f"自动校准完成！\n\n"
                result_msg += f"成功校准设备: {success_count}/{len(devices)}\n"
                if failed_devices:
                    result_msg += f"失败设备: {', '.join(failed_devices)}\n"
                result_msg += f"\n校准时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

                self.record_calibration_result("自动校准", len(failed_devices) == 0, result_msg)

                if failed_devices:
                    QMessageBox.warning(self.main_window, "校准完成（有失败）", result_msg)
                else:
                    QMessageBox.information(self.main_window, "校准完成", result_msg)

                self.calibration_completed.emit("自动校准", len(failed_devices) == 0, result_msg)
            else:
                print("用户取消了自动校准")

        except Exception as e:
            error_msg = f"自动校准失败: {str(e)}"
            self.record_calibration_result("自动校准", False, error_msg)
            QMessageBox.critical(self.main_window, "校准失败", error_msg)
            self.calibration_completed.emit("自动校准", False, error_msg)
        finally:
            self.current_calibration = None

    def generic_calibrate(self, device_name):
        """通用设备校准"""
        try:
            self.current_calibration = device_name

            QMessageBox.information(self.main_window, "设备校准", f"开始执行 {device_name}")

            # 简单的通用校准流程
            result_msg = f"{device_name} 校准完成！\n\n校准时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            self.record_calibration_result(device_name, True, result_msg)
            self.calibration_completed.emit(device_name, True, result_msg)

        except Exception as e:
            error_msg = f"{device_name} 校准失败: {str(e)}"
            self.record_calibration_result(device_name, False, error_msg)
            QMessageBox.critical(self.main_window, "校准失败", error_msg)
            self.calibration_completed.emit(device_name, False, error_msg)
        finally:
            self.current_calibration = None

    def calibrate_robot_zero_point(self):
        """机器人零点校准"""
        # 模拟零点校准过程
        time.sleep(2)
        return "机器人零点校准成功！\n\n校准结果：\n- X轴零点: 0.00mm\n- Y轴零点: 0.00mm\n- Z轴零点: 0.00mm"

    def calibrate_robot_tool_center(self):
        """机器人工具中心点校准"""
        # 模拟工具中心点校准过程
        time.sleep(3)
        return "机器人工具中心点校准成功！\n\n校准结果：\n- TCP偏移X: 0.5mm\n- TCP偏移Y: -0.2mm\n- TCP偏移Z: 10.0mm"

    def calibrate_robot_full(self):
        """机器人完整校准"""
        # 模拟完整校准过程
        time.sleep(5)
        return "机器人完整校准成功！\n\n校准结果：\n- 零点校准: 完成\n- 工具中心点: 完成\n- 坐标系校准: 完成\n- 精度验证: 通过"

    def record_calibration_result(self, device_name, success, result_msg):
        """记录校准结果"""
        try:
            calibration_record = {
                "device_name": device_name,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "success": success,
                "result": result_msg,
                "operator": "当前用户"  # 可以从主窗口获取当前用户信息
            }

            self.calibration_history.append(calibration_record)

            # 保存到文件
            self.save_calibration_data()

            print(f"📝 校准记录已保存: {device_name} - {'成功' if success else '失败'}")

        except Exception as e:
            print(f"记录校准结果失败: {e}")

    def save_calibration_data(self):
        """保存校准数据到文件"""
        try:
            data = {
                "calibration_history": self.calibration_history,
                "last_updated": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

            with open(self.calibration_data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

            print("✅ 校准数据已保存")

        except Exception as e:
            print(f"保存校准数据失败: {e}")

    def load_calibration_data(self):
        """从文件加载校准数据"""
        try:
            if not os.path.exists(self.calibration_data_file):
                print("校准数据文件不存在，使用默认数据")
                return

            with open(self.calibration_data_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            self.calibration_history = data.get('calibration_history', [])
            last_updated = data.get('last_updated', '未知')

            print(f"已加载 {len(self.calibration_history)} 条校准记录，最后更新时间: {last_updated}")

        except Exception as e:
            print(f"加载校准数据失败: {e}")
            self.calibration_history = []

    def get_calibration_history(self):
        """获取校准历史记录"""
        return self.calibration_history.copy()

    def get_last_calibration(self, device_name):
        """获取指定设备的最后一次校准记录"""
        for record in reversed(self.calibration_history):
            if record["device_name"] == device_name:
                return record
        return None

    def is_device_calibrated(self, device_name, hours_threshold=24):
        """检查设备是否在指定时间内校准过"""
        last_calibration = self.get_last_calibration(device_name)
        if not last_calibration or not last_calibration["success"]:
            return False

        try:
            calibration_time = datetime.strptime(last_calibration["timestamp"], "%Y-%m-%d %H:%M:%S")
            time_diff = datetime.now() - calibration_time
            return time_diff.total_seconds() < hours_threshold * 3600
        except:
            return False


class CameraCalibrationDialog(QDialog):
    """相机校准对话框"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("相机校准")
        self.setFixedSize(400, 300)
        self.setup_ui()

    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)

        # 标题
        title = QLabel("相机校准设置")
        title.setStyleSheet("font-size: 16px; font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(title)

        # 校准参数组
        params_group = QGroupBox("校准参数")
        params_layout = QGridLayout(params_group)

        # 焦距
        params_layout.addWidget(QLabel("焦距 (mm):"), 0, 0)
        self.focal_length_edit = QtWidgets.QLineEdit("50.0")
        params_layout.addWidget(self.focal_length_edit, 0, 1)

        # 分辨率
        params_layout.addWidget(QLabel("分辨率:"), 1, 0)
        self.resolution_edit = QtWidgets.QLineEdit("1920x1080")
        params_layout.addWidget(self.resolution_edit, 1, 1)

        # 畸变系数
        params_layout.addWidget(QLabel("畸变系数:"), 2, 0)
        self.distortion_edit = QtWidgets.QLineEdit("0.01")
        params_layout.addWidget(self.distortion_edit, 2, 1)

        layout.addWidget(params_group)

        # 按钮
        button_layout = QHBoxLayout()
        self.ok_btn = QPushButton("开始校准")
        self.cancel_btn = QPushButton("取消")

        self.ok_btn.clicked.connect(self.accept)
        self.cancel_btn.clicked.connect(self.reject)

        button_layout.addWidget(self.ok_btn)
        button_layout.addWidget(self.cancel_btn)
        layout.addLayout(button_layout)

    def get_calibration_params(self):
        """获取校准参数"""
        return {
            "focal_length": self.focal_length_edit.text(),
            "resolution": self.resolution_edit.text(),
            "distortion": self.distortion_edit.text()
        }


class RobotCalibrationDialog(QDialog):
    """机器人校准对话框"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("机器人校准")
        self.setFixedSize(350, 250)
        self.setup_ui()

    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)

        # 标题
        title = QLabel("选择校准类型")
        title.setStyleSheet("font-size: 16px; font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(title)

        # 校准类型组
        type_group = QGroupBox("校准类型")
        type_layout = QVBoxLayout(type_group)

        self.zero_point_radio = QtWidgets.QRadioButton("零点校准")
        self.tool_center_radio = QtWidgets.QRadioButton("工具中心点校准")
        self.full_calibration_radio = QtWidgets.QRadioButton("完整校准")

        self.zero_point_radio.setChecked(True)  # 默认选择零点校准

        type_layout.addWidget(self.zero_point_radio)
        type_layout.addWidget(self.tool_center_radio)
        type_layout.addWidget(self.full_calibration_radio)

        layout.addWidget(type_group)

        # 按钮
        button_layout = QHBoxLayout()
        self.ok_btn = QPushButton("开始校准")
        self.cancel_btn = QPushButton("取消")

        self.ok_btn.clicked.connect(self.accept)
        self.cancel_btn.clicked.connect(self.reject)

        button_layout.addWidget(self.ok_btn)
        button_layout.addWidget(self.cancel_btn)
        layout.addLayout(button_layout)

    def get_calibration_type(self):
        """获取校准类型"""
        if self.zero_point_radio.isChecked():
            return "zero_point"
        elif self.tool_center_radio.isChecked():
            return "tool_center"
        else:
            return "full"

    def cleanup(self):
        """清理模块资源"""
        try:
            print("🧹 清理校准模块资源...")

            # 停止可能的校准进程
            if hasattr(self, 'current_calibration') and self.current_calibration:
                self.current_calibration = None
                print("✅ 已停止当前校准进程")

            # 保存校准数据
            self.save_calibration_data()

            print("✅ 校准模块资源清理完成")

        except Exception as e:
            print(f"❌ 清理校准模块资源失败: {e}")
            import traceback
            traceback.print_exc()
