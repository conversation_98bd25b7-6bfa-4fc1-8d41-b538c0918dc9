#!/usr/bin/python
# -*- coding: utf-8 -*-

import typing as t
from PyQt5.QtCore import Qt, pyqtSlot
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                             QLabel, QPushButton, QSpinBox, QDoubleSpinBox,
                             QGroupBox, QDialogButtonBox, QDesktopWidget, QMessageBox)
from blade_inspection.core.flags import Codes
from hdmtv.core.node import Request, Response
from blade_inspection.core.inspect_service import InspectService
from hdmtv.core.cdi import get_instance


class RobotSelectDialog(QDialog):
    """机器人点位选择对话框 - 适配Blade Insight项目"""

    def __init__(self, parent=None, program_module=None):
        super(RobotSelectDialog, self).__init__(parent=parent)
        self.program_module = program_module  # 保存程序模块引用

        # 初始化参数
        self._request = "modify"  # "insert" 或 "modify"
        self._is_insert_front = False
        self._is_absolute = True
        self._is_joint = True
        self._speed = 40.0
        self._accuracy = -1
        self._base_joint_pos = [0.0, 0.0, 0.0, 0.0, 0.0, 0.0]
        self._base_tcp_pos = [0.0, 0.0, 0.0, 0.0, 0.0, 0.0]
        self.inspect_service = get_instance(clazz=InspectService)

        # 设置样式
        self.setStyleSheet("background-color: rgb(50,51,61);")

        # 初始化UI
        self._init_ui()

    def _init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 顶部蓝色条
        top_label = QLabel()
        top_label.setFixedHeight(12)
        top_label.setStyleSheet("background-color: rgb(46,115,228);")
        layout.addWidget(top_label)
        
        # 标题区域
        title_layout = QHBoxLayout()
        title_layout.setContentsMargins(20, 15, 20, 15)
        
        title_label = QLabel("机器人点位调整")
        title_label.setStyleSheet("color: rgb(255, 255, 255); font: bold; font-size: 36px;")
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        
        layout.addLayout(title_layout)
        
        # 主要内容区域
        main_layout = QHBoxLayout()
        main_layout.setContentsMargins(20, 0, 20, 20)
        main_layout.setSpacing(20)
        
        # 运动模式组
        self._create_move_mode_group(main_layout)
        
        # 移动方式组
        self._create_move_type_group(main_layout)
        
        # 插入位置组
        self._create_insert_position_group(main_layout)
        
        # 参数设置组
        self._create_parameter_group(main_layout)
        
        layout.addLayout(main_layout)
        
        # 底部按钮区域
        self._create_button_area(layout)

    def _create_move_mode_group(self, parent_layout):
        """创建运动模式组"""
        group = QGroupBox("运动模式")
        group.setStyleSheet("color: rgb(255, 255, 255); font: bold; font-size: 32px;")
        
        layout = QVBoxLayout(group)
        layout.setSpacing(10)
        layout.setContentsMargins(20, 15, 20, 15)
        
        self.absolute_button = QPushButton("绝对模式")
        self.relative_button = QPushButton("相对模式")
        
        for btn in [self.absolute_button, self.relative_button]:
            btn.setStyleSheet(self._get_button_style())
            layout.addWidget(btn)
        
        # 连接信号
        self.absolute_button.clicked.connect(self.set_absolute_mode)
        self.relative_button.clicked.connect(self.set_relative_mode)
        
        parent_layout.addWidget(group)

    def _create_move_type_group(self, parent_layout):
        """创建移动方式组"""
        group = QGroupBox("移动方式")
        group.setStyleSheet("color: rgb(255, 255, 255); font: bold; font-size: 32px;")
        
        layout = QVBoxLayout(group)
        layout.setSpacing(10)
        layout.setContentsMargins(20, 15, 20, 15)
        
        self.joint_button = QPushButton("关节运动")
        self.linear_button = QPushButton("直线运动")
        
        for btn in [self.joint_button, self.linear_button]:
            btn.setStyleSheet(self._get_button_style())
            layout.addWidget(btn)
        
        # 连接信号
        self.joint_button.clicked.connect(self.set_joint_move)
        self.linear_button.clicked.connect(self.set_linear_move)
        
        parent_layout.addWidget(group)

    def _create_insert_position_group(self, parent_layout):
        """创建插入位置组"""
        group = QGroupBox("插入位置")
        group.setStyleSheet("color: rgb(255, 255, 255); font: bold; font-size: 32px;")
        
        layout = QVBoxLayout(group)
        layout.setSpacing(10)
        layout.setContentsMargins(20, 15, 20, 15)
        
        self.insert_front_button = QPushButton("当前步骤之前")
        self.insert_back_button = QPushButton("当前步骤之后")
        
        for btn in [self.insert_front_button, self.insert_back_button]:
            btn.setStyleSheet(self._get_button_style())
            layout.addWidget(btn)
        
        # 连接信号
        self.insert_front_button.clicked.connect(self.set_insert_front)
        self.insert_back_button.clicked.connect(self.set_insert_back)
        
        parent_layout.addWidget(group)

    def _create_parameter_group(self, parent_layout):
        """创建参数设置组"""
        group = QGroupBox("参数设置")
        group.setStyleSheet("color: rgb(255, 255, 255); font: bold; font-size: 32px;")
        
        layout = QGridLayout(group)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 15, 20, 15)
        
        # 速度设置
        speed_label = QLabel("速度:")
        speed_label.setStyleSheet("color: rgb(255, 255, 255); font: bold; font-size: 32px;")
        layout.addWidget(speed_label, 0, 0)
        
        self.speed_text = QDoubleSpinBox()
        self.speed_text.setRange(0.1, 100.0)
        self.speed_text.setValue(40.0)
        self.speed_text.setSuffix(" %")
        self.speed_text.setStyleSheet(self._get_spinbox_style())
        layout.addWidget(self.speed_text, 0, 1)
        
        # 精度设置
        accuracy_label = QLabel("精度:")
        accuracy_label.setStyleSheet("color: rgb(255, 255, 255); font: bold; font-size: 32px;")
        layout.addWidget(accuracy_label, 1, 0)
        
        self.accuracy_text = QSpinBox()
        self.accuracy_text.setRange(-1, 10)
        self.accuracy_text.setValue(-1)
        self.accuracy_text.setSpecialValueText("默认")
        self.accuracy_text.setStyleSheet(self._get_spinbox_style())
        layout.addWidget(self.accuracy_text, 1, 1)
        
        parent_layout.addWidget(group)

    def _create_button_area(self, parent_layout):
        """创建底部按钮区域"""
        button_layout = QHBoxLayout()
        button_layout.setContentsMargins(20, 10, 20, 20)
        button_layout.setSpacing(20)
        
        # 确定按钮
        ok_button = QPushButton("确定")
        ok_button.setFixedSize(120, 50)
        ok_button.setStyleSheet("""
            QPushButton {
                background-color: rgb(46,115,228);
                color: white;
                border: none;
                border-radius: 8px;
                font: bold;
                font-size: 24px;
            }
            QPushButton:hover {
                background-color: rgb(36,105,218);
            }
            QPushButton:pressed {
                background-color: rgb(26,95,208);
            }
        """)
        ok_button.clicked.connect(self.accepted_request)
        
        # 取消按钮
        cancel_button = QPushButton("取消")
        cancel_button.setFixedSize(120, 50)
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: rgb(95,95,95);
                color: white;
                border: none;
                border-radius: 8px;
                font: bold;
                font-size: 24px;
            }
            QPushButton:hover {
                background-color: rgb(85,85,85);
            }
            QPushButton:pressed {
                background-color: rgb(75,75,75);
            }
        """)
        cancel_button.clicked.connect(self.rejected_request)
        
        button_layout.addStretch()
        button_layout.addWidget(ok_button)
        button_layout.addWidget(cancel_button)
        
        parent_layout.addLayout(button_layout)

    def _move_to_center(self):
        """将对话框移动到屏幕中央"""
        try:
            # 如果有父窗口，相对于父窗口居中
            if self.parent():
                parent_geometry = self.parent().geometry()
                dialog_size = self.size()

                # 计算相对于父窗口的居中位置
                x = parent_geometry.x() + (parent_geometry.width() - dialog_size.width()) // 2
                y = parent_geometry.y() + (parent_geometry.height() - dialog_size.height()) // 2

                self.move(x, y)
                print(f"对话框已相对于父窗口居中: ({x}, {y})")
            else:
                # 如果没有父窗口，相对于屏幕居中
                desktop = QDesktopWidget()
                screen = desktop.screenGeometry()
                dialog_size = self.size()

                x = (screen.width() - dialog_size.width()) // 2
                y = (screen.height() - dialog_size.height()) // 2

                self.move(x, y)
                print(f"对话框已相对于屏幕居中: ({x}, {y})")

        except Exception as e:
            print(f"居中对话框时出错: {e}")
            # 如果出错，使用默认位置
            self.move(100, 100)

    def _get_button_style(self):
        """获取按钮样式"""
        return """
            QPushButton {
                color: rgb(255, 255, 255); 
                font: bold; 
                font-size: 32px;
                border: 2px solid #000000;
                background-color: rgb(50,51,61);
                padding: 10px;
                min-height: 40px;
            }
            QPushButton:hover {
                background-color: rgb(60,61,71);
            }
            QPushButton:pressed {
                background-color: rgb(255,140,0);
            }
        """

    def _get_spinbox_style(self):
        """获取数值输入框样式"""
        return """
            QSpinBox, QDoubleSpinBox {
                color: rgb(255, 255, 255);
                font: bold;
                font-size: 32px;
                border: 2px solid #000000;
                background-color: rgb(50,51,61);
                padding: 5px;
            }
        """

    def show_dialog(self, **options):
        """显示对话框"""
        if "parent" in options:
            self.setParent(options["parent"], Qt.Dialog)

        self._request = options.get("request", "modify")
        self._speed = options.get("speed", 40.0)
        self._accuracy = options.get("accuracy", -1)
        self._is_absolute = options.get("is_absolute", True)
        self._is_joint = options.get("is_joint", True)
        self._is_insert_front = options.get("is_insert_front", False)

        self._render_dialog()

        # 确保对话框大小已经确定后再居中
        self.adjustSize()  # 调整对话框大小以适应内容
        self._move_to_center()

        self.show()

    def _render_dialog(self):
        """渲染对话框界面"""
        # 按钮样式定义
        normal_style = self._get_button_style()
        pressed_style = normal_style.replace("rgb(50,51,61)", "rgb(255,140,0)")

        # 运动模式按钮
        if self._is_absolute:
            self.absolute_button.setStyleSheet(pressed_style)
            self.relative_button.setStyleSheet(normal_style)
        else:
            self.absolute_button.setStyleSheet(normal_style)
            self.relative_button.setStyleSheet(pressed_style)

        # 移动方式按钮
        if self._is_joint:
            self.joint_button.setStyleSheet(pressed_style)
            self.linear_button.setStyleSheet(normal_style)
        else:
            self.joint_button.setStyleSheet(normal_style)
            self.linear_button.setStyleSheet(pressed_style)

        # 插入位置按钮 - 修复的逻辑
        if self._request == "insert":
            self.insert_front_button.setEnabled(True)
            self.insert_back_button.setEnabled(True)
            if self._is_insert_front:
                self.insert_front_button.setStyleSheet(pressed_style)
                self.insert_back_button.setStyleSheet(normal_style)
            else:
                self.insert_front_button.setStyleSheet(normal_style)
                self.insert_back_button.setStyleSheet(pressed_style)
        else:
            self.insert_front_button.setEnabled(False)
            self.insert_back_button.setEnabled(False)
            disabled_style = normal_style.replace("rgb(50,51,61)", "rgb(30,31,41)")
            self.insert_front_button.setStyleSheet(disabled_style)
            self.insert_back_button.setStyleSheet(disabled_style)

        # 设置数值
        self.speed_text.setValue(self._speed)
        self.accuracy_text.setValue(self._accuracy)

    @pyqtSlot()
    def accepted_request(self):
        """确认按钮点击处理"""
        try:
            # 获取参数
            data = {}
            data["move_mode"] = 0 if self._is_absolute else 1
            data["kind"] = "joint" if self._is_joint else "linear"
            data["speed"] = float(self.speed_text.value())

            accuracy = int(self.accuracy_text.value())
            if accuracy >= 0:
                data["accuracy"] = accuracy

            if self._request == "insert":
                data["insert"] = "front" if self._is_insert_front else "back"

            self.close()

            # 调用相应的接口
            if self._request == "insert":
                speed: float = data.get("speed", 10.0)
                kind: str = data.get("kind", "joint")
                move_mode: int = data.get("move_mode", 0)
                insert: str = data.get("insert", "front")
                accuracy: t.Optional[t.Union[int, float]] = data.get("accuracy")
                code, value = self.mark_robot_position(
                    speed=speed, kind=kind, move_mode=move_mode, insert=insert, accuracy=accuracy)
                result = {"code": code, "message": "点位插入成功"}
                self._show_result("点位插入", result)
            else:
                speed: float = data.get("speed", 10.0)
                kind: str = data.get("kind", "joint")
                move_mode: int = data.get("move_mode", 0)
                accuracy: t.Optional[t.Union[int, float]] = data.get("accuracy")
                code, value = self.modify_robot_position(
                    speed=speed, kind=kind, move_mode=move_mode, accuracy=accuracy)
                result = {"code": code, "message": "点位修改成功"}
                self._show_result("点位修改", result)

        except Exception as e:
            QMessageBox.warning(self, "错误", f"操作失败: {str(e)}")

    @pyqtSlot()
    def rejected_request(self):
        """取消按钮点击处理"""
        self.close()

    def mark_robot_position(
        self,
        speed: float,
        kind: str,
        move_mode: int,
        insert: str,
        accuracy: t.Optional[t.Union[int, float]]
    ) -> t.Tuple[int, t.Dict[str, t.Any]]:
        """ Mark the current position and send into the Robotics as the add result. """
        return self._operate_robot_position(
            speed=speed, kind=kind, move_mode=move_mode, insert=insert, is_block=False, is_modify=False, accuracy=accuracy)

    def modify_robot_position(
        self,
        speed: float,
        kind: str,
        move_mode: int,
        accuracy: t.Optional[t.Union[int, float]]
    ) -> t.Tuple[int, t.Dict[str, t.Any]]:
        """ Modify the current position and send into the Robotics as the modification result. """
        return self._operate_robot_position(
            speed=speed, kind=kind, move_mode=move_mode, is_block=False, is_modify=True, accuracy=accuracy)

    def _show_result(self, title, result):
        """显示操作结果"""
        try:
            code = result.get("code", -1)
            message = result.get("message", "未知错误")

            if code == 0:
                QMessageBox.information(self, title, f"操作成功！\n\n{message}")
            else:
                QMessageBox.warning(self, title, f"操作失败！\n\n{message}")

        except Exception as e:
            QMessageBox.warning(self, title, f"显示结果失败: {str(e)}")

    def _operate_robot_position(
            self,
            speed: float,
            kind: str = "joint",
            move_mode: int = 0,
            insert: str = "front",
            is_block: bool = True,
            is_modify: bool = True,
            accuracy: t.Optional[t.Union[int, float]] = None
    ) -> t.Tuple[int, t.Dict[str, t.Any]]:
        """ Mark the current robot's joint positions while it has been suspended. """
        # if self._cur_process is None:
        #     return Codes.PROCESS_NOT_STARTED, dict()
        # elif self._cur_process == "execute":
        #     return Codes.UNSUPPORTED_STATE, dict()
        # elif not self._check_robot_tcp_tool():
        #     return Codes.ROBOT_WRONG_TCP_TOOL, dict()

        # 获取TCP位置
        tcp_response: Response = self.inspect_service.robot_tcp_position_client.call(request=Request())
        if tcp_response.code != Codes.SUCCESS:
            return Codes.ROBOT_OP_ERROR, dict()
        joint_response: Response = self.inspect_service.robot_joint_position_client.call(request=Request())
        if joint_response.code != Codes.SUCCESS:
            return Codes.ROBOT_OP_ERROR, dict()
        tcp_position, joint_position = tcp_response.data.get("tcp_position", []), joint_response.data.get("joint_position", [])

        if joint_position is None or tcp_position is None:
            return Codes.ROBOT_OP_ERROR, dict()
        elif kind == "joint":
            if move_mode == 1:
                joint_position = [i - j for i, j in zip(joint_position, self._base_joint_pos)]
            command_matrix: t.Dict[str, t.Any] = {
                "prefix": "/localhost/ns/robot_1",
                "action": "RobotMoveAction",
                "commands_list": [
                    {
                        "command": "joint_move",
                        "kwargs": {
                            "joint_pos": joint_position,
                            "is_block": is_block,
                            "move_mode": move_mode,
                            "speed": speed
                        }
                    }
                ]
            }
        elif kind == "linear":
            if move_mode == 1:
                tcp_position = [i - j for i, j in zip(tcp_position, self._base_tcp_pos)]
                if abs(tcp_position[3]) > 1e-3:
                    return Codes.ROBOT_OP_ERROR, dict()
                if abs(tcp_position[4]) > 1e-3:
                    return Codes.ROBOT_OP_ERROR, dict()
                if abs(tcp_position[5]) > 1e-3:
                    return Codes.ROBOT_OP_ERROR, dict()
            command_matrix: t.Dict[str, t.Any] = {
                "prefix": "/localhost/ns/robot_1",
                "action": "RobotMoveAction",
                "commands_list": [
                    {
                        "command": "linear_move",
                        "kwargs": {
                            "end_pos": tcp_position,
                            "is_block": is_block,
                            "move_mode": move_mode,
                            "speed": speed
                        }
                    }
                ]
            }
        else:
            return Codes.ROBOT_OP_ERROR, dict()

        if accuracy is not None:
            command_matrix["commands_list"][0]["kwargs"]["accuracy"] = accuracy

        # 通过程序模块调用modify或add函数
        if is_modify:
            if self.program_module:
                code, value = self.program_module.modify(step_process=command_matrix)
                # modify函数内部已经调用了refresh_program_steps，无需重复调用
            else:
                print("❌ 程序模块不可用")
                code, value = Codes.ROBOT_OP_ERROR, {"error": "程序模块不可用"}
        else:
            if self.program_module:
                code, value = self.program_module.add(step_process=command_matrix, insert=insert)
                # add函数内部已经调用了refresh_program_steps，无需重复调用
            else:
                print("❌ 程序模块不可用")
                code, value = Codes.ROBOT_OP_ERROR, {"error": "程序模块不可用"}

        if code != 0:  # 修改为0表示成功
            code = Codes.ROBOT_OP_ERROR
        return code, value

    @pyqtSlot()
    def set_absolute_mode(self):
        """设置绝对模式"""
        self._is_absolute = True
        self._render_dialog()

    @pyqtSlot()
    def set_relative_mode(self):
        """设置相对模式"""
        self._is_absolute = False
        self._render_dialog()

    @pyqtSlot()
    def set_joint_move(self):
        """设置关节运动"""
        self._is_joint = True
        self._render_dialog()

    @pyqtSlot()
    def set_linear_move(self):
        """设置直线运动"""
        self._is_joint = False
        self._render_dialog()

    @pyqtSlot()
    def set_insert_back(self):
        """设置在当前步骤之后插入"""
        self._is_insert_front = False
        self._render_dialog()

    @pyqtSlot()
    def set_insert_front(self):
        """设置在当前步骤之前插入"""
        self._is_insert_front = True
        self._render_dialog()
