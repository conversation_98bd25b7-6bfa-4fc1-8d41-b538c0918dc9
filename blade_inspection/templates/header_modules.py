#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import os
import json
import time
from datetime import datetime
from PyQt5.QtWidgets import (QMessageBox, QDialog, QVBoxLayout, QHBoxLayout, 
                             QLabel, QGroupBox, QGridLayout, QPushButton,
                             QLineEdit, QComboBox, QCheckBox, QTextEdit,
                             QListWidget, QListWidgetItem, QProgressBar,
                             QTabWidget, QWidget, QFrame, QSplitter)
from PyQt5.QtCore import Qt, pyqtSignal, QObject, QTimer
from PyQt5.QtGui import QPixmap, QIcon
from PyQt5 import QtWidgets


class HeaderModulesManager(QObject):
    """界面顶部模块管理器"""
    
    # 定义信号
    operator_changed = pyqtSignal(str)  # 操作员变更信号
    connection_status_changed = pyqtSignal(str, bool)  # 连接状态变更信号
    alarm_triggered = pyqtSignal(str, str)  # 报警触发信号
    hardware_config_changed = pyqtSignal(dict)  # 硬件配置变更信号
    
    def __init__(self, main_window):
        """初始化顶部模块管理器"""
        super().__init__()
        self.main_window = main_window
        
        # 数据文件
        self.config_data_file = "header_config_data.json"
        
        # 当前状态
        self.current_operator = "未登录"
        self.connection_status = {}  # 设备连接状态
        self.alarm_list = []  # 报警列表
        self.hardware_config = {}  # 硬件配置
        
        # 设备列表
        self.devices = {
            "robot": "机器人",
            "camera_2d": "2D相机",
            "camera_3d": "3D相机", 
            "sensor1": "传感器1",
            "sensor2": "传感器2",
            "sensor3": "传感器3",
            "gripper": "夹爪",
            "conveyor": "传送带"
        }
        
        # 初始化数据
        self.load_config_data()
        self.init_connection_status()

    def setup_header_signals(self):
        """设置顶部按钮信号连接"""
        try:
            # 硬件配置按钮
            if hasattr(self.main_window, 'hardware_config_btn') and self.main_window.hardware_config_btn:
                self.main_window.hardware_config_btn.clicked.connect(self.show_hardware_config)
                
            # 操作人员按钮
            if hasattr(self.main_window, 'operator_btn') and self.main_window.operator_btn:
                self.main_window.operator_btn.clicked.connect(self.show_operator_login)
                
            # 连接状态按钮
            if hasattr(self.main_window, 'connect_status') and self.main_window.connect_status:
                self.main_window.connect_status.clicked.connect(self.show_connection_monitor)
                
            # 报警信息按钮
            if hasattr(self.main_window, 'alarm_btn') and self.main_window.alarm_btn:
                self.main_window.alarm_btn.clicked.connect(self.show_alarm_info)
                
            # 实时监控按钮
            if hasattr(self.main_window, 'monitor_btn') and self.main_window.monitor_btn:
                self.main_window.monitor_btn.clicked.connect(self.show_real_time_monitor)
                
            print("✅ 顶部模块信号连接完成")
            
        except Exception as e:
            print(f"顶部模块信号连接失败: {e}")

    def setup_header_buttons_style(self):
        """设置顶部按钮样式，图标在上文字在下"""
        try:
            # 设置各个按钮
            if hasattr(self.main_window, 'hardware_config_btn') and self.main_window.hardware_config_btn:
                self.create_icon_text_button(
                    self.main_window.hardware_config_btn,
                    "blade/static/ui/settings.png",
                    "硬件配置"
                )

            if hasattr(self.main_window, 'operator_btn') and self.main_window.operator_btn:
                self.create_icon_text_button(
                    self.main_window.operator_btn,
                    "blade/static/ui/user.png",
                    "操作人员"
                )

            if hasattr(self.main_window, 'connect_status') and self.main_window.connect_status:
                self.create_icon_text_button(
                    self.main_window.connect_status,
                    "blade/static/ui/connection.png",
                    "连接状态"
                )

            if hasattr(self.main_window, 'alarm_btn') and self.main_window.alarm_btn:
                self.create_icon_text_button(
                    self.main_window.alarm_btn,
                    "blade/static/ui/warning.png",
                    "报警信息"
                )

            if hasattr(self.main_window, 'monitor_btn') and self.main_window.monitor_btn:
                self.create_icon_text_button(
                    self.main_window.monitor_btn,
                    "blade/static/ui/camera.png",
                    "实时监控"
                )

            print("✅ 顶部按钮样式设置完成")

        except Exception as e:
            print(f"设置头部按钮样式失败: {e}")

    def create_icon_text_button(self, button, icon_path, text):
        """创建图标在上文字在下的按钮（完全按照test_blade_ui.py实现）"""
        if not button:
            return

        try:
            import traceback
            # 清除原有文字和图标
            button.setText("")
            from PyQt5.QtGui import QIcon
            button.setIcon(QIcon())

            # 设置按钮固定大小
            button.setFixedSize(60, 60)

            # 创建垂直布局的widget
            widget = QtWidgets.QWidget()
            layout = QtWidgets.QVBoxLayout(widget)
            layout.setContentsMargins(5, 5, 5, 5)
            layout.setSpacing(3)

            # 创建图标标签
            icon_label = QtWidgets.QLabel()
            icon_label.setFixedSize(24, 24)
            icon_label.setAlignment(Qt.AlignCenter)

            # 设置图标
            if os.path.exists(icon_path):
                pixmap = QPixmap(icon_path)
                scaled_pixmap = pixmap.scaled(24, 24, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                icon_label.setPixmap(scaled_pixmap)
            else:
                icon_label.setText("⚙")  # 默认图标
                icon_label.setStyleSheet("font-size: 18px; color: white;")

            # 创建文字标签
            text_label = QtWidgets.QLabel(text)
            text_label.setAlignment(Qt.AlignCenter)
            text_label.setFixedHeight(20)  # 固定文字标签高度
            text_label.setStyleSheet("""
                QLabel {
                    color: white;
                    font-size: 10px;
                    font-weight: bold;
                }
            """)
            text_label.setWordWrap(True)

            # 添加到布局，确保垂直居中对齐
            layout.addStretch()  # 上方弹性空间
            layout.addWidget(icon_label, 0, Qt.AlignCenter)
            layout.addWidget(text_label, 0, Qt.AlignCenter)
            layout.addStretch()  # 下方弹性空间

            # 设置按钮样式
            button.setStyleSheet("""
                QPushButton {
                    background-color: #34495e;
                    border: 1px solid #4a6741;
                    border-radius: 6px;
                    padding: 0px;
                }
                QPushButton:hover {
                    background-color: #4a6741;
                }
                QPushButton:pressed {
                    background-color: #2c3e50;
                }
            """)

            # 清空按钮原有布局
            if button.layout():
                QtWidgets.QWidget().setLayout(button.layout())

            # 设置新布局
            button.setLayout(layout)

        except Exception as e:
            print(f"设置按钮样式失败: {e}")
            import traceback
            traceback.print_exc()
            # 设置基本样式作为后备
            button.setText(text)
            button.setStyleSheet("""
                QPushButton {
                    background-color: #34495e;
                    color: white;
                    border: 1px solid #4a6741;
                    border-radius: 4px;
                    padding: 5px;
                    font-size: 10px;
                }
            """)

    def load_config_data(self):
        """加载配置数据"""
        try:
            if not os.path.exists(self.config_data_file):
                print("配置数据文件不存在，使用默认配置")
                self.create_default_config()
                return

            with open(self.config_data_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            self.current_operator = data.get('current_operator', '未登录')
            self.connection_status = data.get('connection_status', {})
            self.alarm_list = data.get('alarm_list', [])
            self.hardware_config = data.get('hardware_config', {})

            print(f"已加载配置数据，当前操作员: {self.current_operator}")

        except Exception as e:
            print(f"加载配置数据失败: {e}")
            self.create_default_config()

    def create_default_config(self):
        """创建默认配置"""
        self.current_operator = "未登录"
        self.connection_status = {}
        self.alarm_list = []
        self.hardware_config = {
            "robot_ip": "*************",
            "camera_2d_ip": "*************",
            "camera_3d_ip": "*************",
            "sensor_port": "COM3",
            "gripper_enabled": True,
            "conveyor_speed": 50
        }
        self.save_config_data()

    def save_config_data(self):
        """保存配置数据"""
        try:
            data = {
                'current_operator': self.current_operator,
                'connection_status': self.connection_status,
                'alarm_list': self.alarm_list,
                'hardware_config': self.hardware_config,
                'last_updated': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

            with open(self.config_data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

            print("✅ 配置数据已保存")

        except Exception as e:
            print(f"保存配置数据失败: {e}")

    def init_connection_status(self):
        """初始化连接状态"""
        for device_id in self.devices.keys():
            if device_id not in self.connection_status:
                self.connection_status[device_id] = False

    def show_hardware_config(self):
        """显示硬件配置对话框（使用原有的blade_dialogs实现，但去掉屏蔽设置）"""
        try:
            # 使用修改后的硬件配置对话框
            dialog = OriginalStyleHardwareConfigDialog(self.main_window, self)
            if dialog.exec_() == QDialog.Accepted:
                # 获取配置并保存
                config = dialog.get_config()
                self.hardware_config.update(config)
                self.save_config_data()
                self.hardware_config_changed.emit(config)
                print("硬件配置已更新")

        except Exception as e:
            print(f"显示硬件配置对话框失败: {e}")
            QMessageBox.warning(self.main_window, "错误", f"无法打开硬件配置对话框: {e}")

    def show_operator_login(self):
        """显示操作人员登录对话框"""
        try:
            from templates.blade_dialogs import OperatorLoginDialog
            dialog = OperatorLoginDialog(self.main_window)
            dialog.login_success.connect(self.on_login_success)
            dialog.exec_()

        except Exception as e:
            print(f"显示操作人员登录对话框失败: {e}")
            QMessageBox.warning(self.main_window, "错误", f"无法打开登录对话框: {e}")

    def on_login_success(self, role, username):
        """登录成功处理（与blade_dialogs.py保持一致）"""
        self.current_operator = f"{role}-{username}"
        self.save_config_data()
        self.operator_changed.emit(self.current_operator)
        print(f"操作员登录成功: {role}-{username}")

    def show_connection_monitor(self):
        """显示连接状态监控对话框"""
        try:
            dialog = ConnectionMonitorDialog(self.main_window, self)
            dialog.exec_()
            
        except Exception as e:
            print(f"显示连接状态监控失败: {e}")
            QMessageBox.warning(self.main_window, "错误", f"无法打开连接状态监控: {e}")

    def show_alarm_info(self):
        """显示报警信息对话框"""
        try:
            dialog = AlarmInfoDialog(self.main_window, self)
            dialog.exec_()
            
        except Exception as e:
            print(f"显示报警信息失败: {e}")
            QMessageBox.warning(self.main_window, "错误", f"无法打开报警信息: {e}")

    def show_real_time_monitor(self):
        """显示实时监控窗口"""
        try:
            dialog = RealTimeMonitorDialog(self.main_window, self)
            dialog.exec_()
            
        except Exception as e:
            print(f"显示实时监控失败: {e}")
            QMessageBox.warning(self.main_window, "错误", f"无法打开实时监控: {e}")

    def connect_device(self, device_id):
        """连接设备"""
        try:
            # 模拟连接过程
            print(f"正在连接设备: {self.devices.get(device_id, device_id)}")
            time.sleep(0.5)  # 模拟连接延时
            
            # 更新连接状态
            self.connection_status[device_id] = True
            self.save_config_data()
            self.connection_status_changed.emit(device_id, True)
            
            print(f"设备连接成功: {self.devices.get(device_id, device_id)}")
            return True
            
        except Exception as e:
            print(f"连接设备失败: {e}")
            return False

    def disconnect_device(self, device_id):
        """断开设备连接"""
        try:
            # 更新连接状态
            self.connection_status[device_id] = False
            self.save_config_data()
            self.connection_status_changed.emit(device_id, False)
            
            print(f"设备已断开: {self.devices.get(device_id, device_id)}")
            return True
            
        except Exception as e:
            print(f"断开设备失败: {e}")
            return False

    def add_alarm(self, alarm_type, message):
        """添加报警信息"""
        alarm = {
            "type": alarm_type,
            "message": message,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "status": "active"
        }
        self.alarm_list.append(alarm)
        self.save_config_data()
        self.alarm_triggered.emit(alarm_type, message)
        print(f"新增报警: {alarm_type} - {message}")

    def clear_alarm(self, index):
        """清除报警"""
        if 0 <= index < len(self.alarm_list):
            alarm = self.alarm_list.pop(index)
            self.save_config_data()
            print(f"已清除报警: {alarm['message']}")

    def get_connection_status(self):
        """获取连接状态"""
        return self.connection_status.copy()

    def get_alarm_count(self):
        """获取报警数量"""
        return len([alarm for alarm in self.alarm_list if alarm.get("status") == "active"])


class OriginalStyleHardwareConfigDialog(QDialog):
    """硬件配置对话框（按照blade_dialogs.py的原始样式，但去掉屏蔽设置）"""

    def __init__(self, parent=None, header_manager=None):
        super().__init__(parent)
        self.header_manager = header_manager
        self.setWindowTitle("硬件配置")
        self.setFixedSize(700, 600)
        self.setModal(True)

        self.setup_ui()
        self.setup_styles()
        self.load_config()

    def setup_ui(self):
        """设置UI（按照blade_dialogs.py的实现）"""
        layout = QVBoxLayout(self)

        # 标签页
        self.tab_widget = QTabWidget()

        # 机器人设置标签页
        self.robot_tab = self.create_robot_tab()
        self.tab_widget.addTab(self.robot_tab, "机器人设置")

        # 相机设置标签页
        self.camera_tab = self.create_camera_tab()
        self.tab_widget.addTab(self.camera_tab, "相机设置")

        # 传感器设置标签页
        self.sensor_tab = self.create_sensor_tab()
        self.tab_widget.addTab(self.sensor_tab, "传感器设置")

        layout.addWidget(self.tab_widget)

        # 按钮（使用QDialogButtonBox，与原实现一致）
        from PyQt5.QtWidgets import QDialogButtonBox
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel | QDialogButtonBox.Apply
        )
        button_box.accepted.connect(self.save_and_close)
        button_box.rejected.connect(self.reject)
        button_box.button(QDialogButtonBox.Apply).clicked.connect(self.apply_config)

        layout.addWidget(button_box)

    def create_robot_tab(self):
        """创建机器人设置标签页（按照blade_dialogs.py实现）"""
        widget = QWidget()
        layout = QtWidgets.QFormLayout(widget)

        # 连接设置组
        connection_group = QGroupBox("连接设置")
        connection_layout = QtWidgets.QFormLayout(connection_group)

        self.robot_ip_edit = QLineEdit("*************")
        connection_layout.addRow("IP地址:", self.robot_ip_edit)

        self.robot_port_spin = QtWidgets.QSpinBox()
        self.robot_port_spin.setRange(1, 65535)
        self.robot_port_spin.setValue(502)
        connection_layout.addRow("端口:", self.robot_port_spin)

        # 运动参数组
        motion_group = QGroupBox("运动参数")
        motion_layout = QtWidgets.QFormLayout(motion_group)

        self.robot_speed_spin = QtWidgets.QSpinBox()
        self.robot_speed_spin.setRange(1, 100)
        self.robot_speed_spin.setValue(50)
        self.robot_speed_spin.setSuffix(" %")
        motion_layout.addRow("运动速度:", self.robot_speed_spin)

        self.robot_accel_spin = QtWidgets.QSpinBox()
        self.robot_accel_spin.setRange(1, 100)
        self.robot_accel_spin.setValue(30)
        self.robot_accel_spin.setSuffix(" %")
        motion_layout.addRow("加速度:", self.robot_accel_spin)

        self.robot_precision_spin = QtWidgets.QDoubleSpinBox()
        self.robot_precision_spin.setRange(0.01, 10.0)
        self.robot_precision_spin.setValue(0.1)
        self.robot_precision_spin.setSuffix(" mm")
        motion_layout.addRow("定位精度:", self.robot_precision_spin)

        layout.addWidget(connection_group)
        layout.addWidget(motion_group)

        return widget

    def create_camera_tab(self):
        """创建相机设置标签页（按照blade_dialogs.py实现）"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 2D识别功能块
        vision_2d_group = QGroupBox("2D识别")
        vision_2d_layout = QtWidgets.QFormLayout(vision_2d_group)

        self.vision_2d_exposure_spin = QtWidgets.QSpinBox()
        self.vision_2d_exposure_spin.setRange(1, 10000)
        self.vision_2d_exposure_spin.setValue(100)
        self.vision_2d_exposure_spin.setSuffix(" μs")
        vision_2d_layout.addRow("曝光时间:", self.vision_2d_exposure_spin)

        self.vision_2d_gain_spin = QtWidgets.QSpinBox()
        self.vision_2d_gain_spin.setRange(0, 100)
        self.vision_2d_gain_spin.setValue(50)
        self.vision_2d_gain_spin.setSuffix(" %")
        vision_2d_layout.addRow("增益:", self.vision_2d_gain_spin)

        self.vision_2d_light_spin = QtWidgets.QSpinBox()
        self.vision_2d_light_spin.setRange(0, 255)
        self.vision_2d_light_spin.setValue(128)
        vision_2d_layout.addRow("光源亮度:", self.vision_2d_light_spin)

        layout.addWidget(vision_2d_group)

        # 3D检测功能块
        vision_3d_group = QGroupBox("3D检测")
        vision_3d_layout = QtWidgets.QFormLayout(vision_3d_group)

        self.vision_3d_exposure_spin = QtWidgets.QSpinBox()
        self.vision_3d_exposure_spin.setRange(1, 10000)
        self.vision_3d_exposure_spin.setValue(200)
        self.vision_3d_exposure_spin.setSuffix(" μs")
        vision_3d_layout.addRow("曝光时间:", self.vision_3d_exposure_spin)

        self.vision_3d_gain_spin = QtWidgets.QSpinBox()
        self.vision_3d_gain_spin.setRange(0, 100)
        self.vision_3d_gain_spin.setValue(30)
        self.vision_3d_gain_spin.setSuffix(" %")
        vision_3d_layout.addRow("增益:", self.vision_3d_gain_spin)

        self.vision_3d_light_spin = QtWidgets.QSpinBox()
        self.vision_3d_light_spin.setRange(0, 255)
        self.vision_3d_light_spin.setValue(200)
        vision_3d_layout.addRow("光源亮度:", self.vision_3d_light_spin)

        layout.addWidget(vision_3d_group)

        return widget

    def create_sensor_tab(self):
        """创建传感器设置标签页（按照blade_dialogs.py实现）"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 上料传送带
        feed_conveyor_group = QGroupBox("上料传送带")
        feed_conveyor_layout = QtWidgets.QFormLayout(feed_conveyor_group)

        self.feed_conveyor_ip_edit = QLineEdit("*************")
        feed_conveyor_layout.addRow("IP地址:", self.feed_conveyor_ip_edit)

        self.feed_conveyor_port_spin = QtWidgets.QSpinBox()
        self.feed_conveyor_port_spin.setRange(1, 65535)
        self.feed_conveyor_port_spin.setValue(502)
        feed_conveyor_layout.addRow("端口:", self.feed_conveyor_port_spin)

        self.feed_conveyor_speed_spin = QtWidgets.QSpinBox()
        self.feed_conveyor_speed_spin.setRange(1, 100)
        self.feed_conveyor_speed_spin.setValue(50)
        self.feed_conveyor_speed_spin.setSuffix(" %")
        feed_conveyor_layout.addRow("传送带速度:", self.feed_conveyor_speed_spin)

        self.feed_conveyor_enabled = QCheckBox("启用")
        self.feed_conveyor_enabled.setChecked(True)
        feed_conveyor_layout.addRow("状态:", self.feed_conveyor_enabled)

        # 下料传送带
        discharge_conveyor_group = QGroupBox("下料传送带")
        discharge_conveyor_layout = QtWidgets.QFormLayout(discharge_conveyor_group)

        self.discharge_conveyor_ip_edit = QLineEdit("*************")
        discharge_conveyor_layout.addRow("IP地址:", self.discharge_conveyor_ip_edit)

        self.discharge_conveyor_port_spin = QtWidgets.QSpinBox()
        self.discharge_conveyor_port_spin.setRange(1, 65535)
        self.discharge_conveyor_port_spin.setValue(502)
        discharge_conveyor_layout.addRow("端口:", self.discharge_conveyor_port_spin)

        self.discharge_conveyor_speed_spin = QtWidgets.QSpinBox()
        self.discharge_conveyor_speed_spin.setRange(1, 100)
        self.discharge_conveyor_speed_spin.setValue(45)
        self.discharge_conveyor_speed_spin.setSuffix(" %")
        discharge_conveyor_layout.addRow("传送带速度:", self.discharge_conveyor_speed_spin)

        self.discharge_conveyor_enabled = QCheckBox("启用")
        self.discharge_conveyor_enabled.setChecked(True)
        discharge_conveyor_layout.addRow("状态:", self.discharge_conveyor_enabled)

        layout.addWidget(feed_conveyor_group)
        layout.addWidget(discharge_conveyor_group)

        return widget

    def setup_styles(self):
        """设置样式（按照blade_dialogs.py实现）"""
        self.setStyleSheet("""
            QDialog {
                background-color: #f0f0f0;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QTabWidget::pane {
                border: 1px solid #cccccc;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #e0e0e0;
                padding: 8px 16px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 2px solid #007acc;
            }
        """)

    def load_config(self):
        """加载配置（按照blade_dialogs.py实现）"""
        if not self.header_manager:
            return

        config = self.header_manager.hardware_config

        # 加载机器人配置
        if hasattr(self, 'robot_ip_edit'):
            self.robot_ip_edit.setText(config.get("robot_ip", "*************"))
        if hasattr(self, 'robot_port_spin'):
            self.robot_port_spin.setValue(config.get("robot_port", 502))
        if hasattr(self, 'robot_speed_spin'):
            self.robot_speed_spin.setValue(config.get("robot_speed", 50))
        if hasattr(self, 'robot_accel_spin'):
            self.robot_accel_spin.setValue(config.get("robot_accel", 30))
        if hasattr(self, 'robot_precision_spin'):
            self.robot_precision_spin.setValue(config.get("robot_precision", 0.1))

    def get_config(self):
        """获取配置（按照blade_dialogs.py实现）"""
        config = {}

        # 机器人配置
        if hasattr(self, 'robot_ip_edit'):
            config["robot_ip"] = self.robot_ip_edit.text()
        if hasattr(self, 'robot_port_spin'):
            config["robot_port"] = self.robot_port_spin.value()
        if hasattr(self, 'robot_speed_spin'):
            config["robot_speed"] = self.robot_speed_spin.value()
        if hasattr(self, 'robot_accel_spin'):
            config["robot_accel"] = self.robot_accel_spin.value()
        if hasattr(self, 'robot_precision_spin'):
            config["robot_precision"] = self.robot_precision_spin.value()

        # 相机配置
        if hasattr(self, 'vision_2d_exposure_spin'):
            config["vision_2d_exposure"] = self.vision_2d_exposure_spin.value()
        if hasattr(self, 'vision_2d_gain_spin'):
            config["vision_2d_gain"] = self.vision_2d_gain_spin.value()
        if hasattr(self, 'vision_2d_light_spin'):
            config["vision_2d_light"] = self.vision_2d_light_spin.value()

        if hasattr(self, 'vision_3d_exposure_spin'):
            config["vision_3d_exposure"] = self.vision_3d_exposure_spin.value()
        if hasattr(self, 'vision_3d_gain_spin'):
            config["vision_3d_gain"] = self.vision_3d_gain_spin.value()
        if hasattr(self, 'vision_3d_light_spin'):
            config["vision_3d_light"] = self.vision_3d_light_spin.value()

        # 传感器配置
        if hasattr(self, 'feed_conveyor_ip_edit'):
            config["feed_conveyor_ip"] = self.feed_conveyor_ip_edit.text()
        if hasattr(self, 'feed_conveyor_port_spin'):
            config["feed_conveyor_port"] = self.feed_conveyor_port_spin.value()
        if hasattr(self, 'feed_conveyor_speed_spin'):
            config["feed_conveyor_speed"] = self.feed_conveyor_speed_spin.value()
        if hasattr(self, 'feed_conveyor_enabled'):
            config["feed_conveyor_enabled"] = self.feed_conveyor_enabled.isChecked()

        if hasattr(self, 'discharge_conveyor_ip_edit'):
            config["discharge_conveyor_ip"] = self.discharge_conveyor_ip_edit.text()
        if hasattr(self, 'discharge_conveyor_port_spin'):
            config["discharge_conveyor_port"] = self.discharge_conveyor_port_spin.value()
        if hasattr(self, 'discharge_conveyor_speed_spin'):
            config["discharge_conveyor_speed"] = self.discharge_conveyor_speed_spin.value()
        if hasattr(self, 'discharge_conveyor_enabled'):
            config["discharge_conveyor_enabled"] = self.discharge_conveyor_enabled.isChecked()

        return config

    def save_and_close(self):
        """保存并关闭（按照blade_dialogs.py实现）"""
        if self.header_manager:
            config = self.get_config()
            self.header_manager.hardware_config.update(config)
            self.header_manager.save_config_data()
        self.accept()

    def apply_config(self):
        """应用配置（按照blade_dialogs.py实现）"""
        if self.header_manager:
            config = self.get_config()
            self.header_manager.hardware_config.update(config)
            self.header_manager.save_config_data()
        QMessageBox.information(self, "配置保存", "硬件配置已保存")


# 以下是旧的HardwareConfigDialog类的方法，已被OriginalStyleHardwareConfigDialog替代
# 保留作为备用，但不再使用

class HardwareConfigDialog(QDialog):
    """旧的硬件配置对话框（已废弃，使用OriginalStyleHardwareConfigDialog）"""

    def __init__(self, parent=None, header_manager=None):
        super().__init__(parent)
        self.header_manager = header_manager
        self.setWindowTitle("硬件配置")
        self.setFixedSize(600, 500)
        self.setModal(True)

        self.init_ui()
        self.load_current_config()

    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)

        # 创建标签页
        tab_widget = QTabWidget()

        # 网络配置标签页
        network_tab = self.create_network_tab()
        tab_widget.addTab(network_tab, "网络配置")

        # 设备配置标签页
        device_tab = self.create_device_tab()
        tab_widget.addTab(device_tab, "设备配置")

        # 系统配置标签页
        system_tab = self.create_system_tab()
        tab_widget.addTab(system_tab, "系统配置")

        layout.addWidget(tab_widget)

        # 按钮区域
        button_layout = QHBoxLayout()

        self.ok_btn = QPushButton("确定")
        self.cancel_btn = QPushButton("取消")
        self.apply_btn = QPushButton("应用")

        self.ok_btn.clicked.connect(self.accept)
        self.cancel_btn.clicked.connect(self.reject)
        self.apply_btn.clicked.connect(self.apply_config)

        button_layout.addStretch()
        button_layout.addWidget(self.ok_btn)
        button_layout.addWidget(self.cancel_btn)
        button_layout.addWidget(self.apply_btn)

        layout.addLayout(button_layout)

    def create_network_tab(self):
        """创建网络配置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 机器人配置
        robot_group = QGroupBox("机器人配置")
        robot_layout = QGridLayout(robot_group)

        robot_layout.addWidget(QLabel("IP地址:"), 0, 0)
        self.robot_ip_edit = QLineEdit()
        robot_layout.addWidget(self.robot_ip_edit, 0, 1)

        robot_layout.addWidget(QLabel("端口:"), 1, 0)
        self.robot_port_edit = QLineEdit()
        robot_layout.addWidget(self.robot_port_edit, 1, 1)

        layout.addWidget(robot_group)

        # 相机配置
        camera_group = QGroupBox("相机配置")
        camera_layout = QGridLayout(camera_group)

        camera_layout.addWidget(QLabel("2D相机IP:"), 0, 0)
        self.camera_2d_ip_edit = QLineEdit()
        camera_layout.addWidget(self.camera_2d_ip_edit, 0, 1)

        camera_layout.addWidget(QLabel("3D相机IP:"), 1, 0)
        self.camera_3d_ip_edit = QLineEdit()
        camera_layout.addWidget(self.camera_3d_ip_edit, 1, 1)

        layout.addWidget(camera_group)

        layout.addStretch()
        return widget

    def create_device_tab(self):
        """创建设备配置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 传感器配置
        sensor_group = QGroupBox("传感器配置")
        sensor_layout = QGridLayout(sensor_group)

        sensor_layout.addWidget(QLabel("串口:"), 0, 0)
        self.sensor_port_combo = QComboBox()
        self.sensor_port_combo.addItems(["COM1", "COM2", "COM3", "COM4", "COM5"])
        sensor_layout.addWidget(self.sensor_port_combo, 0, 1)

        sensor_layout.addWidget(QLabel("波特率:"), 1, 0)
        self.sensor_baudrate_combo = QComboBox()
        self.sensor_baudrate_combo.addItems(["9600", "19200", "38400", "57600", "115200"])
        sensor_layout.addWidget(self.sensor_baudrate_combo, 1, 1)

        layout.addWidget(sensor_group)

        # 夹爪配置
        gripper_group = QGroupBox("夹爪配置")
        gripper_layout = QGridLayout(gripper_group)

        self.gripper_enabled_check = QCheckBox("启用夹爪")
        gripper_layout.addWidget(self.gripper_enabled_check, 0, 0)

        gripper_layout.addWidget(QLabel("夹爪力度:"), 1, 0)
        self.gripper_force_edit = QLineEdit()
        gripper_layout.addWidget(self.gripper_force_edit, 1, 1)

        layout.addWidget(gripper_group)

        # 传送带配置
        conveyor_group = QGroupBox("传送带配置")
        conveyor_layout = QGridLayout(conveyor_group)

        conveyor_layout.addWidget(QLabel("速度 (%):"), 0, 0)
        self.conveyor_speed_edit = QLineEdit()
        conveyor_layout.addWidget(self.conveyor_speed_edit, 0, 1)

        self.conveyor_enabled_check = QCheckBox("启用传送带")
        conveyor_layout.addWidget(self.conveyor_enabled_check, 1, 0)

        layout.addWidget(conveyor_group)

        layout.addStretch()
        return widget

    def create_system_tab(self):
        """创建系统配置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 系统设置
        system_group = QGroupBox("系统设置")
        system_layout = QGridLayout(system_group)

        system_layout.addWidget(QLabel("工作模式:"), 0, 0)
        self.work_mode_combo = QComboBox()
        self.work_mode_combo.addItems(["自动模式", "手动模式", "调试模式"])
        system_layout.addWidget(self.work_mode_combo, 0, 1)

        system_layout.addWidget(QLabel("日志级别:"), 1, 0)
        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(["DEBUG", "INFO", "WARNING", "ERROR"])
        system_layout.addWidget(self.log_level_combo, 1, 1)

        self.auto_save_check = QCheckBox("自动保存配置")
        system_layout.addWidget(self.auto_save_check, 2, 0)

        layout.addWidget(system_group)

        # 安全设置
        safety_group = QGroupBox("安全设置")
        safety_layout = QGridLayout(safety_group)

        self.emergency_stop_check = QCheckBox("启用急停功能")
        safety_layout.addWidget(self.emergency_stop_check, 0, 0)

        self.safety_door_check = QCheckBox("启用安全门检测")
        safety_layout.addWidget(self.safety_door_check, 1, 0)

        layout.addWidget(safety_group)

        layout.addStretch()
        return widget

    def load_current_config(self):
        """加载当前配置"""
        if not self.header_manager:
            return

        config = self.header_manager.hardware_config

        # 网络配置
        self.robot_ip_edit.setText(config.get("robot_ip", "*************"))
        self.robot_port_edit.setText(str(config.get("robot_port", 8080)))
        self.camera_2d_ip_edit.setText(config.get("camera_2d_ip", "*************"))
        self.camera_3d_ip_edit.setText(config.get("camera_3d_ip", "*************"))

        # 设备配置
        self.sensor_port_combo.setCurrentText(config.get("sensor_port", "COM3"))
        self.sensor_baudrate_combo.setCurrentText(str(config.get("sensor_baudrate", 115200)))
        self.gripper_enabled_check.setChecked(config.get("gripper_enabled", True))
        self.gripper_force_edit.setText(str(config.get("gripper_force", 50)))
        self.conveyor_speed_edit.setText(str(config.get("conveyor_speed", 50)))
        self.conveyor_enabled_check.setChecked(config.get("conveyor_enabled", True))

        # 系统配置
        self.work_mode_combo.setCurrentText(config.get("work_mode", "自动模式"))
        self.log_level_combo.setCurrentText(config.get("log_level", "INFO"))
        self.auto_save_check.setChecked(config.get("auto_save", True))
        self.emergency_stop_check.setChecked(config.get("emergency_stop", True))
        self.safety_door_check.setChecked(config.get("safety_door", True))

    def get_config(self):
        """获取配置"""
        return {
            # 网络配置
            "robot_ip": self.robot_ip_edit.text(),
            "robot_port": int(self.robot_port_edit.text() or 8080),
            "camera_2d_ip": self.camera_2d_ip_edit.text(),
            "camera_3d_ip": self.camera_3d_ip_edit.text(),

            # 设备配置
            "sensor_port": self.sensor_port_combo.currentText(),
            "sensor_baudrate": int(self.sensor_baudrate_combo.currentText()),
            "gripper_enabled": self.gripper_enabled_check.isChecked(),
            "gripper_force": int(self.gripper_force_edit.text() or 50),
            "conveyor_speed": int(self.conveyor_speed_edit.text() or 50),
            "conveyor_enabled": self.conveyor_enabled_check.isChecked(),

            # 系统配置
            "work_mode": self.work_mode_combo.currentText(),
            "log_level": self.log_level_combo.currentText(),
            "auto_save": self.auto_save_check.isChecked(),
            "emergency_stop": self.emergency_stop_check.isChecked(),
            "safety_door": self.safety_door_check.isChecked()
        }

    def apply_config(self):
        """应用配置"""
        if self.header_manager:
            config = self.get_config()
            self.header_manager.hardware_config.update(config)
            self.header_manager.save_config_data()
            QMessageBox.information(self, "成功", "配置已应用")


class ConnectionMonitorDialog(QDialog):
    """连接状态监控对话框（保持原有blade_dialogs.py的样式，添加新功能）"""

    def __init__(self, parent=None, header_manager=None):
        super().__init__(parent)
        self.header_manager = header_manager
        self.setWindowTitle("设备连接状态监控")
        self.setFixedSize(800, 600)
        self.setModal(True)

        # 设备状态数据（保持原有格式，但与header_manager同步）
        self.device_status = {
            "robot": {"name": "机器人", "status": "已连接", "ip": "*************", "last_update": "2024-01-20 10:30:15"},
            "plc": {"name": "PLC", "status": "已连接", "ip": "************", "last_update": "2024-01-20 10:30:12"},
            "vision_2d_camera": {"name": "2D识别相机", "status": "已连接", "ip": "*************", "last_update": "2024-01-20 10:30:18"},
            "vision_2d_light": {"name": "2D光源控制器", "status": "已连接", "ip": "*************", "last_update": "2024-01-20 10:30:16"},
            "vision_3d_camera": {"name": "3D检测相机", "status": "已连接", "ip": "*************", "last_update": "2024-01-20 10:30:14"},
            "vision_3d_light": {"name": "3D光源控制器", "status": "已连接", "ip": "*************", "last_update": "2024-01-20 10:30:13"}
        }

        self.device_widgets = {}
        self.setup_ui()
        self.setup_styles()
        self.setup_timer()
        self.sync_with_header_manager()

    def setup_ui(self):
        """设置UI（保持原有blade_dialogs.py的样式）"""
        layout = QVBoxLayout(self)

        # 标题（完全按照原有样式）
        title_label = QLabel("设备连接状态实时监控")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title_label)

        # 设备状态网格（保持原有样式）
        self.create_device_grid(layout)

        # 按钮区域（保持原有样式，但添加连接/断开功能）
        button_layout = QHBoxLayout()

        # 手动刷新按钮（原有）
        refresh_btn = QPushButton("手动刷新")
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        refresh_btn.clicked.connect(self.refresh_status)

        # 自动刷新复选框（原有）
        auto_refresh_checkbox = QCheckBox("自动刷新 (每5秒)")
        auto_refresh_checkbox.setChecked(True)
        auto_refresh_checkbox.stateChanged.connect(self.toggle_auto_refresh)

        # 新增：全局连接按钮
        connect_all_btn = QPushButton("全部连接")
        connect_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        connect_all_btn.clicked.connect(self.connect_all_devices)

        # 新增：全局断开按钮
        disconnect_all_btn = QPushButton("全部断开")
        disconnect_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        disconnect_all_btn.clicked.connect(self.disconnect_all_devices)

        # 关闭按钮（原有）
        close_btn = QPushButton("关闭")
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        close_btn.clicked.connect(self.accept)

        button_layout.addWidget(refresh_btn)
        button_layout.addWidget(auto_refresh_checkbox)
        button_layout.addWidget(connect_all_btn)
        button_layout.addWidget(disconnect_all_btn)
        button_layout.addStretch()
        button_layout.addWidget(close_btn)

        layout.addLayout(button_layout)

    def create_device_grid(self, parent_layout):
        """创建设备状态网格（保持原有样式，添加连接/断开按钮）"""
        # 创建滚动区域
        scroll_area = QtWidgets.QScrollArea()
        scroll_widget = QWidget()
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)

        grid_layout = QtWidgets.QGridLayout(scroll_widget)
        grid_layout.setSpacing(15)

        self.device_widgets = {}

        row = 0
        col = 0
        for device_id, device_info in self.device_status.items():
            device_widget = self.create_device_widget(device_id, device_info)
            grid_layout.addWidget(device_widget, row, col)

            col += 1
            if col >= 2:  # 每行2个设备（保持原有布局）
                col = 0
                row += 1

        parent_layout.addWidget(scroll_area)

    def create_device_widget(self, device_id, device_info):
        """创建单个设备状态组件（保持原有样式，添加连接/断开按钮）"""
        widget = QGroupBox(device_info["name"])
        layout = QVBoxLayout(widget)

        # 状态指示器（保持原有样式）
        status_layout = QHBoxLayout()
        status_label = QLabel("状态:")
        status_indicator = QLabel(device_info["status"])

        if device_info["status"] == "已连接":
            status_indicator.setStyleSheet("""
                QLabel {
                    color: white;
                    background-color: #27ae60;
                    border-radius: 10px;
                    padding: 5px 10px;
                    font-weight: bold;
                }
            """)
        else:
            status_indicator.setStyleSheet("""
                QLabel {
                    color: white;
                    background-color: #e74c3c;
                    border-radius: 10px;
                    padding: 5px 10px;
                    font-weight: bold;
                }
            """)

        status_layout.addWidget(status_label)
        status_layout.addWidget(status_indicator)
        status_layout.addStretch()
        layout.addLayout(status_layout)

        # IP地址（保持原有样式）
        ip_layout = QHBoxLayout()
        ip_label = QLabel("IP地址:")
        ip_value = QLabel(device_info["ip"])
        ip_layout.addWidget(ip_label)
        ip_layout.addWidget(ip_value)
        ip_layout.addStretch()
        layout.addLayout(ip_layout)

        # 最后更新时间（保持原有样式）
        time_layout = QHBoxLayout()
        time_label = QLabel("最后更新:")
        time_value = QLabel(device_info["last_update"])
        time_layout.addWidget(time_label)
        time_layout.addWidget(time_value)
        time_layout.addStretch()
        layout.addLayout(time_layout)

        # 新增：连接/断开按钮
        button_layout = QHBoxLayout()

        connect_btn = QPushButton("连接")
        connect_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 5px 10px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        connect_btn.clicked.connect(lambda: self.connect_device(device_id))

        disconnect_btn = QPushButton("断开")
        disconnect_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 5px 10px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        disconnect_btn.clicked.connect(lambda: self.disconnect_device(device_id))

        button_layout.addWidget(connect_btn)
        button_layout.addWidget(disconnect_btn)
        button_layout.addStretch()
        layout.addLayout(button_layout)

        # 存储组件引用以便更新
        self.device_widgets[device_id] = {
            "widget": widget,
            "status_indicator": status_indicator,
            "time_value": time_value,
            "connect_btn": connect_btn,
            "disconnect_btn": disconnect_btn
        }

        return widget

    def setup_styles(self):
        """设置样式（完全按照原有blade_dialogs.py）"""
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 8px 0 8px;
                color: #2c3e50;
                font-size: 14px;
            }
            QLabel {
                color: #2c3e50;
                font-size: 12px;
            }
        """)

    def setup_timer(self):
        """设置定时器（保持原有5秒刷新）"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.refresh_status)
        self.timer.start(5000)  # 每5秒刷新一次

    def toggle_auto_refresh(self, state):
        """切换自动刷新（保持原有功能）"""
        if state == 2:  # 选中
            self.timer.start(5000)
        else:
            self.timer.stop()

    def refresh_status(self):
        """刷新状态（保持原有功能，添加与header_manager同步）"""
        from datetime import datetime
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 与header_manager同步状态
        if self.header_manager:
            connection_status = self.header_manager.get_connection_status()

            # 更新设备状态
            for device_id, widget_info in self.device_widgets.items():
                # 映射header_manager的设备到原有设备
                mapped_status = self.map_device_status(device_id, connection_status)

                if mapped_status is not None:
                    status_text = "已连接" if mapped_status else "未连接"

                    # 更新状态指示器
                    widget_info["status_indicator"].setText(status_text)
                    if mapped_status:
                        widget_info["status_indicator"].setStyleSheet("""
                            QLabel {
                                color: white;
                                background-color: #27ae60;
                                border-radius: 10px;
                                padding: 5px 10px;
                                font-weight: bold;
                            }
                        """)
                        widget_info["connect_btn"].setEnabled(False)
                        widget_info["disconnect_btn"].setEnabled(True)
                    else:
                        widget_info["status_indicator"].setStyleSheet("""
                            QLabel {
                                color: white;
                                background-color: #e74c3c;
                                border-radius: 10px;
                                padding: 5px 10px;
                                font-weight: bold;
                            }
                        """)
                        widget_info["connect_btn"].setEnabled(True)
                        widget_info["disconnect_btn"].setEnabled(False)

                    # 更新时间
                    widget_info["time_value"].setText(current_time)

    def map_device_status(self, device_id, connection_status):
        """映射设备状态（将原有设备ID映射到header_manager的设备）"""
        device_mapping = {
            "robot": "robot",
            "plc": "robot",  # PLC映射到机器人
            "vision_2d_camera": "camera_2d",
            "vision_2d_light": "camera_2d",  # 2D光源映射到2D相机
            "vision_3d_camera": "camera_3d",
            "vision_3d_light": "camera_3d"   # 3D光源映射到3D相机
        }

        mapped_device = device_mapping.get(device_id)
        if mapped_device and mapped_device in connection_status:
            return connection_status[mapped_device]
        return None

    def sync_with_header_manager(self):
        """与header_manager同步初始状态"""
        if self.header_manager:
            connection_status = self.header_manager.get_connection_status()

            for device_id, device_info in self.device_status.items():
                mapped_status = self.map_device_status(device_id, connection_status)
                if mapped_status is not None:
                    device_info["status"] = "已连接" if mapped_status else "未连接"

    def connect_device(self, device_id):
        """连接设备（新增功能）"""
        if self.header_manager:
            # 映射到header_manager的设备
            device_mapping = {
                "robot": "robot",
                "plc": "robot",
                "vision_2d_camera": "camera_2d",
                "vision_2d_light": "camera_2d",
                "vision_3d_camera": "camera_3d",
                "vision_3d_light": "camera_3d"
            }

            mapped_device = device_mapping.get(device_id)
            if mapped_device:
                success = self.header_manager.connect_device(mapped_device)
                if success:
                    device_name = self.device_status[device_id]["name"]
                    QMessageBox.information(self, "连接成功", f"{device_name} 连接成功")
                    self.refresh_status()
                else:
                    device_name = self.device_status[device_id]["name"]
                    QMessageBox.warning(self, "连接失败", f"{device_name} 连接失败")

    def disconnect_device(self, device_id):
        """断开设备（新增功能）"""
        if self.header_manager:
            # 映射到header_manager的设备
            device_mapping = {
                "robot": "robot",
                "plc": "robot",
                "vision_2d_camera": "camera_2d",
                "vision_2d_light": "camera_2d",
                "vision_3d_camera": "camera_3d",
                "vision_3d_light": "camera_3d"
            }

            mapped_device = device_mapping.get(device_id)
            if mapped_device:
                success = self.header_manager.disconnect_device(mapped_device)
                if success:
                    device_name = self.device_status[device_id]["name"]
                    QMessageBox.information(self, "断开成功", f"{device_name} 已断开")
                    self.refresh_status()

    def connect_all_devices(self):
        """连接所有设备（新增功能）"""
        if self.header_manager:
            success_count = 0
            for device_id in self.header_manager.devices.keys():
                if self.header_manager.connect_device(device_id):
                    success_count += 1

            self.refresh_status()
            QMessageBox.information(self, "连接完成", f"成功连接 {success_count} 个设备")

    def disconnect_all_devices(self):
        """断开所有设备（新增功能）"""
        if self.header_manager:
            for device_id in self.header_manager.devices.keys():
                self.header_manager.disconnect_device(device_id)

            self.refresh_status()
            QMessageBox.information(self, "断开完成", "所有设备已断开")

    def closeEvent(self, event):
        """关闭事件（保持原有功能）"""
        if hasattr(self, 'timer'):
            self.timer.stop()
        super().closeEvent(event)


# 旧的ConnectionMonitorDialog方法已删除，避免与新实现冲突


class AlarmInfoDialog(QDialog):
    """报警信息对话框"""

    def __init__(self, parent=None, header_manager=None):
        super().__init__(parent)
        self.header_manager = header_manager
        self.setWindowTitle("报警信息")
        self.setFixedSize(600, 400)
        self.setModal(True)

        self.init_ui()
        self.load_alarms()

    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)

        # 标题和统计
        header_layout = QHBoxLayout()

        title_label = QLabel("系统报警信息")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold;")
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        self.alarm_count_label = QLabel("报警数量: 0")
        self.alarm_count_label.setStyleSheet("color: red; font-weight: bold;")
        header_layout.addWidget(self.alarm_count_label)

        layout.addLayout(header_layout)

        # 操作按钮
        button_layout = QHBoxLayout()

        self.add_test_alarm_btn = QPushButton("添加测试报警")
        self.clear_all_btn = QPushButton("清除所有")
        self.refresh_btn = QPushButton("刷新")

        self.add_test_alarm_btn.clicked.connect(self.add_test_alarm)
        self.clear_all_btn.clicked.connect(self.clear_all_alarms)
        self.refresh_btn.clicked.connect(self.load_alarms)

        button_layout.addWidget(self.add_test_alarm_btn)
        button_layout.addWidget(self.clear_all_btn)
        button_layout.addWidget(self.refresh_btn)
        button_layout.addStretch()

        layout.addLayout(button_layout)

        # 报警列表
        self.alarm_list = QListWidget()
        layout.addWidget(self.alarm_list)

        # 关闭按钮
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.accept)
        layout.addWidget(close_btn)

    def load_alarms(self):
        """加载报警信息"""
        self.alarm_list.clear()

        if not self.header_manager:
            return

        alarms = self.header_manager.alarm_list
        active_count = 0

        for i, alarm in enumerate(alarms):
            if alarm.get("status") == "active":
                active_count += 1

                # 创建报警项
                item_widget = QWidget()
                item_layout = QHBoxLayout(item_widget)
                item_layout.setContentsMargins(5, 5, 5, 5)

                # 报警图标
                icon_label = QLabel("⚠")
                icon_label.setStyleSheet("color: red; font-size: 16px; font-weight: bold;")
                icon_label.setFixedWidth(30)
                item_layout.addWidget(icon_label)

                # 报警信息
                info_layout = QVBoxLayout()

                type_label = QLabel(f"类型: {alarm['type']}")
                type_label.setStyleSheet("font-weight: bold;")
                info_layout.addWidget(type_label)

                message_label = QLabel(f"消息: {alarm['message']}")
                info_layout.addWidget(message_label)

                time_label = QLabel(f"时间: {alarm['timestamp']}")
                time_label.setStyleSheet("color: gray; font-size: 10px;")
                info_layout.addWidget(time_label)

                item_layout.addLayout(info_layout)

                # 清除按钮
                clear_btn = QPushButton("清除")
                clear_btn.setFixedSize(60, 30)
                clear_btn.clicked.connect(lambda checked, idx=i: self.clear_alarm(idx))
                item_layout.addWidget(clear_btn)

                # 添加到列表
                item = QListWidgetItem()
                item.setSizeHint(item_widget.sizeHint())
                self.alarm_list.addItem(item)
                self.alarm_list.setItemWidget(item, item_widget)

        # 更新计数
        self.alarm_count_label.setText(f"报警数量: {active_count}")

        if active_count == 0:
            # 显示无报警信息
            no_alarm_item = QListWidgetItem("✅ 当前系统状态正常，无报警信息")
            no_alarm_item.setTextAlignment(Qt.AlignCenter)
            self.alarm_list.addItem(no_alarm_item)

    def add_test_alarm(self):
        """添加测试报警"""
        if self.header_manager:
            import random
            alarm_types = ["设备故障", "通信异常", "传感器错误", "安全警告", "系统异常"]
            messages = [
                "机器人通信超时",
                "相机连接失败",
                "传感器读数异常",
                "安全门未关闭",
                "系统内存不足"
            ]

            alarm_type = random.choice(alarm_types)
            message = random.choice(messages)

            self.header_manager.add_alarm(alarm_type, message)
            self.load_alarms()

    def clear_alarm(self, index):
        """清除指定报警"""
        if self.header_manager:
            self.header_manager.clear_alarm(index)
            self.load_alarms()

    def clear_all_alarms(self):
        """清除所有报警"""
        if self.header_manager:
            reply = QMessageBox.question(self, "确认", "确定要清除所有报警信息吗？")
            if reply == QMessageBox.Yes:
                self.header_manager.alarm_list.clear()
                self.header_manager.save_config_data()
                self.load_alarms()


class RealTimeMonitorDialog(QDialog):
    """实时监控对话框"""

    def __init__(self, parent=None, header_manager=None):
        super().__init__(parent)
        self.header_manager = header_manager
        self.setWindowTitle("实时监控")
        self.setFixedSize(800, 600)
        self.setModal(True)

        self.init_ui()
        self.start_monitoring()

    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)

        # 标题
        title_label = QLabel("实时监控")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; padding: 10px;")
        layout.addWidget(title_label)

        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)

        # 左侧：相机画面
        camera_widget = self.create_camera_widget()
        splitter.addWidget(camera_widget)

        # 右侧：监控信息
        info_widget = self.create_info_widget()
        splitter.addWidget(info_widget)

        splitter.setStretchFactor(0, 2)  # 相机画面占2/3
        splitter.setStretchFactor(1, 1)  # 信息面板占1/3

        layout.addWidget(splitter)

        # 控制按钮
        control_layout = QHBoxLayout()

        self.start_btn = QPushButton("开始监控")
        self.stop_btn = QPushButton("停止监控")
        self.snapshot_btn = QPushButton("截图")
        self.close_btn = QPushButton("关闭")

        self.start_btn.clicked.connect(self.start_monitoring)
        self.stop_btn.clicked.connect(self.stop_monitoring)
        self.snapshot_btn.clicked.connect(self.take_snapshot)
        self.close_btn.clicked.connect(self.accept)

        control_layout.addWidget(self.start_btn)
        control_layout.addWidget(self.stop_btn)
        control_layout.addWidget(self.snapshot_btn)
        control_layout.addStretch()
        control_layout.addWidget(self.close_btn)

        layout.addLayout(control_layout)

    def create_camera_widget(self):
        """创建相机画面控件"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 相机选择
        camera_layout = QHBoxLayout()
        camera_layout.addWidget(QLabel("相机:"))

        self.camera_combo = QComboBox()
        self.camera_combo.addItems(["2D相机", "3D相机", "全部相机"])
        camera_layout.addWidget(self.camera_combo)

        camera_layout.addStretch()
        layout.addLayout(camera_layout)

        # 画面显示区域
        self.camera_display = QLabel("相机画面显示区域")
        self.camera_display.setStyleSheet("""
            QLabel {
                border: 2px solid #ccc;
                background-color: #f0f0f0;
                text-align: center;
                font-size: 14px;
                color: #666;
            }
        """)
        self.camera_display.setMinimumSize(400, 300)
        self.camera_display.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.camera_display)

        return widget

    def create_info_widget(self):
        """创建信息面板控件"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 系统状态
        status_group = QGroupBox("系统状态")
        status_layout = QVBoxLayout(status_group)

        self.status_text = QTextEdit()
        self.status_text.setMaximumHeight(150)
        self.status_text.setReadOnly(True)
        status_layout.addWidget(self.status_text)

        layout.addWidget(status_group)

        # 设备状态
        device_group = QGroupBox("设备状态")
        device_layout = QVBoxLayout(device_group)

        self.device_status_list = QListWidget()
        self.device_status_list.setMaximumHeight(200)
        device_layout.addWidget(self.device_status_list)

        layout.addWidget(device_group)

        return widget

    def start_monitoring(self):
        """开始监控"""
        self.status_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] 开始实时监控")
        self.camera_display.setText("监控中...\n(模拟相机画面)")
        self.camera_display.setStyleSheet("""
            QLabel {
                border: 2px solid green;
                background-color: #e8f5e8;
                text-align: center;
                font-size: 14px;
                color: green;
            }
        """)

        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)

        # 更新设备状态
        self.update_device_status()

    def stop_monitoring(self):
        """停止监控"""
        self.status_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] 停止实时监控")
        self.camera_display.setText("监控已停止")
        self.camera_display.setStyleSheet("""
            QLabel {
                border: 2px solid #ccc;
                background-color: #f0f0f0;
                text-align: center;
                font-size: 14px;
                color: #666;
            }
        """)

        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)

    def take_snapshot(self):
        """截图"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"snapshot_{timestamp}.jpg"
        self.status_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] 截图已保存: {filename}")

    def update_device_status(self):
        """更新设备状态"""
        self.device_status_list.clear()

        if self.header_manager:
            connection_status = self.header_manager.get_connection_status()

            for device_id, device_name in self.header_manager.devices.items():
                is_connected = connection_status.get(device_id, False)
                status_text = "✅ 在线" if is_connected else "❌ 离线"

                item = QListWidgetItem(f"{device_name}: {status_text}")
                if is_connected:
                    item.setBackground(QtWidgets.QColor(200, 255, 200))
                else:
                    item.setBackground(QtWidgets.QColor(255, 200, 200))

                self.device_status_list.addItem(item)

    def cleanup(self):
        """清理模块资源"""
        try:
            print("🧹 清理头部模块资源...")

            # 断开所有设备连接
            if hasattr(self, 'devices') and self.devices:
                for device_id in self.devices.keys():
                    self.disconnect_device(device_id)
                print("✅ 已断开所有设备连接")

            # 停止可能的定时器
            # 这里主要是清理连接监控对话框中的定时器

            print("✅ 头部模块资源清理完成")

        except Exception as e:
            print(f"❌ 清理头部模块资源失败: {e}")
            import traceback
            traceback.print_exc()
