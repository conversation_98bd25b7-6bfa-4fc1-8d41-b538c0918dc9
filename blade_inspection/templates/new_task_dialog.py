#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全新的任务创建对话框 - 优化UI设计
"""

import os
import json
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                             QLabel, QLineEdit, QPushButton, QTextEdit,
                             QFrame, QMessageBox, QWidget, QListWidget, QListWidgetItem,
                             QFileDialog)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap, QIcon


class NewTaskDialog(QDialog):
    """全新的任务创建对话框"""
    
    task_created = pyqtSignal(dict)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("创建检测任务")
        self.setFixedSize(900, 700)
        self.setModal(True)
        
        # 可用程序列表
        self.available_programs = {
            'S88.88.8888': 'S88.88.8888.json',
            'S21.08.6230': 'S21.08.6230.json',
            'S44.55.6666': 'S44.55.6666.json'
        }
        
        self.selected_program = None
        self.task_info_data = {}
        self.selected_folder_path = None

        self.setup_ui()
        
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # 标题区域
        self.create_header(layout)
        
        # 主要内容区域
        content_layout = QHBoxLayout()
        content_layout.setSpacing(30)
        
        # 左侧：程序选择区域
        self.create_program_selection(content_layout)
        
        # 右侧：任务信息区域
        self.create_task_info_area(content_layout)
        
        layout.addLayout(content_layout)
        
        # 底部按钮区域
        self.create_buttons(layout)
        
        # 设置样式
        self.setup_styles()
        
    def create_header(self, parent_layout):
        """创建标题区域"""
        header_frame = QFrame()
        header_frame.setFixedHeight(80)
        header_layout = QVBoxLayout(header_frame)
        
        # 主标题
        title = QLabel("🚀 创建检测任务")
        title.setAlignment(Qt.AlignCenter)
        title.setObjectName("main_title")
        header_layout.addWidget(title)
        
        # 副标题
        subtitle = QLabel("选择任务文件夹，系统将自动读取task_info.json文件")
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setObjectName("subtitle")
        header_layout.addWidget(subtitle)
        
        parent_layout.addWidget(header_frame)
        
    def create_program_selection(self, parent_layout):
        """创建程序选择区域"""
        left_frame = QFrame()
        left_frame.setFixedWidth(350)
        left_frame.setObjectName("left_frame")
        left_layout = QVBoxLayout(left_frame)
        
        # 区域标题
        section_title = QLabel("📋 任务配置")
        section_title.setObjectName("section_title")
        left_layout.addWidget(section_title)
        
        # 文件夹选择区域
        folder_frame = QFrame()
        folder_frame.setObjectName("input_frame")
        folder_layout = QVBoxLayout(folder_frame)

        folder_layout.addWidget(QLabel("选择任务文件夹:"))

        folder_select_layout = QHBoxLayout()
        self.select_folder_btn = QPushButton("📁 选择文件夹")
        self.select_folder_btn.setObjectName("select_folder_btn")
        self.select_folder_btn.clicked.connect(self.select_task_folder)
        folder_select_layout.addWidget(self.select_folder_btn)

        self.folder_path_label = QLabel("未选择文件夹")
        self.folder_path_label.setObjectName("folder_path_label")
        self.folder_path_label.setWordWrap(True)
        folder_select_layout.addWidget(self.folder_path_label, 1)

        folder_layout.addLayout(folder_select_layout)
        left_layout.addWidget(folder_frame)

        # 件号显示（从task_info.json读取）
        part_frame = QFrame()
        part_frame.setObjectName("input_frame")
        part_layout = QVBoxLayout(part_frame)

        part_layout.addWidget(QLabel("件号显示:"))
        self.part_display = QLineEdit()
        self.part_display.setPlaceholderText("选择文件夹后自动显示...")
        self.part_display.setObjectName("part_input")
        self.part_display.setReadOnly(True)
        part_layout.addWidget(self.part_display)

        left_layout.addWidget(part_frame)
        
        # 程序号显示（从task_info.json读取）
        program_frame = QFrame()
        program_frame.setObjectName("input_frame")
        program_layout = QVBoxLayout(program_frame)

        program_layout.addWidget(QLabel("程序号:"))
        self.program_display = QLineEdit()
        self.program_display.setPlaceholderText("选择文件夹后自动显示...")
        self.program_display.setObjectName("part_input")
        self.program_display.setReadOnly(True)
        program_layout.addWidget(self.program_display)

        left_layout.addWidget(program_frame)

        # 任务参数设置区域
        params_frame = QFrame()
        params_frame.setObjectName("input_frame")
        params_layout = QVBoxLayout(params_frame)

        # 标题
        params_layout.addWidget(QLabel("任务参数设置:"))

        # 水平布局包含任务数量、速度和应用按钮
        params_horizontal_layout = QHBoxLayout()

        # 任务数量
        quantity_label = QLabel("任务数量:")
        quantity_label.setFixedWidth(60)
        params_horizontal_layout.addWidget(quantity_label)

        self.quantity_display = QLineEdit()
        self.quantity_display.setPlaceholderText("1")
        self.quantity_display.setObjectName("part_input")
        self.quantity_display.setFixedWidth(80)
        params_horizontal_layout.addWidget(self.quantity_display)

        # 速度设置
        speed_label = QLabel("速度:")
        speed_label.setFixedWidth(40)
        params_horizontal_layout.addWidget(speed_label)

        self.speed_input = QLineEdit()
        self.speed_input.setPlaceholderText("100")
        self.speed_input.setObjectName("part_input")
        self.speed_input.setFixedWidth(80)
        params_horizontal_layout.addWidget(self.speed_input)

        # 应用按钮
        self.apply_params_btn = QPushButton("应用")
        self.apply_params_btn.setObjectName("apply_button")
        self.apply_params_btn.setFixedWidth(60)
        self.apply_params_btn.clicked.connect(self.apply_task_params)
        params_horizontal_layout.addWidget(self.apply_params_btn)

        # 添加弹性空间
        params_horizontal_layout.addStretch()

        params_layout.addLayout(params_horizontal_layout)
        left_layout.addWidget(params_frame)

        # 选中状态显示
        self.selection_label = QLabel("请选择任务文件夹")
        self.selection_label.setObjectName("selection_label")
        left_layout.addWidget(self.selection_label)
        
        parent_layout.addWidget(left_frame)
        
    def create_task_info_area(self, parent_layout):
        """创建任务信息区域"""
        right_frame = QFrame()
        right_frame.setObjectName("right_frame")
        right_layout = QVBoxLayout(right_frame)
        
        # 区域标题
        section_title = QLabel("📊 任务详细信息")
        section_title.setObjectName("section_title")
        right_layout.addWidget(section_title)
        
        # 任务信息显示
        self.task_info_text = QTextEdit()
        self.task_info_text.setObjectName("task_info_text")
        self.task_info_text.setReadOnly(True)
        self.task_info_text.setPlaceholderText("选择任务文件夹后，这里将显示详细的任务信息...")
        right_layout.addWidget(self.task_info_text)
        
        # 状态信息
        status_frame = QFrame()
        status_frame.setObjectName("status_frame")
        status_layout = QHBoxLayout(status_frame)
        
        self.status_icon = QLabel("⏳")
        self.status_icon.setFixedSize(24, 24)
        status_layout.addWidget(self.status_icon)
        
        self.status_text = QLabel("等待选择任务文件夹...")
        self.status_text.setObjectName("status_text")
        status_layout.addWidget(self.status_text)
        status_layout.addStretch()

        right_layout.addWidget(status_frame)
        
        parent_layout.addWidget(right_frame)
        
    def create_buttons(self, parent_layout):
        """创建按钮区域"""
        button_frame = QFrame()
        button_frame.setFixedHeight(60)
        button_layout = QHBoxLayout(button_frame)
        
        # 刷新按钮
        refresh_btn = QPushButton("🔄 重新选择文件夹")
        refresh_btn.setObjectName("secondary_button")
        refresh_btn.clicked.connect(self.select_task_folder)
        button_layout.addWidget(refresh_btn)
        
        button_layout.addStretch()
        
        # 取消按钮
        cancel_btn = QPushButton("❌ 取消")
        cancel_btn.setObjectName("cancel_button")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        # 创建按钮
        self.create_btn = QPushButton("✅ 创建任务")
        self.create_btn.setObjectName("create_button")
        self.create_btn.setEnabled(False)
        self.create_btn.clicked.connect(self.create_task)
        button_layout.addWidget(self.create_btn)
        
        parent_layout.addWidget(button_frame)
        
    def setup_styles(self):
        """设置样式"""
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
            }
            
            #main_title {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                margin: 10px 0;
            }
            
            #subtitle {
                font-size: 14px;
                color: #6c757d;
                margin-bottom: 10px;
            }
            
            #section_title {
                font-size: 16px;
                font-weight: bold;
                color: #495057;
                padding: 10px 0;
                border-bottom: 2px solid #dee2e6;
                margin-bottom: 15px;
            }
            
            #left_frame, #right_frame {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 12px;
                padding: 20px;
            }
            
            #input_frame {
                background-color: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 8px;
                padding: 15px;
                margin-bottom: 15px;
            }
            
            #part_input {
                padding: 8px 10px;
                border: 2px solid #ced4da;
                border-radius: 6px;
                font-size: 14px;
                background-color: white;
                text-align: center;
            }
            
            #part_input:focus {
                border-color: #80bdff;
                outline: none;
            }

            #select_folder_btn {
                background-color: #17a2b8;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
                min-width: 120px;
            }

            #select_folder_btn:hover {
                background-color: #138496;
            }

            #folder_path_label {
                color: #6c757d;
                font-size: 12px;
                padding: 8px;
                background-color: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 4px;
                margin-left: 10px;
            }

            #apply_button {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-size: 12px;
                font-weight: bold;
                margin-left: 10px;
            }

            #apply_button:hover {
                background-color: #218838;
            }

            #apply_button:pressed {
                background-color: #1e7e34;
            }
            
            #program_list {
                border: 2px solid #ced4da;
                border-radius: 8px;
                background-color: white;
                font-size: 14px;
                min-height: 200px;
            }
            
            #program_list::item {
                padding: 12px;
                border-bottom: 1px solid #e9ecef;
            }
            
            #program_list::item:hover {
                background-color: #e3f2fd;
            }
            
            #program_list::item:selected {
                background-color: #2196f3;
                color: white;
            }
            
            #selection_label {
                font-weight: bold;
                color: #6c757d;
                padding: 10px;
                background-color: #f8f9fa;
                border-radius: 6px;
                margin-top: 10px;
            }
            
            #task_info_text {
                border: 2px solid #ced4da;
                border-radius: 8px;
                background-color: white;
                font-size: 13px;
                font-family: 'Consolas', 'Monaco', monospace;
                padding: 15px;
            }
            
            #status_frame {
                background-color: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 6px;
                padding: 10px;
                margin-top: 10px;
            }
            
            #status_text {
                font-size: 14px;
                color: #495057;
            }
            
            #create_button {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 30px;
                font-size: 14px;
                font-weight: bold;
                min-width: 120px;
            }
            
            #create_button:hover {
                background-color: #218838;
            }
            
            #create_button:disabled {
                background-color: #6c757d;
            }
            
            #cancel_button {
                background-color: #dc3545;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 30px;
                font-size: 14px;
                font-weight: bold;
                min-width: 120px;
            }
            
            #cancel_button:hover {
                background-color: #c82333;
            }
            
            #secondary_button {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            
            #secondary_button:hover {
                background-color: #5a6268;
            }
        """)
        
    def select_task_folder(self):
        """选择任务文件夹"""
        try:
            # 默认打开指定目录
            default_path = r"D:\blade_inspect\task"
            if not os.path.exists(default_path):
                default_path = os.getcwd()

            folder_path = QFileDialog.getExistingDirectory(
                self,
                "选择任务文件夹",
                default_path,
                QFileDialog.ShowDirsOnly | QFileDialog.DontResolveSymlinks
            )

            if folder_path:
                self.selected_folder_path = folder_path
                self.folder_path_label.setText(f"已选择: {folder_path}")
                self.folder_path_label.setStyleSheet("color: #28a745; font-weight: bold;")

                # 尝试读取task_info.json
                self.load_task_info_from_folder(folder_path)

        except Exception as e:
            print(f"选择文件夹失败: {e}")
            QMessageBox.warning(self, "错误", f"选择文件夹失败: {e}")

    def load_task_info_from_folder(self, folder_path):
        """从选择的文件夹加载task_info.json"""
        try:
            task_info_path = os.path.join(folder_path, "task_info.json")

            if not os.path.exists(task_info_path):
                QMessageBox.warning(self, "文件不存在", f"在选择的文件夹中未找到 task_info.json 文件\n\n路径: {task_info_path}")
                return

            # 读取task_info.json
            with open(task_info_path, 'r', encoding='utf-8') as f:
                self.task_info_data = json.load(f)

            print("task_info.json 加载成功")

            # 更新界面显示
            self.update_ui_from_task_info()

            # 更新状态
            self.status_icon.setText("✅")
            self.status_text.setText("任务信息加载成功")

            # 启用创建按钮
            self.create_btn.setEnabled(True)

            # 显示任务信息
            self.display_task_info()

        except json.JSONDecodeError as e:
            QMessageBox.critical(self, "文件格式错误", f"task_info.json 文件格式不正确:\n\n{str(e)}")
        except Exception as e:
            print(f"加载任务信息失败: {e}")
            QMessageBox.warning(self, "加载失败", f"加载任务信息失败: {e}")

    def update_ui_from_task_info(self):
        """根据task_info.json更新界面显示"""
        try:
            if not self.task_info_data:
                return

            # 更新件号显示
            product_name = self.task_info_data.get('ProductName', '')
            self.part_display.setText(product_name)
            self.selected_program = product_name

            # 更新程序号显示
            program_files = self.task_info_data.get('ProgramFiles', [])
            if program_files:
                program_name = program_files[0].get('ProgramName', '')
                self.program_display.setText(program_name)

            # 更新任务数量
            task_qty = self.task_info_data.get('TaskQty', 1)
            self.quantity_display.setText(str(task_qty).strip())

            # 更新速度设置
            speed = self.task_info_data.get('Speed', 100)
            self.speed_input.setText(str(speed))

            # 更新选中状态显示
            self.selection_label.setText(f"✅ 已加载任务: {product_name}")
            self.selection_label.setStyleSheet("color: #28a745; font-weight: bold;")

        except Exception as e:
            print(f"更新界面失败: {e}")

    def apply_task_params(self):
        """应用任务参数设置"""
        try:
            # 获取任务数量
            quantity_text = self.quantity_display.text().strip()
            if quantity_text:
                try:
                    quantity = int(quantity_text)
                    if quantity <= 0:
                        QMessageBox.warning(self, "参数错误", "任务数量必须大于0！")
                        return
                except ValueError:
                    QMessageBox.warning(self, "参数错误", "任务数量必须是有效的数字！")
                    return
            else:
                quantity = 1

            # 获取速度设置
            speed_text = self.speed_input.text().strip()
            if speed_text:
                try:
                    speed = int(speed_text)
                    if speed <= 0 or speed > 200:
                        QMessageBox.warning(self, "参数错误", "速度必须在1-200之间！")
                        return
                except ValueError:
                    QMessageBox.warning(self, "参数错误", "速度必须是有效的数字！")
                    return
            else:
                speed = 100

            # 更新任务信息
            if self.task_info_data:
                self.task_info_data['TaskQty'] = quantity
                self.task_info_data['Speed'] = speed

                # 更新显示
                self.quantity_display.setText(str(quantity).strip())
                self.speed_input.setText(str(speed).strip())

                # 重新显示任务信息
                self.display_task_info()

                QMessageBox.information(self, "应用成功",
                    f"任务参数已更新：\n任务数量: {quantity}\n速度: {speed}")
            else:
                QMessageBox.warning(self, "错误", "请先选择任务文件夹！")

        except Exception as e:
            print(f"应用任务参数失败: {e}")
            QMessageBox.warning(self, "错误", f"应用任务参数失败: {e}")

    def display_task_info(self):
        """显示任务信息 - 按照图片格式显示"""
        try:
            if not self.task_info_data:
                self.task_info_text.setText("❌ 未找到任务信息文件 (task_info.json)")
                return

            # 按照图片格式显示任务信息
            info_text = "【任务详情】\n"
            info_text += f"产线名称: {self.task_info_data.get('ProductLineName', '未知')}\n"
            info_text += f"任务名称: {self.task_info_data.get('MOTaskName', '未知')}\n"
            info_text += f"工单名称: {self.task_info_data.get('MOName', '未知')}\n"
            info_text += f"工序名称: {self.task_info_data.get('SpecificationName', '未知')}\n"
            info_text += f"产品名称: {self.task_info_data.get('ProductName', '未知')}\n"
            info_text += f"批号: {self.task_info_data.get('LotSN', '未知')}\n"
            info_text += f"规格名称: {self.task_info_data.get('SpecificationName', '未知')}\n"
            info_text += f"任务数量: {self.task_info_data.get('TaskQty', '未知')}\n"
            info_text += f"速度设置: {self.task_info_data.get('Speed', '100')}\n"
            info_text += f"创建时间: {self.task_info_data.get('create_time', '未知')}\n"
            info_text += f"操作员: {self.task_info_data.get('operator', '未知')}\n"

            # 添加物料信息
            materials = self.task_info_data.get('MaterialsBOM', [])
            if materials:
                info_text += "\n【物料清单】\n"
                for i, material in enumerate(materials, 1):
                    info_text += f"{i}. 产品名称: {material.get('ProductName', '未知')}\n"
                    info_text += f"   产品描述: {material.get('ProductDescription', '未知')}\n"
                    info_text += f"   材质: {material.get('Material', '未知')}\n"
                    info_text += f"   厚度: {material.get('Thickness', '未知')}\n"
                    info_text += f"   直径: {material.get('Diameter', '未知')}\n"
                    if i < len(materials):
                        info_text += "\n"

            # 添加程序文件信息
            program_files = self.task_info_data.get('ProgramFiles', [])
            if program_files:
                info_text += "\n【程序文件】\n"
                for i, program in enumerate(program_files, 1):
                    info_text += f"{i}. 程序名称: {program.get('ProgramName', '未知')}\n"
                    info_text += f"   设备名称: {program.get('EquipmentName', '未知')}\n"
                    info_text += f"   程序文件: {program.get('ProgramFileURL', '未知')}\n"
                    if i < len(program_files):
                        info_text += "\n"

            self.task_info_text.setPlainText(info_text)

        except Exception as e:
            self.task_info_text.setText(f"❌ 显示任务信息失败: {e}")
            print(f"显示任务信息失败: {e}")
            
    def create_task(self):
        """创建任务"""
        try:
            if not self.selected_folder_path or not self.task_info_data:
                QMessageBox.warning(self, "选择错误", "请先选择任务文件夹并确保加载了任务信息")
                return

            # 获取任务数量
            try:
                task_quantity = int(self.quantity_display.text() or "1")
            except ValueError:
                task_quantity = 1

            # 获取速度设置
            try:
                task_speed = int(self.speed_input.text() or "100")
            except ValueError:
                task_speed = 100

            # 构建任务数据
            task_data = {
                'part_number': self.part_display.text() or self.task_info_data.get('ProductName', ''),
                'program_name': self.program_display.text() or self.task_info_data.get('ProductName', ''),
                'task_info': self.task_info_data,
                'task_quantity': task_quantity,
                'task_speed': task_speed,
                'folder_path': self.selected_folder_path,
                'created_time': __import__('datetime').datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

            # 发送信号
            self.task_created.emit(task_data)

            product_name = self.task_info_data.get('ProductName', '未知任务')
            QMessageBox.information(self, "创建成功",
                f"✅ 任务 {product_name} 创建成功！\n数量: {task_quantity}\n速度: {task_speed}")
            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "创建失败", f"❌ 任务创建失败: {e}")
            print(f"创建任务失败: {e}")


# 测试函数
def test_new_dialog():
    """测试新对话框"""
    import sys
    from PyQt5.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    
    dialog = NewTaskDialog()
    dialog.task_created.connect(lambda data: print(f"任务创建: {data}"))
    
    result = dialog.exec_()
    print(f"对话框结果: {result}")


if __name__ == "__main__":
    test_new_dialog()
