#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配方导入模块 - 分层设计的配方管理功能
"""

import os
import json
import yaml
import shutil
import re
from datetime import datetime
from PyQt5.QtWidgets import QMessageBox, QInputDialog, QTableWidgetItem, QPushButton, QHBoxLayout, QWidget
from PyQt5.QtCore import Qt


class RecipeImportModule:
    """配方导入管理模块"""
    
    def __init__(self, main_window):
        self.main_window = main_window
        self.cache_path = None
        self.recipe_storage_path = None
        self.recipe_data_list = []
        self.hidden_recipes = set()
        
        # 初始化路径
        self.initialize_paths()
    
    def initialize_paths(self):
        """初始化路径配置"""
        try:
            # 读取配置文件获取cache_path
            self.cache_path = self.load_cache_path_from_config()
            
            # 配方存储路径 - 使用配置文件中的cache_path + task
            self.recipe_storage_path = os.path.join(self.cache_path, "task")
            
            # 确保存储目录存在
            os.makedirs(self.recipe_storage_path, exist_ok=True)
            
            print(f"配方存储路径初始化完成: {self.recipe_storage_path}")
            
        except Exception as e:
            print(f"初始化路径失败: {e}")
            # 使用默认路径
            self.cache_path = "D:/blade_inspect"
            self.recipe_storage_path = os.path.join(self.cache_path, "task")
            os.makedirs(self.recipe_storage_path, exist_ok=True)
    
    def load_cache_path_from_config(self):
        """从config.yaml配置文件中读取cache_path"""
        try:
            config_path = os.path.join("configs", "config.yaml")
            if not os.path.exists(config_path):
                print(f"配置文件不存在: {config_path}，使用默认路径")
                return "D:/blade_inspect"
            
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            
            cache_path = config_data.get('INSPECT', {}).get('cache_path', 'D:/blade_inspect')
            print(f"从配置文件读取cache_path: {cache_path}")
            return cache_path
            
        except Exception as e:
            print(f"读取配置文件失败: {e}，使用默认路径")
            return "D:/blade_inspect"
    
    def scan_task_folders(self):
        """扫描task文件夹下的所有子文件夹，查找task_info.json文件"""
        recipes = []
        
        if not os.path.exists(self.recipe_storage_path):
            print(f"配方存储路径不存在: {self.recipe_storage_path}")
            return recipes
        
        try:
            # 遍历task文件夹下的所有子文件夹
            for folder_name in os.listdir(self.recipe_storage_path):
                folder_path = os.path.join(self.recipe_storage_path, folder_name)
                
                # 只处理文件夹
                if not os.path.isdir(folder_path):
                    continue
                
                # 查找task_info.json文件
                task_info_path = os.path.join(folder_path, "task_info.json")
                if os.path.exists(task_info_path):
                    try:
                        # 读取task_info.json文件
                        with open(task_info_path, 'r', encoding='utf-8') as f:
                            task_info_data = json.load(f)
                        
                        # 检查是否已经在内存中存在
                        found_in_memory = any(recipe.get("folder_name") == folder_name for recipe in self.recipe_data_list)
                        
                        if not found_in_memory:
                            # 解析task_info.json数据并映射到表格字段
                            recipe_info = self.parse_task_info_to_recipe(task_info_data, folder_name, task_info_path)
                            recipes.append(recipe_info)
                            
                    except Exception as e:
                        print(f"读取task_info.json文件失败 {task_info_path}: {e}")
                        
        except Exception as e:
            print(f"扫描task文件夹失败: {e}")
        
        return recipes
    
    def parse_task_info_to_recipe(self, task_info_data, folder_name, file_path):
        """解析task_info.json数据并映射到配方表格字段"""
        try:
            # 获取文件修改时间作为默认创建时间
            stat = os.stat(file_path)
            default_create_time = datetime.fromtimestamp(stat.st_mtime).strftime("%Y-%m-%d %H:%M")
            
            # 字段映射：task_info.json字段 -> 表格显示字段
            recipe_info = {
                "name": task_info_data.get("MOTaskName", folder_name),  # 任务名
                "product_name": task_info_data.get("ProductName", "未知产品"),  # 产品名称
                "lot_sn": task_info_data.get("LotSN", "未知批号"),  # 批号
                "mo_name": task_info_data.get("MOName", "未知工单"),  # 工单名称
                "product_line_name": task_info_data.get("ProductLineName", "未知产线"),  # 产线名称
                "order_name": task_info_data.get("OrderName", "未知订单"),  # 订单名称
                "specification_name": task_info_data.get("SpecificationName", "未知工序"),  # 工序名称
                "task_qty": str(task_info_data.get("TaskQty", 0)),  # 任务数量
                "create_time": task_info_data.get("create_time", default_create_time),  # 上传时间
                "operator": task_info_data.get("operator", "未知用户"),  # 上传人
                "folder_name": folder_name,  # 保存文件夹名称用于标识
                "file_path": file_path,  # 保存文件路径
                "task_info_data": task_info_data  # 保存原始数据
            }
            
            print(f"解析task_info.json: {folder_name} -> {recipe_info['name']}")
            return recipe_info
            
        except Exception as e:
            print(f"解析task_info.json失败 {file_path}: {e}")
            # 返回默认数据
            return {
                "name": folder_name,
                "product_name": "解析失败",
                "lot_sn": "解析失败",
                "mo_name": "解析失败",
                "product_line_name": "解析失败", 
                "order_name": "解析失败",
                "specification_name": "解析失败",
                "task_qty": "0",
                "create_time": "解析失败",
                "operator": "系统",
                "folder_name": folder_name,
                "file_path": file_path
            }
    
    def load_recipes(self):
        """加载所有配方数据"""
        try:
            # 扫描task文件夹获取配方
            scanned_recipes = self.scan_task_folders()
            
            # 合并内存中的配方数据和扫描到的配方
            all_recipes = []
            all_recipes.extend(self.recipe_data_list)
            all_recipes.extend(scanned_recipes)
            
            # 如果没有找到任何配方，显示提示信息
            if not all_recipes:
                all_recipes = [
                    {
                        "name": "未找到配方",
                        "product_name": "请检查task文件夹",
                        "lot_sn": "无数据",
                        "mo_name": "无数据", 
                        "product_line_name": "无数据",
                        "order_name": "无数据",
                        "specification_name": "无数据",
                        "task_qty": "0",
                        "create_time": "无数据",
                        "operator": "系统"
                    }
                ]
            
            print(f"加载配方完成，共找到 {len(all_recipes)} 个配方")
            return all_recipes
            
        except Exception as e:
            print(f"加载配方失败: {e}")
            return []
    
    def get_recipe_detail(self, recipe_name):
        """获取配方详细信息"""
        try:
            # 在已加载的配方中查找
            for recipe in self.recipe_data_list:
                if recipe.get("name") == recipe_name or recipe.get("folder_name") == recipe_name:
                    return recipe.get("task_info_data", {})
            
            # 如果没找到，尝试重新扫描
            recipes = self.scan_task_folders()
            for recipe in recipes:
                if recipe.get("name") == recipe_name or recipe.get("folder_name") == recipe_name:
                    return recipe.get("task_info_data", {})
            
            return {}
            
        except Exception as e:
            print(f"获取配方详情失败: {e}")
            return {}
    
    def refresh_recipes(self):
        """刷新配方列表"""
        try:
            return self.load_recipes()
        except Exception as e:
            print(f"刷新配方失败: {e}")
            return []

    def add_new_recipe(self):
        """新增配方 - 弹出图号输入对话框"""
        try:
            # 弹出输入对话框
            part_number, ok = QInputDialog.getText(
                self.main_window,
                "添加新配方",
                "请输入图号 (例如: S21.08.5330):"
            )

            if not ok or not part_number.strip():
                return False

            part_number = part_number.strip()

            # 验证图号格式 (简单验证)
            if not re.match(r'^S\d{2}\.\d{2}\.\d{4}$', part_number):
                QMessageBox.warning(
                    self.main_window,
                    "格式错误",
                    f"图号格式不正确！\n\n请输入格式如: S21.08.5330\n当前输入: {part_number}"
                )
                return False

            # 检查是否已存在
            task_folder_name = f"任务-{part_number}"
            target_folder = os.path.join(self.recipe_storage_path, task_folder_name)

            if os.path.exists(target_folder):
                QMessageBox.warning(
                    self.main_window,
                    "配方已存在",
                    f"配方 '{part_number}' 已存在！\n\n文件夹: {task_folder_name}"
                )
                return False

            # 执行配方创建
            success = self.create_recipe_from_template(part_number)

            if success:
                QMessageBox.information(
                    self.main_window,
                    "创建成功",
                    f"✅ 配方 '{part_number}' 创建成功！\n\n文件夹: {task_folder_name}"
                )
                return True
            else:
                return False

        except Exception as e:
            QMessageBox.critical(
                self.main_window,
                "创建失败",
                f"❌ 创建配方失败: {e}"
            )
            print(f"新增配方失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def create_recipe_from_template(self, part_number):
        """从模板创建新配方"""
        try:
            # 源文件路径
            recipes_dir = "recipes"
            task_info_source = os.path.join(recipes_dir, "task_info.json")
            program_source = os.path.join(recipes_dir, "S88.88.8888.json")

            # 检查源文件是否存在
            if not os.path.exists(task_info_source):
                QMessageBox.warning(
                    self.main_window,
                    "模板文件缺失",
                    f"找不到模板文件: {task_info_source}"
                )
                return False

            if not os.path.exists(program_source):
                QMessageBox.warning(
                    self.main_window,
                    "模板文件缺失",
                    f"找不到模板文件: {program_source}"
                )
                return False

            # 目标文件夹
            task_folder_name = f"任务-{part_number}"
            target_folder = os.path.join(self.recipe_storage_path, task_folder_name)

            # 创建目标文件夹
            os.makedirs(target_folder, exist_ok=True)
            print(f"创建文件夹: {target_folder}")

            # 目标文件路径
            task_info_target = os.path.join(target_folder, "task_info.json")
            program_target = os.path.join(target_folder, f"{part_number}.json")

            # 拷贝并修改task_info.json
            success = self.copy_and_modify_task_info(task_info_source, task_info_target, part_number)
            if not success:
                return False

            # 拷贝程序文件
            success = self.copy_program_file(program_source, program_target)
            if not success:
                return False

            print(f"配方创建完成: {part_number}")
            return True

        except Exception as e:
            print(f"从模板创建配方失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def copy_and_modify_task_info(self, source_path, target_path, new_part_number):
        """拷贝并修改task_info.json文件"""
        try:
            # 读取源文件
            with open(source_path, 'r', encoding='utf-8') as f:
                task_info_data = json.load(f)

            # 替换所有涉及S21.08.6230的内容为新的图号
            old_part_number = "S21.08.6230"
            task_info_json_str = json.dumps(task_info_data, ensure_ascii=False, indent=2)

            # 执行替换
            modified_json_str = task_info_json_str.replace(old_part_number, new_part_number)

            # 解析修改后的JSON
            modified_data = json.loads(modified_json_str)

            # 更新时间戳和操作员信息
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M")
            modified_data["create_time"] = current_time
            modified_data["operator"] = "用户"

            # 写入目标文件
            with open(target_path, 'w', encoding='utf-8') as f:
                json.dump(modified_data, f, ensure_ascii=False, indent=2)

            print(f"task_info.json 已拷贝并修改: {source_path} -> {target_path}")
            print(f"替换内容: {old_part_number} -> {new_part_number}")
            return True

        except Exception as e:
            print(f"拷贝并修改task_info.json失败: {e}")
            QMessageBox.warning(
                self.main_window,
                "文件处理失败",
                f"处理task_info.json失败: {e}"
            )
            return False

    def copy_program_file(self, source_path, target_path):
        """拷贝程序文件"""
        try:
            # 直接拷贝文件
            shutil.copy2(source_path, target_path)
            print(f"程序文件已拷贝: {source_path} -> {target_path}")
            return True

        except Exception as e:
            print(f"拷贝程序文件失败: {e}")
            QMessageBox.warning(
                self.main_window,
                "文件拷贝失败",
                f"拷贝程序文件失败: {e}"
            )
            return False

    def update_recipe_table(self, recipes):
        """更新配方表格显示"""
        try:
            if not self.main_window or not hasattr(self.main_window, 'recipe_table'):
                return

            recipe_table = self.main_window.recipe_table
            if not recipe_table:
                return

            if recipe_table.columnCount() < 10:
                return

            recipe_table.setRowCount(len(recipes))

            for row, recipe in enumerate(recipes):
                recipe_table.setRowHeight(row, 80)

                # 检查是否为新的task_info格式数据（包含product_name字段）
                if 'product_name' in recipe:
                    # 新的task_info格式数据，直接使用解析后的字段
                    data = [
                        recipe.get("name", "未知任务"),  # 任务名
                        recipe.get("product_name", "未知产品"),  # 产品名称
                        recipe.get("lot_sn", "未知批号"),  # 批号
                        recipe.get("mo_name", "未知工单"),  # 工单名称
                        recipe.get("product_line_name", "未知产线"),  # 产线名称
                        recipe.get("order_name", "未知订单"),  # 订单名称
                        recipe.get("specification_name", "未知工序"),  # 工序名称
                        recipe.get("task_qty", "0"),  # 任务数量
                        recipe.get("create_time", "未知时间"),  # 上传时间
                        recipe.get("operator", "未知用户")  # 上传人
                    ]
                else:
                    # 传统配方格式，填充默认值
                    data = [
                        recipe.get("name", "未知配方"),  # 任务名
                        recipe.get("name", "未知配方"),  # 产品名称
                        "传统配方",  # 批号
                        "无",  # 工单名称
                        recipe.get("ip", "本地"),  # 产线名称
                        "无",  # 订单名称
                        "无",  # 工序名称
                        recipe.get("count", "0"),  # 任务数量
                        recipe.get("create_time", "未知时间"),  # 上传时间
                        recipe.get("operator", "未知用户")  # 上传人
                    ]

                for col, value in enumerate(data):
                    if col < 10:
                        item = QTableWidgetItem(str(value))
                        item.setTextAlignment(Qt.AlignCenter)
                        item.setFlags(item.flags() & ~Qt.ItemIsEditable)
                        recipe_table.setItem(row, col, item)

                self.add_recipe_action_buttons(row, recipe["name"])

            recipe_table.viewport().update()
            recipe_table.update()

        except Exception as e:
            print(f"更新配方表格失败: {e}")
            import traceback
            traceback.print_exc()

    def add_recipe_action_buttons(self, row, recipe_name):
        """添加配方操作按钮"""
        try:
            if not self.main_window or not hasattr(self.main_window, 'recipe_table'):
                return

            recipe_table = self.main_window.recipe_table

            # 创建按钮容器
            widget = QWidget()
            layout = QHBoxLayout(widget)
            layout.setContentsMargins(5, 5, 5, 5)
            layout.setSpacing(5)

            # 详情按钮
            detail_btn = QPushButton("详情")
            detail_btn.setFixedSize(50, 30)
            detail_btn.setStyleSheet("""
                QPushButton {
                    background-color: #3498db;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    font-size: 10px;
                }
                QPushButton:hover {
                    background-color: #2980b9;
                }
            """)

            # 编辑按钮
            edit_btn = QPushButton("编辑")
            edit_btn.setFixedSize(50, 30)
            edit_btn.setStyleSheet("""
                QPushButton {
                    background-color: #f39c12;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    font-size: 10px;
                }
                QPushButton:hover {
                    background-color: #e67e22;
                }
            """)

            # 删除按钮
            delete_btn = QPushButton("删除")
            delete_btn.setFixedSize(50, 30)
            delete_btn.setStyleSheet("""
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    font-size: 10px;
                }
                QPushButton:hover {
                    background-color: #c0392b;
                }
            """)

            # 连接信号
            detail_btn.clicked.connect(lambda: self.show_recipe_detail(recipe_name))
            edit_btn.clicked.connect(lambda: self.edit_recipe(recipe_name))
            delete_btn.clicked.connect(lambda: self.delete_recipe(recipe_name))

            layout.addWidget(detail_btn)
            layout.addWidget(edit_btn)
            layout.addWidget(delete_btn)

            # 设置到表格的最后一列（索引10）
            recipe_table.setCellWidget(row, 10, widget)

        except Exception as e:
            print(f"添加操作按钮失败: {e}")
            import traceback
            traceback.print_exc()

    def show_recipe_detail(self, recipe_name):
        """显示配方详情"""
        try:
            # 获取配方详情
            recipe_data = self.get_recipe_detail(recipe_name)

            if not recipe_data:
                QMessageBox.warning(self.main_window, "错误", f"无法找到配方: {recipe_name}")
                return

            # 格式化显示配方详情
            detail_text = f"配方名称: {recipe_name}\n\n"
            detail_text += "=== 任务信息 ===\n"
            detail_text += f"产线名称: {recipe_data.get('ProductLineName', '未知')}\n"
            detail_text += f"任务名称: {recipe_data.get('MOTaskName', '未知')}\n"
            detail_text += f"工单名称: {recipe_data.get('MOName', '未知')}\n"
            detail_text += f"订单名称: {recipe_data.get('OrderName', '未知')}\n"
            detail_text += f"产品名称: {recipe_data.get('ProductName', '未知')}\n"
            detail_text += f"批号: {recipe_data.get('LotSN', '未知')}\n"
            detail_text += f"工序名称: {recipe_data.get('SpecificationName', '未知')}\n"
            detail_text += f"任务数量: {recipe_data.get('TaskQty', '未知')}\n"
            detail_text += f"站点代码: {recipe_data.get('SiteCode', '未知')}\n"
            detail_text += f"工单类型: {recipe_data.get('MOType', '未知')}\n\n"
            detail_text += "=== 系统信息 ===\n"
            detail_text += f"操作员: {recipe_data.get('operator', '未知')}\n"
            detail_text += f"创建时间: {recipe_data.get('create_time', '未知')}"

            QMessageBox.information(self.main_window, "配方详情", detail_text)

        except Exception as e:
            QMessageBox.warning(self.main_window, "错误", f"无法读取配方详情: {e}")

    def edit_recipe(self, recipe_name):
        """编辑配方"""
        QMessageBox.information(self.main_window, "编辑配方", f"打开配方编辑器编辑: {recipe_name}")

    def delete_recipe(self, recipe_name):
        """删除配方"""
        try:
            reply = QMessageBox.question(
                self.main_window,
                "确认删除",
                f"确定要删除配方 '{recipe_name}' 吗？\n\n此操作将删除整个配方文件夹及其内容，无法恢复！",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # 查找对应的文件夹
                for folder_name in os.listdir(self.recipe_storage_path):
                    folder_path = os.path.join(self.recipe_storage_path, folder_name)
                    if os.path.isdir(folder_path):
                        task_info_path = os.path.join(folder_path, "task_info.json")
                        if os.path.exists(task_info_path):
                            try:
                                with open(task_info_path, 'r', encoding='utf-8') as f:
                                    task_info_data = json.load(f)

                                # 检查是否是要删除的配方
                                if (task_info_data.get("MOTaskName") == recipe_name or
                                    folder_name.endswith(recipe_name.replace("任务-", ""))):

                                    # 删除整个文件夹
                                    shutil.rmtree(folder_path)
                                    print(f"已删除配方文件夹: {folder_path}")

                                    QMessageBox.information(
                                        self.main_window,
                                        "删除成功",
                                        f"配方 '{recipe_name}' 已成功删除！"
                                    )

                                    # 刷新表格
                                    if hasattr(self.main_window, 'refresh_recipe_table'):
                                        self.main_window.refresh_recipe_table()

                                    return

                            except Exception as e:
                                print(f"检查配方文件失败: {e}")
                                continue

                QMessageBox.warning(
                    self.main_window,
                    "删除失败",
                    f"未找到配方 '{recipe_name}' 对应的文件夹"
                )

        except Exception as e:
            QMessageBox.warning(self.main_window, "删除失败", f"删除配方失败: {e}")
            print(f"删除配方失败: {e}")
            import traceback
            traceback.print_exc()
