#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Blade Insight 系统对话框
包含操作人员登录、硬件配置等对话框
"""
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, QFormLayout,
    QLabel, QLineEdit, QPushButton, QComboBox, QSpinBox, QDoubleSpinBox,
    QGroupBox, QTabWidget, QWidget, QDialogButtonBox, QMessageBox,
    QCheckBox, QSlider
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap


class OperatorLoginDialog(QDialog):
    """操作人员登录对话框"""

    login_success = pyqtSignal(str, str)  # 角色, 用户名

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("操作人员登录")
        self.setFixedSize(400, 300)
        self.setModal(True)

        self.setup_ui()
        self.setup_styles()

    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)

        # 标题
        title_label = QLabel("Blade Insight 系统登录")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setObjectName("title_label")
        layout.addWidget(title_label)

        # 登录表单
        form_widget = QWidget()
        form_layout = QFormLayout(form_widget)

        # 角色选择
        self.role_combo = QComboBox()
        self.role_combo.addItems(["操作员", "调试员", "技术员"])
        self.role_combo.setObjectName("role_combo")
        form_layout.addRow("登录角色:", self.role_combo)

        # 用户名
        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText("请输入用户名")
        self.username_edit.setObjectName("username_edit")
        form_layout.addRow("用户名:", self.username_edit)

        # 密码
        self.password_edit = QLineEdit()
        self.password_edit.setPlaceholderText("请输入密码")
        self.password_edit.setEchoMode(QLineEdit.Password)
        self.password_edit.setObjectName("password_edit")
        form_layout.addRow("密码:", self.password_edit)

        layout.addWidget(form_widget)

        # 按钮
        button_layout = QHBoxLayout()

        self.login_btn = QPushButton("登录")
        self.login_btn.setObjectName("login_btn")
        self.login_btn.clicked.connect(self.handle_login)

        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.setObjectName("cancel_btn")
        self.cancel_btn.clicked.connect(self.reject)

        button_layout.addStretch()
        button_layout.addWidget(self.login_btn)
        button_layout.addWidget(self.cancel_btn)

        layout.addLayout(button_layout)

        # 回车键登录
        self.password_edit.returnPressed.connect(self.handle_login)

    def setup_styles(self):
        """设置样式"""
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            QLabel#title_label {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 20px;
            }
            QComboBox#role_combo, QLineEdit#username_edit, QLineEdit#password_edit {
                padding: 8px;
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                font-size: 12px;
                min-height: 20px;
            }
            QComboBox#role_combo:focus, QLineEdit#username_edit:focus, QLineEdit#password_edit:focus {
                border-color: #3498db;
            }
            QPushButton#login_btn {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton#login_btn:hover {
                background-color: #2980b9;
            }
            QPushButton#cancel_btn {
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton#cancel_btn:hover {
                background-color: #7f8c8d;
            }
        """)

    def handle_login(self):
        """处理登录"""
        role = self.role_combo.currentText()
        username = self.username_edit.text().strip()
        password = self.password_edit.text().strip()

        if not username:
            QMessageBox.warning(self, "登录失败", "请输入用户名")
            return

        if not password:
            QMessageBox.warning(self, "登录失败", "请输入密码")
            return

        # 简单的密码验证（实际项目中应该连接数据库）
        valid_credentials = {
            "操作员": {"admin": "123456", "operator": "123456"},
            "调试员": {"debug": "123456", "test": "123456"},
            "技术员": {"tech": "123456", "engineer": "123456"}
        }

        if role in valid_credentials and username in valid_credentials[role]:
            if valid_credentials[role][username] == password:
                self.login_success.emit(role, username)
                self.accept()
                return

        QMessageBox.warning(self, "登录失败", "用户名或密码错误")


class HardwareConfigDialog(QDialog):
    """硬件配置对话框"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("硬件配置")
        self.setFixedSize(700, 600)
        self.setModal(True)

        self.setup_ui()
        self.setup_styles()
        self.load_config()

    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)

        # 标签页
        self.tab_widget = QTabWidget()

        # 屏蔽设置标签页
        self.shield_tab = self.create_shield_tab()
        self.tab_widget.addTab(self.shield_tab, "屏蔽设置")

        # 机器人设置标签页
        self.robot_tab = self.create_robot_tab()
        self.tab_widget.addTab(self.robot_tab, "机器人设置")

        # 相机设置标签页
        self.camera_tab = self.create_camera_tab()
        self.tab_widget.addTab(self.camera_tab, "相机设置")

        # 传感器设置标签页
        self.sensor_tab = self.create_sensor_tab()
        self.tab_widget.addTab(self.sensor_tab, "传感器设置")

        layout.addWidget(self.tab_widget)

        # 按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel | QDialogButtonBox.Apply
        )
        button_box.accepted.connect(self.save_and_close)
        button_box.rejected.connect(self.reject)
        button_box.button(QDialogButtonBox.Apply).clicked.connect(self.apply_config)

        layout.addWidget(button_box)

    def create_shield_tab(self):
        """创建屏蔽设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 设备屏蔽控制组
        shield_group = QGroupBox("设备屏蔽控制")
        shield_layout = QFormLayout(shield_group)

        # JQR设备
        jqr_layout = QHBoxLayout()
        self.jqr_enabled = QCheckBox("启用机器人")
        self.jqr_enabled.setChecked(True)
        self.jqr_connect_btn = QPushButton("连接")
        self.jqr_disconnect_btn = QPushButton("断开")
        self.jqr_status_label = QPushButton("已连接")
        self.jqr_status_label.setStyleSheet("""
            QPushButton {
                color: green;
                font-weight: bold;
                border: 1px solid green;
                border-radius: 4px;
                padding: 4px 8px;
                background-color: #f0fff0;
            }
            QPushButton:hover {
                background-color: #e0ffe0;
            }
        """)
        self.jqr_status_label.clicked.connect(self.open_connection_monitor)

        jqr_layout.addWidget(self.jqr_enabled)
        jqr_layout.addWidget(self.jqr_connect_btn)
        jqr_layout.addWidget(self.jqr_disconnect_btn)
        jqr_layout.addWidget(self.jqr_status_label)
        jqr_layout.addStretch()
        shield_layout.addRow("机器人设备:", jqr_layout)

        # PLC设备
        plc_layout = QHBoxLayout()
        self.plc_enabled = QCheckBox("启用PLC")
        self.plc_enabled.setChecked(True)
        self.plc_connect_btn = QPushButton("连接")
        self.plc_disconnect_btn = QPushButton("断开")
        self.plc_status_label = QPushButton("已连接")
        self.plc_status_label.setStyleSheet("""
            QPushButton {
                color: green;
                font-weight: bold;
                border: 1px solid green;
                border-radius: 4px;
                padding: 4px 8px;
                background-color: #f0fff0;
            }
            QPushButton:hover {
                background-color: #e0ffe0;
            }
        """)
        self.plc_status_label.clicked.connect(self.open_connection_monitor)

        plc_layout.addWidget(self.plc_enabled)
        plc_layout.addWidget(self.plc_connect_btn)
        plc_layout.addWidget(self.plc_disconnect_btn)
        plc_layout.addWidget(self.plc_status_label)
        plc_layout.addStretch()
        shield_layout.addRow("PLC设备:", plc_layout)

        # 2D识别设备
        vision_2d_layout = QHBoxLayout()
        self.vision_2d_enabled = QCheckBox("启用2D识别")
        self.vision_2d_enabled.setChecked(True)
        self.vision_2d_connect_btn = QPushButton("连接")
        self.vision_2d_disconnect_btn = QPushButton("断开")
        self.vision_2d_status_label = QPushButton("已连接")
        self.vision_2d_status_label.setStyleSheet("""
            QPushButton {
                color: green;
                font-weight: bold;
                border: 1px solid green;
                border-radius: 4px;
                padding: 4px 8px;
                background-color: #f0fff0;
            }
            QPushButton:hover {
                background-color: #e0ffe0;
            }
        """)
        self.vision_2d_status_label.clicked.connect(self.open_connection_monitor)

        vision_2d_layout.addWidget(self.vision_2d_enabled)
        vision_2d_layout.addWidget(self.vision_2d_connect_btn)
        vision_2d_layout.addWidget(self.vision_2d_disconnect_btn)
        vision_2d_layout.addWidget(self.vision_2d_status_label)
        vision_2d_layout.addStretch()
        shield_layout.addRow("2D识别设备:", vision_2d_layout)

        # 3D检测设备
        vision_3d_layout = QHBoxLayout()
        self.vision_3d_enabled = QCheckBox("启用3D检测")
        self.vision_3d_enabled.setChecked(True)
        self.vision_3d_connect_btn = QPushButton("连接")
        self.vision_3d_disconnect_btn = QPushButton("断开")
        self.vision_3d_status_label = QPushButton("已连接")
        self.vision_3d_status_label.setStyleSheet("""
            QPushButton {
                color: green;
                font-weight: bold;
                border: 1px solid green;
                border-radius: 4px;
                padding: 4px 8px;
                background-color: #f0fff0;
            }
            QPushButton:hover {
                background-color: #e0ffe0;
            }
        """)
        self.vision_3d_status_label.clicked.connect(self.open_connection_monitor)

        vision_3d_layout.addWidget(self.vision_3d_enabled)
        vision_3d_layout.addWidget(self.vision_3d_connect_btn)
        vision_3d_layout.addWidget(self.vision_3d_disconnect_btn)
        vision_3d_layout.addWidget(self.vision_3d_status_label)
        vision_3d_layout.addStretch()
        shield_layout.addRow("3D检测设备:", vision_3d_layout)

        layout.addWidget(shield_group)

        # 连接按钮事件
        self.jqr_connect_btn.clicked.connect(lambda: self.connect_device("机器人", self.jqr_status_label))
        self.jqr_disconnect_btn.clicked.connect(lambda: self.disconnect_device("机器人", self.jqr_status_label))
        self.plc_connect_btn.clicked.connect(lambda: self.connect_device("PLC", self.plc_status_label))
        self.plc_disconnect_btn.clicked.connect(lambda: self.disconnect_device("PLC", self.plc_status_label))
        self.vision_2d_connect_btn.clicked.connect(lambda: self.connect_device("2D识别", self.vision_2d_status_label))
        self.vision_2d_disconnect_btn.clicked.connect(
            lambda: self.disconnect_device("2D识别", self.vision_2d_status_label))
        self.vision_3d_connect_btn.clicked.connect(lambda: self.connect_device("3D检测", self.vision_3d_status_label))
        self.vision_3d_disconnect_btn.clicked.connect(
            lambda: self.disconnect_device("3D检测", self.vision_3d_status_label))

        return widget

    def create_robot_tab(self):
        """创建机器人设置标签页"""
        widget = QWidget()
        layout = QFormLayout(widget)

        # 连接设置组
        connection_group = QGroupBox("连接设置")
        connection_layout = QFormLayout(connection_group)

        self.robot_ip_edit = QLineEdit("*************")
        connection_layout.addRow("IP地址:", self.robot_ip_edit)

        self.robot_port_spin = QSpinBox()
        self.robot_port_spin.setRange(1, 65535)
        self.robot_port_spin.setValue(502)
        connection_layout.addRow("端口:", self.robot_port_spin)

        # 运动参数组
        motion_group = QGroupBox("运动参数")
        motion_layout = QFormLayout(motion_group)

        self.robot_speed_spin = QSpinBox()
        self.robot_speed_spin.setRange(1, 100)
        self.robot_speed_spin.setValue(50)
        self.robot_speed_spin.setSuffix(" %")
        motion_layout.addRow("运动速度:", self.robot_speed_spin)

        self.robot_accel_spin = QSpinBox()
        self.robot_accel_spin.setRange(1, 100)
        self.robot_accel_spin.setValue(30)
        self.robot_accel_spin.setSuffix(" %")
        motion_layout.addRow("加速度:", self.robot_accel_spin)

        self.robot_precision_spin = QDoubleSpinBox()
        self.robot_precision_spin.setRange(0.01, 10.0)
        self.robot_precision_spin.setValue(0.1)
        self.robot_precision_spin.setSuffix(" mm")
        motion_layout.addRow("定位精度:", self.robot_precision_spin)

        layout.addWidget(connection_group)
        layout.addWidget(motion_group)

        return widget

    def create_camera_tab(self):
        """创建相机设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 2D识别功能块
        vision_2d_group = QGroupBox("2D识别")
        vision_2d_layout = QFormLayout(vision_2d_group)

        self.vision_2d_exposure_spin = QSpinBox()
        self.vision_2d_exposure_spin.setRange(1, 10000)
        self.vision_2d_exposure_spin.setValue(100)
        self.vision_2d_exposure_spin.setSuffix(" μs")
        vision_2d_layout.addRow("曝光时间:", self.vision_2d_exposure_spin)

        self.vision_2d_gain_spin = QSpinBox()
        self.vision_2d_gain_spin.setRange(0, 100)
        self.vision_2d_gain_spin.setValue(50)
        self.vision_2d_gain_spin.setSuffix(" %")
        vision_2d_layout.addRow("增益:", self.vision_2d_gain_spin)

        self.vision_2d_light_spin = QSpinBox()
        self.vision_2d_light_spin.setRange(0, 255)
        self.vision_2d_light_spin.setValue(128)
        vision_2d_layout.addRow("光源亮度:", self.vision_2d_light_spin)

        # 3D识别功能块
        vision_3d_group = QGroupBox("3D识别")
        vision_3d_layout = QFormLayout(vision_3d_group)

        self.vision_3d_exposure_spin = QSpinBox()
        self.vision_3d_exposure_spin.setRange(1, 10000)
        self.vision_3d_exposure_spin.setValue(200)
        self.vision_3d_exposure_spin.setSuffix(" μs")
        vision_3d_layout.addRow("曝光时间:", self.vision_3d_exposure_spin)

        self.vision_3d_gain_spin = QSpinBox()
        self.vision_3d_gain_spin.setRange(0, 100)
        self.vision_3d_gain_spin.setValue(60)
        self.vision_3d_gain_spin.setSuffix(" %")
        vision_3d_layout.addRow("增益:", self.vision_3d_gain_spin)

        self.vision_3d_light_spin = QSpinBox()
        self.vision_3d_light_spin.setRange(0, 255)
        self.vision_3d_light_spin.setValue(150)
        vision_3d_layout.addRow("光源亮度:", self.vision_3d_light_spin)

        layout.addWidget(vision_2d_group)
        layout.addWidget(vision_3d_group)

        return widget

    def create_sensor_tab(self):
        """创建传感器设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 上料传送带
        feed_conveyor_group = QGroupBox("上料传送带")
        feed_conveyor_layout = QFormLayout(feed_conveyor_group)

        self.feed_conveyor_ip_edit = QLineEdit("*************")
        feed_conveyor_layout.addRow("IP地址:", self.feed_conveyor_ip_edit)

        self.feed_conveyor_port_spin = QSpinBox()
        self.feed_conveyor_port_spin.setRange(1, 65535)
        self.feed_conveyor_port_spin.setValue(502)
        feed_conveyor_layout.addRow("端口:", self.feed_conveyor_port_spin)

        self.feed_conveyor_speed_spin = QSpinBox()
        self.feed_conveyor_speed_spin.setRange(1, 100)
        self.feed_conveyor_speed_spin.setValue(50)
        self.feed_conveyor_speed_spin.setSuffix(" %")
        feed_conveyor_layout.addRow("传送带速度:", self.feed_conveyor_speed_spin)

        self.feed_conveyor_enabled = QCheckBox("启用")
        self.feed_conveyor_enabled.setChecked(True)
        feed_conveyor_layout.addRow("状态:", self.feed_conveyor_enabled)

        # 下料传送带
        discharge_conveyor_group = QGroupBox("下料传送带")
        discharge_conveyor_layout = QFormLayout(discharge_conveyor_group)

        self.discharge_conveyor_ip_edit = QLineEdit("*************")
        discharge_conveyor_layout.addRow("IP地址:", self.discharge_conveyor_ip_edit)

        self.discharge_conveyor_port_spin = QSpinBox()
        self.discharge_conveyor_port_spin.setRange(1, 65535)
        self.discharge_conveyor_port_spin.setValue(502)
        discharge_conveyor_layout.addRow("端口:", self.discharge_conveyor_port_spin)

        self.discharge_conveyor_speed_spin = QSpinBox()
        self.discharge_conveyor_speed_spin.setRange(1, 100)
        self.discharge_conveyor_speed_spin.setValue(45)
        self.discharge_conveyor_speed_spin.setSuffix(" %")
        discharge_conveyor_layout.addRow("传送带速度:", self.discharge_conveyor_speed_spin)

        self.discharge_conveyor_enabled = QCheckBox("启用")
        self.discharge_conveyor_enabled.setChecked(True)
        discharge_conveyor_layout.addRow("状态:", self.discharge_conveyor_enabled)

        layout.addWidget(feed_conveyor_group)
        layout.addWidget(discharge_conveyor_group)

        return widget

    def connect_device(self, device_name, status_label):
        """连接设备"""
        try:
            print(f"正在连接{device_name}设备...")
            # 这里应该实现实际的设备连接逻辑
            # 模拟连接成功
            status_label.setText("已连接")
            status_label.setStyleSheet("color: green; font-weight: bold;")
            QMessageBox.information(self, "连接成功", f"{device_name}设备连接成功！")
        except Exception as e:
            status_label.setText("连接失败")
            status_label.setStyleSheet("color: red; font-weight: bold;")
            QMessageBox.warning(self, "连接失败", f"{device_name}设备连接失败：{str(e)}")

    def disconnect_device(self, device_name, status_label):
        """断开设备"""
        try:
            print(f"正在断开{device_name}设备...")
            # 这里应该实现实际的设备断开逻辑
            # 模拟断开成功
            status_label.setText("已断开")
            status_label.setStyleSheet("""
                QPushButton {
                    color: red;
                    font-weight: bold;
                    border: 1px solid red;
                    border-radius: 4px;
                    padding: 4px 8px;
                    background-color: #fff0f0;
                }
                QPushButton:hover {
                    background-color: #ffe0e0;
                }
            """)
            QMessageBox.information(self, "断开成功", f"{device_name}设备已断开！")
        except Exception as e:
            QMessageBox.warning(self, "断开失败", f"{device_name}设备断开失败：{str(e)}")

    def open_connection_monitor(self):
        """打开连接监控对话框"""
        try:
            monitor_dialog = ConnectionMonitorDialog(self)
            monitor_dialog.exec_()
        except Exception as e:
            QMessageBox.warning(self, "错误", f"打开连接监控失败：{str(e)}")

    def setup_styles(self):
        """设置样式"""
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            QTabWidget::pane {
                border: 1px solid #bdc3c7;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                color: #2c3e50;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 1px solid white;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox {
                padding: 6px;
                border: 1px solid #bdc3c7;
                border-radius: 3px;
                font-size: 11px;
            }
            QLineEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {
                border-color: #3498db;
            }
        """)

    def load_config(self):
        """加载配置"""
        # 这里可以从配置文件或数据库加载配置
        pass

    def save_and_close(self):
        """保存并关闭"""
        self.apply_config()
        self.accept()

    def apply_config(self):
        """应用配置"""
        # 这里可以保存配置到文件或数据库
        QMessageBox.information(self, "配置保存", "硬件配置已保存")


class DynamicTabWidget(QTabWidget):
    """动态标签页组件"""

    tab_closed = pyqtSignal(str)  # 标签页关闭信号
    tab_switched = pyqtSignal(str)  # 标签页切换信号

    def __init__(self, parent=None):
        super().__init__(parent)
        self.tabs = {}  # 存储标签页信息
        self.current_tab = None

        self.setup_ui()
        self.setup_styles()

    def has_tab(self, tab_id: str) -> bool:
        """检查是否已存在给定 tab_id 的 tab"""
        for i in range(self.count()):
            if self.widget(i).objectName() == tab_id:
                return True
        return False

    def add_tab(self, tab_id: str, title: str, widget: QWidget):
        widget.setObjectName(tab_id)
        self.addTab(widget, title)

    def switch_to_tab(self, tab_id: str):
        for i in range(self.count()):
            if self.widget(i).objectName() == tab_id:
                self.setCurrentIndex(i)
                return

    def setup_ui(self):
        """设置UI"""
        self.layout = QHBoxLayout(self)
        self.layout.setSpacing(2)
        self.layout.setContentsMargins(5, 5, 5, 5)

    def setup_styles(self):
        """设置样式"""
        self.setStyleSheet("""
            QWidget {
                background-color: transparent;
            }
            QPushButton {
                background-color: #ecf0f1;
                color: #2c3e50;
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                padding: 6px 12px;
                margin: 2px;
                font-size: 11px;
                font-weight: bold;
                min-height: 20px;
            }
            QPushButton:hover {
                background-color: #d5dbdb;
                border-color: #85929e;
            }
            QPushButton:checked {
                background-color: #3498db;
                color: white;
                border-color: #2980b9;
            }
            QPushButton#close_btn {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 10px;
                padding: 2px;
                margin: 0px;
                font-weight: bold;
                min-width: 18px;
                max-width: 18px;
                min-height: 18px;
                max-height: 18px;
            }
            QPushButton#close_btn:hover {
                background-color: #c0392b;
            }
        """)

    def add_tab(self, tab_id, title, closable=True):
        """添加标签页"""
        if tab_id in self.tabs:
            self.switch_to_tab(tab_id)
            return

        tab_button = QPushButton(title)
        tab_button.setCheckable(True)
        tab_button.setObjectName(f"tab_{tab_id}")
        tab_button.clicked.connect(lambda: self.switch_to_tab(tab_id))

        # 如果可关闭，添加关闭按钮
        if closable:
            close_btn = QPushButton("×")
            close_btn.setFixedSize(20, 20)
            close_btn.setObjectName("close_btn")
            close_btn.clicked.connect(lambda: self.close_tab(tab_id))

            tab_widget = QWidget()
            tab_layout = QHBoxLayout(tab_widget)
            tab_layout.setContentsMargins(0, 0, 0, 0)
            tab_layout.setSpacing(5)
            tab_layout.addWidget(tab_button)
            tab_layout.addWidget(close_btn)

            self.layout.addWidget(tab_widget)
            self.tabs[tab_id] = {"button": tab_button, "widget": tab_widget, "title": title, "closable": closable}
        else:
            self.layout.addWidget(tab_button)
            self.tabs[tab_id] = {"button": tab_button, "widget": tab_button, "title": title, "closable": closable}

        self.switch_to_tab(tab_id)

    def close_tab(self, tab_id):
        """关闭标签页"""
        if tab_id in self.tabs:
            tab_info = self.tabs[tab_id]
            self.layout.removeWidget(tab_info["widget"])
            tab_info["widget"].deleteLater()
            del self.tabs[tab_id]

            self.tab_closed.emit(tab_id)

            # 如果关闭的是当前标签页，切换到其他标签页
            if self.current_tab == tab_id:
                if self.tabs:
                    next_tab = list(self.tabs.keys())[0]
                    self.switch_to_tab(next_tab)
                else:
                    self.current_tab = None

    def switch_to_tab(self, tab_id):
        """切换到指定标签页"""
        if tab_id not in self.tabs:
            return

        # 取消所有标签页的选中状态
        for tid, tab_info in self.tabs.items():
            tab_info["button"].setChecked(tid == tab_id)

        self.current_tab = tab_id
        self.tab_switched.emit(tab_id)

    def get_current_tab(self):
        """获取当前标签页ID"""
        return self.current_tab


class ConnectionMonitorDialog(QDialog):
    """连接监控对话框"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("设备连接状态监控")
        self.setFixedSize(800, 600)
        self.setModal(True)

        # 设备状态数据
        self.device_status = {
            "robot": {"name": "机器人", "status": "已连接", "ip": "*************",
                      "last_update": "2024-01-20 10:30:15"},
            "plc": {"name": "PLC", "status": "已连接", "ip": "************", "last_update": "2024-01-20 10:30:12"},
            "vision_2d_camera": {"name": "2D识别相机", "status": "已连接", "ip": "*************",
                                 "last_update": "2024-01-20 10:30:18"},
            "vision_2d_light": {"name": "2D光源控制器", "status": "已连接", "ip": "*************",
                                "last_update": "2024-01-20 10:30:16"},
            "vision_3d_camera": {"name": "3D检测相机", "status": "已连接", "ip": "*************",
                                 "last_update": "2024-01-20 10:30:14"},
            "vision_3d_light": {"name": "3D光源控制器", "status": "已连接", "ip": "*************",
                                "last_update": "2024-01-20 10:30:13"}
        }

        self.setup_ui()
        self.setup_styles()
        self.setup_timer()

    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)

        # 标题
        title_label = QLabel("设备连接状态实时监控")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title_label)

        # 设备状态网格
        self.create_device_grid(layout)

        # 刷新按钮
        button_layout = QHBoxLayout()

        refresh_btn = QPushButton("手动刷新")
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        refresh_btn.clicked.connect(self.refresh_status)

        auto_refresh_checkbox = QCheckBox("自动刷新 (每5秒)")
        auto_refresh_checkbox.setChecked(True)
        auto_refresh_checkbox.stateChanged.connect(self.toggle_auto_refresh)

        close_btn = QPushButton("关闭")
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        close_btn.clicked.connect(self.accept)

        button_layout.addWidget(refresh_btn)
        button_layout.addWidget(auto_refresh_checkbox)
        button_layout.addStretch()
        button_layout.addWidget(close_btn)

        layout.addLayout(button_layout)

    def create_device_grid(self, parent_layout):
        """创建设备状态网格"""
        # 创建滚动区域
        from PyQt5.QtWidgets import QScrollArea
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)

        grid_layout = QGridLayout(scroll_widget)
        grid_layout.setSpacing(15)

        self.device_widgets = {}

        row = 0
        col = 0
        for device_id, device_info in self.device_status.items():
            device_widget = self.create_device_widget(device_id, device_info)
            grid_layout.addWidget(device_widget, row, col)

            col += 1
            if col >= 2:  # 每行2个设备
                col = 0
                row += 1

        parent_layout.addWidget(scroll_area)

    def create_device_widget(self, device_id, device_info):
        """创建单个设备状态组件"""
        widget = QGroupBox(device_info["name"])
        layout = QVBoxLayout(widget)

        # 状态指示器
        status_layout = QHBoxLayout()
        status_label = QLabel("状态:")
        status_indicator = QLabel(device_info["status"])

        if device_info["status"] == "已连接":
            status_indicator.setStyleSheet("""
                QLabel {
                    color: white;
                    background-color: #27ae60;
                    border-radius: 10px;
                    padding: 5px 10px;
                    font-weight: bold;
                }
            """)
        else:
            status_indicator.setStyleSheet("""
                QLabel {
                    color: white;
                    background-color: #e74c3c;
                    border-radius: 10px;
                    padding: 5px 10px;
                    font-weight: bold;
                }
            """)

        status_layout.addWidget(status_label)
        status_layout.addWidget(status_indicator)
        status_layout.addStretch()
        layout.addLayout(status_layout)

        # IP地址
        ip_layout = QHBoxLayout()
        ip_label = QLabel("IP地址:")
        ip_value = QLabel(device_info["ip"])
        ip_layout.addWidget(ip_label)
        ip_layout.addWidget(ip_value)
        ip_layout.addStretch()
        layout.addLayout(ip_layout)

        # 最后更新时间
        time_layout = QHBoxLayout()
        time_label = QLabel("最后更新:")
        time_value = QLabel(device_info["last_update"])
        time_layout.addWidget(time_label)
        time_layout.addWidget(time_value)
        time_layout.addStretch()
        layout.addLayout(time_layout)

        # 存储组件引用以便更新
        self.device_widgets[device_id] = {
            "widget": widget,
            "status_indicator": status_indicator,
            "time_value": time_value
        }

        return widget

    def setup_styles(self):
        """设置样式"""
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 8px 0 8px;
                color: #2c3e50;
                font-size: 14px;
            }
            QLabel {
                color: #2c3e50;
                font-size: 12px;
            }
        """)

    def setup_timer(self):
        """设置定时器"""
        from PyQt5.QtCore import QTimer
        self.timer = QTimer()
        self.timer.timeout.connect(self.refresh_status)
        self.timer.start(5000)  # 每5秒刷新一次

    def toggle_auto_refresh(self, state):
        """切换自动刷新"""
        if state == 2:  # 选中
            self.timer.start(5000)
        else:
            self.timer.stop()

    def closeEvent(self, event):
        """关闭事件处理"""
        try:
            # 停止定时器
            if hasattr(self, 'timer') and self.timer:
                self.timer.stop()
                print("✅ 已停止连接监控对话框定时器")

            super().closeEvent(event)

        except Exception as e:
            print(f"❌ 关闭连接监控对话框失败: {e}")
            super().closeEvent(event)

    def refresh_status(self):
        """刷新设备状态"""
        import random
        from datetime import datetime

        # 模拟设备状态变化
        for device_id, device_info in self.device_status.items():
            # 随机模拟设备断连（10%概率）
            if random.random() < 0.1:
                device_info["status"] = "已断开"
            else:
                device_info["status"] = "已连接"

            # 更新时间
            device_info["last_update"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # 更新UI
            if device_id in self.device_widgets:
                widget_info = self.device_widgets[device_id]

                # 更新状态指示器
                status_indicator = widget_info["status_indicator"]
                status_indicator.setText(device_info["status"])

                if device_info["status"] == "已连接":
                    status_indicator.setStyleSheet("""
                        QLabel {
                            color: white;
                            background-color: #27ae60;
                            border-radius: 10px;
                            padding: 5px 10px;
                            font-weight: bold;
                        }
                    """)
                else:
                    status_indicator.setStyleSheet("""
                        QLabel {
                            color: white;
                            background-color: #e74c3c;
                            border-radius: 10px;
                            padding: 5px 10px;
                            font-weight: bold;
                        }
                    """)
                    # 如果设备断连，显示提示
                    self.show_disconnect_warning(device_info["name"])

                # 更新时间
                widget_info["time_value"].setText(device_info["last_update"])

    def show_disconnect_warning(self, device_name):
        """显示设备断连警告"""
        # 避免重复弹窗，这里只打印日志
        print(f"⚠️ 警告：{device_name} 设备连接断开！")

        # 在实际应用中，可以发送系统通知或记录到日志文件
        # 也可以在界面上显示警告图标或弹出提示框
