<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1400</width>
    <height>900</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Blade Insight</string>
  </property>
  <property name="styleSheet">
   <string notr="true">
    QMainWindow {
        background-color: #f5f5f5;
    }
    QPushButton {
        border: 1px solid #ccc;
        border-radius: 5px;
        padding: 8px 16px;
        background-color: #ffffff;
        font-size: 12px;
    }
    QPushButton:hover {
        background-color: #e6f3ff;
        border-color: #0078d4;
    }
    QPushButton:pressed {
        background-color: #cce7ff;
    }
   </string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="main_layout" stretch="0,1">
    <property name="spacing">
     <number>0</number>
    </property>
    <property name="leftMargin">
     <number>0</number>
    </property>
    <property name="topMargin">
     <number>0</number>
    </property>
    <property name="rightMargin">
     <number>0</number>
    </property>
    <property name="bottomMargin">
     <number>0</number>
    </property>
    <item>
     <!-- 顶部标题栏 -->
     <widget class="QWidget" name="title_bar">
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>80</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>16777215</width>
        <height>80</height>
       </size>
      </property>
      <property name="styleSheet">
       <string notr="true">
        QWidget#title_bar {
            background-color: #2c3e50;
            border-bottom: 2px solid #34495e;
        }
        QLabel {
            color: white;
            font-size: 18px;
            font-weight: bold;
        }
        QPushButton {
            color: white;
            background-color: #34495e;
            border: 1px solid #4a6741;
            border-radius: 4px;
            padding: 6px 12px;
            font-size: 11px;
        }
        QPushButton:hover {
            background-color: #4a6741;
        }
        QPushButton#connect_status {
            border-radius: 4px;
            min-width: 70px;
            max-width: 70px;
            min-height: 60px;
            max-height: 60px;
        }
        QPushButton#connect_status[connected="true"] {
            background-color: #27ae60;
        }
        QPushButton#connect_status[connected="false"] {
            background-color: #e74c3c;
        }
        QPushButton#alarm_btn[alarm="true"] {
            background-color: #e74c3c;
            animation: blink 1s infinite;
        }
       </string>
      </property>
      <layout class="QHBoxLayout" name="title_layout">
       <property name="leftMargin">
        <number>15</number>
       </property>
       <property name="topMargin">
        <number>10</number>
       </property>
       <property name="rightMargin">
        <number>15</number>
       </property>
       <property name="bottomMargin">
        <number>10</number>
       </property>
       <property name="spacing">
        <number>10</number>
       </property>
       <item>
        <!-- Logo -->
        <widget class="QLabel" name="logo_label">
         <property name="minimumSize">
          <size>
           <width>40</width>
           <height>40</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>40</width>
           <height>40</height>
          </size>
         </property>
         <property name="text">
          <string>LOGO</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
         <property name="styleSheet">
          <string notr="true">
           QLabel {
               border: 1px solid #bdc3c7;
               background-color: #ecf0f1;
               color: #2c3e50;
               font-size: 10px;
           }
          </string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="system_name">
         <property name="text">
          <string>Blade Insight</string>
         </property>
         <property name="styleSheet">
          <string notr="true">
           QLabel {
               margin-left: 15px;
               font-size: 20px;
               font-weight: bold;
           }
          </string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="title_spacer">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QPushButton" name="hardware_config_btn">
         <property name="text">
          <string>硬件配置</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="operator_btn">
         <property name="text">
          <string>操作人员</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="connect_status">
         <property name="text">
          <string>连接状态</string>
         </property>
         <property name="toolTip">
          <string>连接状态</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="alarm_btn">
         <property name="text">
          <string>报警信息</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="monitor_btn">
         <property name="text">
          <string>实时监控</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </item>

    <item>
     <!-- 主要内容区域 -->
     <widget class="QWidget" name="content_area">
      <layout class="QHBoxLayout" name="content_layout" stretch="1,3">
       <property name="spacing">
        <number>0</number>
       </property>
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item>
        <!-- 左侧功能区 -->
        <widget class="QWidget" name="left_panel">
         <property name="minimumSize">
          <size>
           <width>250</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>250</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">
           QWidget#left_panel {
               background-color: #34495e;
               border-right: 2px solid #2c3e50;
           }
           QPushButton {
               background-color: #34495e;
               color: white;
               border: none;
               padding: 20px;
               text-align: left;
               font-size: 14px;
               font-weight: bold;
           }
           QPushButton:hover {
               background-color: #4a6741;
           }
           QPushButton:checked {
               background-color: #2c3e50;
               border-left: 4px solid #3498db;
           }
          </string>
         </property>
         <layout class="QVBoxLayout" name="left_layout" stretch="1,1,1,1,0">
          <property name="spacing">
           <number>0</number>
          </property>
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item>
           <widget class="QPushButton" name="recipe_import_btn">
            <property name="text">
             <string>配方导入</string>
            </property>
            <property name="checkable">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="program_edit_btn">
            <property name="text">
             <string>程序编辑</string>
            </property>
            <property name="checkable">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="detection_task_btn">
            <property name="text">
             <string>检测任务</string>
            </property>
            <property name="checkable">
             <bool>true</bool>
            </property>
            <property name="checked">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="device_calibrate_btn">
            <property name="text">
             <string>设备校准</string>
            </property>
            <property name="checkable">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="left_spacer">
            <property name="orientation">
             <enum>Qt::Vertical</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>20</width>
              <height>40</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <!-- 右侧内容显示区 -->
        <widget class="QWidget" name="right_content_area">
         <layout class="QVBoxLayout" name="right_content_layout">
          <property name="spacing">
           <number>0</number>
          </property>
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item>
           <!-- 动态标签页区域 -->
           <widget class="QWidget" name="dynamic_tabs_area">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>16777215</width>
              <height>40</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">
              QWidget#dynamic_tabs_area {
                  background-color: #ecf0f1;
                  border-bottom: 1px solid #bdc3c7;
              }
             </string>
            </property>
            <layout class="QHBoxLayout" name="dynamic_tabs_layout">
             <property name="spacing">
              <number>0</number>
             </property>
             <property name="leftMargin">
              <number>0</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>0</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <item>
              <!-- 动态标签页将在这里添加 -->
              <widget class="QScrollArea" name="tabs_scroll_area">
               <property name="styleSheet">
                <string notr="true">
                 QScrollArea {
                     border: none;
                     background-color: transparent;
                 }
                 QScrollBar:horizontal {
                     height: 12px;
                     background-color: #ecf0f1;
                 }
                 QScrollBar::handle:horizontal {
                     background-color: #bdc3c7;
                     border-radius: 6px;
                 }
                </string>
               </property>
               <property name="widgetResizable">
                <bool>true</bool>
               </property>
               <property name="horizontalScrollBarPolicy">
                <enum>Qt::ScrollBarAsNeeded</enum>
               </property>
               <property name="verticalScrollBarPolicy">
                <enum>Qt::ScrollBarAlwaysOff</enum>
               </property>
               <widget class="QWidget" name="tabs_container">
                <property name="geometry">
                 <rect>
                  <x>0</x>
                  <y>0</y>
                  <width>1148</width>
                  <height>40</height>
                 </rect>
                </property>
                <layout class="QHBoxLayout" name="tabs_container_layout">
                 <property name="spacing">
                  <number>2</number>
                 </property>
                 <property name="leftMargin">
                  <number>5</number>
                 </property>
                 <property name="topMargin">
                  <number>5</number>
                 </property>
                 <property name="rightMargin">
                  <number>5</number>
                 </property>
                 <property name="bottomMargin">
                  <number>5</number>
                 </property>
                 <item>
                  <spacer name="tabs_spacer">
                   <property name="orientation">
                    <enum>Qt::Horizontal</enum>
                   </property>
                   <property name="sizeHint" stdset="0">
                    <size>
                     <width>40</width>
                     <height>20</height>
                    </size>
                   </property>
                  </spacer>
                 </item>
                </layout>
               </widget>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <!-- 主要内容区域 -->
           <widget class="QStackedWidget" name="content_stack">
            <property name="currentIndex">
             <number>3</number>
            </property>
         <!-- 默认页面 -->
         <widget class="QWidget" name="default_page">
          <layout class="QVBoxLayout" name="default_layout">
           <item>
            <widget class="QLabel" name="welcome_label">
             <property name="text">
              <string>欢迎使用 Blade Insight 系统
请选择左侧功能模块开始操作</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignCenter</set>
             </property>
             <property name="styleSheet">
              <string notr="true">
               QLabel {
                   font-size: 18px;
                   color: #7f8c8d;
                   padding: 50px;
               }
              </string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
         <!-- 配方导入页面 -->
         <widget class="QWidget" name="recipe_import_page">
          <layout class="QVBoxLayout" name="recipe_layout">
           <property name="leftMargin">
            <number>20</number>
           </property>
           <property name="topMargin">
            <number>20</number>
           </property>
           <property name="rightMargin">
            <number>20</number>
           </property>
           <property name="bottomMargin">
            <number>20</number>
           </property>
           <item>
            <!-- 配方导入顶部工具栏 -->
            <widget class="QWidget" name="recipe_toolbar">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>50</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>50</height>
              </size>
             </property>
             <layout class="QHBoxLayout" name="recipe_toolbar_layout">
              <item>
               <widget class="QPushButton" name="recipe_add_btn">
                <property name="text">
                 <string>+ 新增</string>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>80</width>
                  <height>35</height>
                 </size>
                </property>
                <property name="styleSheet">
                 <string notr="true">
                  QPushButton {
                      background-color: #27ae60;
                      color: white;
                      border: none;
                      border-radius: 5px;
                      font-weight: bold;
                  }
                  QPushButton:hover {
                      background-color: #2ecc71;
                  }
                 </string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="recipe_toolbar_spacer">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
              <item>
               <widget class="QPushButton" name="recipe_back_btn">
                <property name="text">
                 <string>← 回退</string>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>80</width>
                  <height>35</height>
                 </size>
                </property>
                <property name="styleSheet">
                 <string notr="true">
                  QPushButton {
                      background-color: #95a5a6;
                      color: white;
                      border: none;
                      border-radius: 5px;
                  }
                  QPushButton:hover {
                      background-color: #7f8c8d;
                  }
                 </string>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <!-- 配方表格 -->
            <widget class="QTableWidget" name="recipe_table">
             <property name="styleSheet">
              <string notr="true">
               QTableWidget {
                   background-color: white;
                   border: 1px solid #bdc3c7;
                   gridline-color: #ecf0f1;
                   selection-background-color: #3498db;
               }
               QHeaderView::section {
                   background-color: #34495e;
                   color: white;
                   padding: 8px;
                   border: none;
                   font-weight: bold;
               }
               QTableWidget::item {
                   padding: 8px;
                   border-bottom: 1px solid #ecf0f1;
               }
               QTableWidget::item:selected {
                   background-color: #3498db;
                   color: white;
               }
              </string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
         <!-- 程序编辑页面 -->
         <widget class="QWidget" name="program_edit_page">
          <layout class="QVBoxLayout" name="program_layout">
           <property name="spacing">
            <number>0</number>
           </property>
           <property name="leftMargin">
            <number>0</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item>
            <!-- 程序编辑标签页导航 -->
            <widget class="QTabWidget" name="program_tab_widget">
             <property name="currentIndex">
              <number>0</number>
             </property>
             <property name="styleSheet">
              <string notr="true">
               QTabWidget::pane {
                   border: 1px solid #bdc3c7;
                   background-color: white;
               }
               QTabBar::tab {
                   background-color: #ecf0f1;
                   color: #2c3e50;
                   padding: 8px 16px;
                   margin-right: 2px;
                   border-top-left-radius: 4px;
                   border-top-right-radius: 4px;
               }
               QTabBar::tab:selected {
                   background-color: #3498db;
                   color: white;
               }
               QTabBar::tab:hover {
                   background-color: #bdc3c7;
               }
              </string>
             </property>
             <!-- 程序列表标签页 -->
             <widget class="QWidget" name="program_list_tab">
              <attribute name="title">
               <string>程序列表</string>
              </attribute>
              <layout class="QVBoxLayout" name="program_list_layout">
               <property name="leftMargin">
                <number>20</number>
               </property>
               <property name="topMargin">
                <number>20</number>
               </property>
               <property name="rightMargin">
                <number>20</number>
               </property>
               <property name="bottomMargin">
                <number>20</number>
               </property>
               <item>
                <!-- 程序列表顶部工具栏 -->
                <widget class="QWidget" name="program_list_toolbar">
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>50</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>16777215</width>
                   <height>50</height>
                  </size>
                 </property>
                 <layout class="QHBoxLayout" name="program_list_toolbar_layout">
                  <item>
                   <widget class="QPushButton" name="program_add_btn">
                    <property name="text">
                     <string>+ 新增</string>
                    </property>
                    <property name="minimumSize">
                     <size>
                      <width>80</width>
                      <height>35</height>
                     </size>
                    </property>
                    <property name="styleSheet">
                     <string notr="true">
                      QPushButton {
                          background-color: #27ae60;
                          color: white;
                          border: none;
                          border-radius: 5px;
                          font-weight: bold;
                      }
                      QPushButton:hover {
                          background-color: #2ecc71;
                      }
                     </string>
                    </property>
                   </widget>
                  </item>
                  <item>
                   <spacer name="program_list_toolbar_spacer">
                    <property name="orientation">
                     <enum>Qt::Horizontal</enum>
                    </property>
                    <property name="sizeHint" stdset="0">
                     <size>
                      <width>40</width>
                      <height>20</height>
                     </size>
                    </property>
                   </spacer>
                  </item>
                 </layout>
                </widget>
               </item>

               <item>
                <!-- 程序编辑表格 -->
                <widget class="QTableWidget" name="program_table">
                 <property name="styleSheet">
                  <string notr="true">
                   QTableWidget {
                       border: 1px solid #bdc3c7;
                       background-color: white;
                       gridline-color: #ecf0f1;
                       selection-background-color: #3498db;
                       alternate-background-color: #f8f9fa;
                   }
                   QHeaderView::section {
                       background-color: #34495e;
                       color: white;
                       padding: 12px;
                       border: none;
                       font-weight: bold;
                       font-size: 12px;
                       min-height: 35px;
                   }
                   QTableWidget::item {
                       padding: 15px 8px;
                       border-bottom: 1px solid #ecf0f1;
                       font-size: 11px;
                       min-height: 60px;
                   }
                   QTableWidget::item:selected {
                       background-color: #e8f4fd;
                       color: #2c3e50;
                   }
                  </string>
                 </property>
                 <property name="alternatingRowColors">
                  <bool>true</bool>
                 </property>
                 <property name="selectionBehavior">
                  <enum>QAbstractItemView::SelectRows</enum>
                 </property>
                 <property name="horizontalScrollMode">
                  <enum>QAbstractItemView::ScrollPerPixel</enum>
                 </property>
                 <property name="verticalScrollMode">
                  <enum>QAbstractItemView::ScrollPerPixel</enum>
                 </property>
                 <attribute name="horizontalHeaderStretchLastSection">
                  <bool>false</bool>
                 </attribute>
                 <attribute name="verticalHeaderVisible">
                  <bool>false</bool>
                 </attribute>
                 <column>
                  <property name="text">
                   <string>类型</string>
                  </property>
                 </column>
                 <column>
                  <property name="text">
                   <string>配方</string>
                  </property>
                 </column>
                 <column>
                  <property name="text">
                   <string>料号</string>
                  </property>
                 </column>
                 <column>
                  <property name="text">
                   <string>名称</string>
                  </property>
                 </column>
                 <column>
                  <property name="text">
                   <string>操作</string>
                  </property>
                 </column>
                </widget>
               </item>
              </layout>
             </widget>
             <!-- 程序编辑与点动控制标签页 -->
             <widget class="QWidget" name="program_edit_jog_tab">
              <attribute name="title">
               <string>程序编辑与点动控制</string>
              </attribute>
              <layout class="QVBoxLayout" name="program_edit_jog_layout">
               <property name="spacing">
                <number>0</number>
               </property>
               <property name="leftMargin">
                <number>0</number>
               </property>
               <property name="topMargin">
                <number>0</number>
               </property>
               <property name="rightMargin">
                <number>0</number>
               </property>
               <property name="bottomMargin">
                <number>0</number>
               </property>
               <item>
                <!-- 嵌入机器人控制界面 -->
                <widget class="QWidget" name="robot_control_container">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">
                   QWidget#robot_control_container {
                       background-color: #f5f5f5;
                   }
                  </string>
                 </property>
                 <layout class="QVBoxLayout" name="robot_control_layout">
                  <property name="spacing">
                   <number>0</number>
                  </property>
                  <property name="leftMargin">
                   <number>0</number>
                  </property>
                  <property name="topMargin">
                   <number>0</number>
                  </property>
                  <property name="rightMargin">
                   <number>0</number>
                  </property>
                  <property name="bottomMargin">
                   <number>0</number>
                  </property>
                  <!-- 这里将通过代码动态加载robot_control_embedded.ui -->
                 </layout>
                </widget>
               </item>
              </layout>
             </widget>
            </widget>
           </item>
          </layout>
         </widget>
         <!-- 检测任务页面 -->
         <widget class="QWidget" name="detection_task_page">
          <layout class="QVBoxLayout" name="task_layout">
           <property name="spacing">
            <number>0</number>
           </property>
           <property name="leftMargin">
            <number>0</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item>
            <!-- 检测任务标签页导航 -->
            <widget class="QTabWidget" name="task_tab_widget">
             <property name="currentIndex">
              <number>0</number>
             </property>
             <property name="styleSheet">
              <string notr="true">
               QTabWidget::pane {
                   border: 1px solid #bdc3c7;
                   background-color: white;
               }
               QTabBar::tab {
                   background-color: #ecf0f1;
                   color: #2c3e50;
                   padding: 8px 16px;
                   margin-right: 2px;
                   border-top-left-radius: 4px;
                   border-top-right-radius: 4px;
               }
               QTabBar::tab:selected {
                   background-color: #3498db;
                   color: white;
               }
               QTabBar::tab:hover {
                   background-color: #bdc3c7;
               }
              </string>
             </property>
             <!-- 任务创建标签页 -->
             <widget class="QWidget" name="task_create_tab">
              <attribute name="title">
               <string>任务创建</string>
              </attribute>
              <layout class="QVBoxLayout" name="task_create_layout">
               <property name="leftMargin">
                <number>20</number>
               </property>
               <property name="topMargin">
                <number>20</number>
               </property>
               <property name="rightMargin">
                <number>20</number>
               </property>
               <property name="bottomMargin">
                <number>20</number>
               </property>
               <item>
                <!-- 任务输入区域 -->
                <widget class="QWidget" name="task_input_widget">
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>200</height>
                  </size>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">
                   QWidget {
                       background-color: #f8f9fa;
                       border: 1px solid #bdc3c7;
                       border-radius: 8px;
                   }
                   QLabel {
                       font-weight: bold;
                       color: #2c3e50;
                       padding: 5px;
                   }
                   QLineEdit, QComboBox {
                       border: 1px solid #bdc3c7;
                       border-radius: 4px;
                       padding: 8px;
                       background-color: white;
                       font-size: 12px;
                   }
                   QComboBox::drop-down {
                       border: none;
                       width: 20px;
                   }
                   QComboBox::down-arrow {
                       image: none;
                       border-left: 5px solid transparent;
                       border-right: 5px solid transparent;
                       border-top: 5px solid #2c3e50;
                   }
                  </string>
                 </property>
                 <layout class="QGridLayout" name="task_input_grid">
                  <property name="leftMargin">
                   <number>20</number>
                  </property>
                  <property name="topMargin">
                   <number>20</number>
                  </property>
                  <property name="rightMargin">
                   <number>20</number>
                  </property>
                  <property name="bottomMargin">
                   <number>20</number>
                  </property>
                  <item row="0" column="0">
                   <widget class="QLabel" name="part_number_label">
                    <property name="text">
                     <string>件号选择:</string>
                    </property>
                   </widget>
                  </item>
                  <item row="0" column="1">
                   <layout class="QHBoxLayout" name="part_number_layout">
                    <item>
                     <widget class="QLineEdit" name="part_number_edit">
                      <property name="readOnly">
                       <bool>true</bool>
                      </property>
                      <property name="placeholderText">
                       <string>点击选择按钮选择任务文件夹</string>
                      </property>
                     </widget>
                    </item>
                    <item>
                     <widget class="QPushButton" name="part_number_select_btn">
                      <property name="text">
                       <string>选择文件夹</string>
                      </property>
                      <property name="minimumSize">
                       <size>
                        <width>100</width>
                        <height>0</height>
                       </size>
                      </property>
                      <property name="styleSheet">
                       <string notr="true">
                        QPushButton {
                            background-color: #3498db;
                            color: white;
                            border: none;
                            border-radius: 4px;
                            padding: 8px 16px;
                            font-weight: bold;
                        }
                        QPushButton:hover {
                            background-color: #2980b9;
                        }
                        QPushButton:pressed {
                            background-color: #21618c;
                        }
                       </string>
                      </property>
                     </widget>
                    </item>
                   </layout>
                  </item>
                  <item row="1" column="0">
                   <widget class="QLabel" name="program_number_label">
                    <property name="text">
                     <string>程序号:</string>
                    </property>
                   </widget>
                  </item>
                  <item row="1" column="1">
                   <widget class="QLabel" name="program_number_label_display">
                    <property name="text">
                     <string>-</string>
                    </property>
                    <property name="styleSheet">
                     <string notr="true">
                      QLabel {
                          border: 2px solid #bdc3c7;
                          border-radius: 4px;
                          padding: 8px;
                          font-size: 12px;
                          background-color: #f8f9fa;
                          color: #2c3e50;
                      }
                     </string>
                    </property>
                    <property name="alignment">
                     <set>Qt::AlignLeft|Qt::AlignVCenter</set>
                    </property>
                   </widget>
                  </item>
                  <item row="2" column="0">
                   <widget class="QLabel" name="task_number_label">
                    <property name="text">
                     <string>任务号:</string>
                    </property>
                   </widget>
                  </item>
                  <item row="2" column="1">
                   <widget class="QLineEdit" name="task_number_edit">
                    <property name="readOnly">
                     <bool>true</bool>
                    </property>
                    <property name="placeholderText">
                     <string>自动生成</string>
                    </property>
                   </widget>
                  </item>
                  <item row="3" column="0">
                   <widget class="QLabel" name="task_info_label">
                    <property name="text">
                     <string>任务信息:</string>
                    </property>
                   </widget>
                  </item>
                  <item row="3" column="1">
                   <widget class="QTextEdit" name="task_info_edit">
                    <property name="minimumSize">
                     <size>
                      <width>0</width>
                      <height>200</height>
                     </size>
                    </property>
                    <property name="maximumSize">
                     <size>
                      <width>16777215</width>
                      <height>300</height>
                     </size>
                    </property>
                    <property name="readOnly">
                     <bool>true</bool>
                    </property>
                    <property name="placeholderText">
                     <string>任务详细信息将在此显示</string>
                    </property>
                    <property name="styleSheet">
                     <string notr="true">
                      QTextEdit {
                          border: 2px solid #bdc3c7;
                          border-radius: 4px;
                          padding: 8px;
                          font-size: 11px;
                          font-family: "Microsoft YaHei", "SimSun";
                          background-color: #f8f9fa;
                      }
                      QTextEdit:focus {
                          border-color: #3498db;
                      }
                     </string>
                    </property>
                   </widget>
                  </item>
                  <item row="4" column="0">
                   <widget class="QLabel" name="task_quantity_label">
                    <property name="text">
                     <string>任务参数:</string>
                    </property>
                   </widget>
                  </item>
                  <item row="4" column="1">
                   <widget class="QWidget" name="task_params_widget">
                    <layout class="QHBoxLayout" name="task_params_layout">
                     <property name="spacing">
                      <number>10</number>
                     </property>
                     <property name="leftMargin">
                      <number>0</number>
                     </property>
                     <property name="topMargin">
                      <number>0</number>
                     </property>
                     <property name="rightMargin">
                      <number>0</number>
                     </property>
                     <property name="bottomMargin">
                      <number>0</number>
                     </property>
                     <item>
                      <widget class="QLabel" name="quantity_label">
                       <property name="text">
                        <string>数量:</string>
                       </property>
                       <property name="minimumSize">
                        <size>
                         <width>60</width>
                         <height>35</height>
                        </size>
                       </property>
                       <property name="styleSheet">
                        <string notr="true">
                         QLabel {
                             font-size: 14px;
                             font-weight: bold;
                             color: #2c3e50;
                         }
                        </string>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QLineEdit" name="task_quantity_edit">
                       <property name="placeholderText">
                        <string>1</string>
                       </property>
                       <property name="inputMask">
                        <string>99</string>
                       </property>
                       <property name="text">
                        <string>1</string>
                       </property>
                       <property name="minimumSize">
                        <size>
                         <width>120</width>
                         <height>35</height>
                        </size>
                       </property>
                       <property name="styleSheet">
                        <string notr="true">
                         QLineEdit {
                             border: 2px solid #bdc3c7;
                             border-radius: 6px;
                             padding: 8px 12px;
                             font-size: 16px;
                             font-weight: bold;
                             background-color: white;
                         }
                         QLineEdit:focus {
                             border-color: #3498db;
                             background-color: #f8f9fa;
                         }
                        </string>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QLabel" name="speed_label">
                       <property name="text">
                        <string>速度:</string>
                       </property>
                       <property name="minimumSize">
                        <size>
                         <width>60</width>
                         <height>35</height>
                        </size>
                       </property>
                       <property name="styleSheet">
                        <string notr="true">
                         QLabel {
                             font-size: 14px;
                             font-weight: bold;
                             color: #2c3e50;
                         }
                        </string>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QLineEdit" name="task_speed_edit">
                       <property name="placeholderText">
                        <string>100</string>
                       </property>
                       <property name="inputMask">
                        <string>999</string>
                       </property>
                       <property name="text">
                        <string>100</string>
                       </property>
                       <property name="minimumSize">
                        <size>
                         <width>120</width>
                         <height>35</height>
                        </size>
                       </property>
                       <property name="styleSheet">
                        <string notr="true">
                         QLineEdit {
                             border: 2px solid #bdc3c7;
                             border-radius: 6px;
                             padding: 8px 12px;
                             font-size: 16px;
                             font-weight: bold;
                             background-color: white;
                         }
                         QLineEdit:focus {
                             border-color: #3498db;
                             background-color: #f8f9fa;
                         }
                        </string>
                       </property>
                      </widget>
                     </item>
                     <item>
                      <widget class="QPushButton" name="apply_params_btn">
                       <property name="text">
                        <string>应用</string>
                       </property>
                       <property name="minimumSize">
                        <size>
                         <width>100</width>
                         <height>35</height>
                        </size>
                       </property>
                       <property name="styleSheet">
                        <string notr="true">
                         QPushButton {
                             background-color: #28a745;
                             color: white;
                             border: none;
                             border-radius: 6px;
                             padding: 8px 16px;
                             font-size: 14px;
                             font-weight: bold;
                         }
                         QPushButton:hover {
                             background-color: #218838;
                         }
                         QPushButton:pressed {
                             background-color: #1e7e34;
                         }
                        </string>
                       </property>
                      </widget>
                     </item>
                    </layout>
                   </widget>
                  </item>
                 </layout>
                </widget>
               </item>
               <item>
                <!-- 任务控制按钮 -->
                <widget class="QWidget" name="task_control_widget">
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>80</height>
                  </size>
                 </property>
                 <layout class="QHBoxLayout" name="task_control_layout">
                  <item>
                   <spacer name="task_control_spacer">
                    <property name="orientation">
                     <enum>Qt::Horizontal</enum>
                    </property>
                    <property name="sizeHint" stdset="0">
                     <size>
                      <width>40</width>
                      <height>20</height>
                     </size>
                    </property>
                   </spacer>
                  </item>
                  <item>
                   <widget class="QPushButton" name="task_start_btn">
                    <property name="text">
                     <string>任务启动</string>
                    </property>
                    <property name="minimumSize">
                     <size>
                      <width>120</width>
                      <height>40</height>
                     </size>
                    </property>
                    <property name="styleSheet">
                     <string notr="true">
                      QPushButton {
                          background-color: #27ae60;
                          color: white;
                          border: none;
                          border-radius: 8px;
                          font-size: 14px;
                          font-weight: bold;
                      }
                      QPushButton:hover {
                          background-color: #2ecc71;
                      }
                      QPushButton:pressed {
                          background-color: #229954;
                      }
                     </string>
                    </property>
                   </widget>
                  </item>
                  <item>
                   <widget class="QPushButton" name="task_status_btn">
                    <property name="text">
                     <string>任务状态</string>
                    </property>
                    <property name="minimumSize">
                     <size>
                      <width>120</width>
                      <height>40</height>
                     </size>
                    </property>
                    <property name="styleSheet">
                     <string notr="true">
                      QPushButton {
                          background-color: #3498db;
                          color: white;
                          border: none;
                          border-radius: 8px;
                          font-size: 14px;
                          font-weight: bold;
                      }
                      QPushButton:hover {
                          background-color: #5dade2;
                      }
                      QPushButton:pressed {
                          background-color: #2980b9;
                      }
                     </string>
                    </property>
                   </widget>
                  </item>
                  <item>
                   <spacer name="task_control_spacer2">
                    <property name="orientation">
                     <enum>Qt::Horizontal</enum>
                    </property>
                    <property name="sizeHint" stdset="0">
                     <size>
                      <width>40</width>
                      <height>20</height>
                     </size>
                    </property>
                   </spacer>
                  </item>
                 </layout>
                </widget>
               </item>
               <item>
                <spacer name="task_create_spacer">
                 <property name="orientation">
                  <enum>Qt::Vertical</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>20</width>
                   <height>40</height>
                  </size>
                 </property>
                </spacer>
               </item>
              </layout>
             </widget>
             <!-- 任务详情查看标签页 -->
             <widget class="QWidget" name="task_detail_tab">
              <attribute name="title">
               <string>任务详情</string>
              </attribute>
              <layout class="QVBoxLayout" name="task_detail_layout">
               <property name="spacing">
                <number>0</number>
               </property>
               <property name="leftMargin">
                <number>20</number>
               </property>
               <property name="topMargin">
                <number>20</number>
               </property>
               <property name="rightMargin">
                <number>20</number>
               </property>
               <property name="bottomMargin">
                <number>20</number>
               </property>
               <item>
                <!-- 这里将嵌入untitled.ui的内容 -->
                <widget class="QWidget" name="untitled_ui_container">
                 <property name="styleSheet">
                  <string notr="true">
                   QWidget {
                       background-color: white;
                       border: 1px solid #bdc3c7;
                   }
                  </string>
                 </property>
                 <layout class="QVBoxLayout" name="untitled_container_layout">
                  <property name="spacing">
                   <number>0</number>
                  </property>
                  <property name="leftMargin">
                   <number>0</number>
                  </property>
                  <property name="topMargin">
                   <number>0</number>
                  </property>
                  <property name="rightMargin">
                   <number>0</number>
                  </property>
                  <property name="bottomMargin">
                   <number>0</number>
                  </property>
                  <item>
                   <widget class="QLabel" name="untitled_placeholder">
                    <property name="text">
                     <string>任务详情界面
(此处将加载untitled.ui界面内容)</string>
                    </property>
                    <property name="alignment">
                     <set>Qt::AlignCenter</set>
                    </property>
                    <property name="styleSheet">
                     <string notr="true">
                      QLabel {
                          font-size: 16px;
                          color: #7f8c8d;
                          padding: 50px;
                          border: 2px dashed #bdc3c7;
                          border-radius: 10px;
                          margin: 20px;
                      }
                     </string>
                    </property>
                   </widget>
                  </item>
                 </layout>
                </widget>
               </item>
              </layout>
             </widget>
             <!-- 任务状态标签页 -->
             <widget class="QWidget" name="task_status_tab">
              <attribute name="title">
               <string>任务状态</string>
              </attribute>
              <layout class="QVBoxLayout" name="task_status_layout">
               <property name="leftMargin">
                <number>20</number>
               </property>
               <property name="topMargin">
                <number>20</number>
               </property>
               <property name="rightMargin">
                <number>20</number>
               </property>
               <property name="bottomMargin">
                <number>20</number>
               </property>
               <item>
                <!-- 任务队列表格 -->
                <widget class="QTableWidget" name="task_queue_table">
                 <property name="styleSheet">
                  <string notr="true">
                   QTableWidget {
                       border: 1px solid #bdc3c7;
                       background-color: white;
                       gridline-color: #ecf0f1;
                       selection-background-color: #3498db;
                       alternate-background-color: #f8f9fa;
                   }
                   QHeaderView::section {
                       background-color: #34495e;
                       color: white;
                       padding: 12px;
                       border: none;
                       font-weight: bold;
                       font-size: 12px;
                       min-height: 35px;
                   }
                   QTableWidget::item {
                       padding: 15px 8px;
                       border-bottom: 1px solid #ecf0f1;
                       font-size: 11px;
                       min-height: 60px;
                   }
                   QTableWidget::item:selected {
                       background-color: #e8f4fd;
                       color: #2c3e50;
                   }
                  </string>
                 </property>
                 <property name="alternatingRowColors">
                  <bool>true</bool>
                 </property>
                 <property name="selectionBehavior">
                  <enum>QAbstractItemView::SelectRows</enum>
                 </property>
                 <property name="horizontalScrollMode">
                  <enum>QAbstractItemView::ScrollPerPixel</enum>
                 </property>
                 <property name="verticalScrollMode">
                  <enum>QAbstractItemView::ScrollPerPixel</enum>
                 </property>
                 <attribute name="horizontalHeaderStretchLastSection">
                  <bool>false</bool>
                 </attribute>
                 <attribute name="verticalHeaderVisible">
                  <bool>false</bool>
                 </attribute>
                 <column>
                  <property name="text">
                   <string>任务号</string>
                  </property>
                 </column>
                 <column>
                  <property name="text">
                   <string>件号</string>
                  </property>
                 </column>
                 <column>
                  <property name="text">
                   <string>程序号</string>
                  </property>
                 </column>
                 <column>
                  <property name="text">
                   <string>创建时间</string>
                  </property>
                 </column>
                 <column>
                  <property name="text">
                   <string>进度</string>
                  </property>
                 </column>
                 <column>
                  <property name="text">
                   <string>状态</string>
                  </property>
                 </column>
                 <column>
                  <property name="text">
                   <string>操作</string>
                  </property>
                 </column>
                </widget>
               </item>
              </layout>
             </widget>
            </widget>
           </item>
          </layout>
         </widget>
         <!-- 设备校准页面 -->
         <widget class="QWidget" name="device_calibrate_page">
          <layout class="QVBoxLayout" name="calibrate_layout">
           <property name="leftMargin">
            <number>30</number>
           </property>
           <property name="topMargin">
            <number>30</number>
           </property>
           <property name="rightMargin">
            <number>30</number>
           </property>
           <property name="bottomMargin">
            <number>30</number>
           </property>
           <item>
            <!-- 设备校准标题 -->
            <widget class="QLabel" name="calibrate_title">
             <property name="text">
              <string>设备校准</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignCenter</set>
             </property>
             <property name="styleSheet">
              <string notr="true">
               QLabel {
                   font-size: 20px;
                   font-weight: bold;
                   color: #2c3e50;
                   padding: 15px;
                   margin-bottom: 20px;
               }
              </string>
             </property>
            </widget>
           </item>
           <item>
            <!-- 校准按钮网格布局 -->
            <widget class="QWidget" name="calibrate_buttons_widget">
             <property name="styleSheet">
              <string notr="true">
               QPushButton {
                   background-color: #3498db;
                   color: white;
                   border: none;
                   border-radius: 8px;
                   padding: 20px;
                   font-size: 16px;
                   font-weight: bold;
                   min-height: 80px;
               }
               QPushButton:hover {
                   background-color: #5dade2;
               }
               QPushButton:pressed {
                   background-color: #2980b9;
               }
               QPushButton[calibrationType="sensor"] {
                   background-color: #e67e22;
               }
               QPushButton[calibrationType="sensor"]:hover {
                   background-color: #f39c12;
               }
               QPushButton[calibrationType="camera"] {
                   background-color: #9b59b6;
               }
               QPushButton[calibrationType="camera"]:hover {
                   background-color: #af7ac5;
               }
               QPushButton[calibrationType="robot"] {
                   background-color: #27ae60;
               }
               QPushButton[calibrationType="robot"]:hover {
                   background-color: #58d68d;
               }
               QPushButton[calibrationType="auto"] {
                   background-color: #e74c3c;
               }
               QPushButton[calibrationType="auto"]:hover {
                   background-color: #ec7063;
               }
              </string>
             </property>
             <layout class="QGridLayout" name="calibrate_grid">
              <property name="spacing">
               <number>20</number>
              </property>

              <item row="0" column="0">
               <widget class="QPushButton" name="sensor1_calibrate_btn">
                <property name="text">
                 <string>传感器1自检</string>
                </property>
                <property name="calibrationType" stdset="0">
                 <string>sensor</string>
                </property>
               </widget>
              </item>
              <item row="0" column="1">
               <widget class="QPushButton" name="sensor2_calibrate_btn">
                <property name="text">
                 <string>传感器2自检</string>
                </property>
                <property name="calibrationType" stdset="0">
                 <string>sensor</string>
                </property>
               </widget>
              </item>
              <item row="0" column="2">
               <widget class="QPushButton" name="sensor3_calibrate_btn">
                <property name="text">
                 <string>传感器3自检</string>
                </property>
                <property name="calibrationType" stdset="0">
                 <string>sensor</string>
                </property>
               </widget>
              </item>
              <item row="1" column="0">
               <widget class="QPushButton" name="camera_calibrate_btn">
                <property name="text">
                 <string>相机标定</string>
                </property>
                <property name="calibrationType" stdset="0">
                 <string>camera</string>
                </property>
               </widget>
              </item>
              <item row="1" column="1">
               <widget class="QPushButton" name="robot_calibrate_btn">
                <property name="text">
                 <string>机器人标定</string>
                </property>
                <property name="calibrationType" stdset="0">
                 <string>robot</string>
                </property>
               </widget>
              </item>
              <item row="1" column="2">
               <widget class="QPushButton" name="gripper_calibrate_btn">
                <property name="text">
                 <string>夹爪标定</string>
                </property>
                <property name="calibrationType" stdset="0">
                 <string>robot</string>
                </property>
               </widget>
              </item>
              <item row="2" column="0" colspan="3">
               <widget class="QPushButton" name="auto_calibrate_btn">
                <property name="text">
                 <string>全自动标定</string>
                </property>
                <property name="calibrationType" stdset="0">
                 <string>auto</string>
                </property>
                <property name="styleSheet">
                 <string notr="true">
                  QPushButton {
                      font-size: 18px;
                      min-height: 80px;
                  }
                 </string>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <spacer name="calibrate_spacer">
             <property name="orientation">
              <enum>Qt::Vertical</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>20</width>
               <height>40</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </widget>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
 </item>
</layout>
</widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources/>
 <connections/>
</ui>
