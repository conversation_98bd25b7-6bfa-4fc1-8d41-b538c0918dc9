<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>RobotControlWidget</class>
 <widget class="QWidget" name="RobotControlWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1400</width>
    <height>800</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="minimumSize">
   <size>
    <width>1200</width>
    <height>700</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>机器人控制界面</string>
  </property>
  <property name="styleSheet">
   <string notr="true">
    QMainWindow {
        background-color: #f5f5f5;
        color: #2c3e50;
    }
    QWidget {
        background-color: #f5f5f5;
        color: #2c3e50;
    }
    QPushButton {
        background-color: #ffffff;
        border: 1px solid #bdc3c7;
        border-radius: 5px;
        padding: 8px 12px;
        color: #2c3e50;
        font-size: 12px;
        font-weight: normal;
    }
    QPushButton:hover {
        background-color: #e6f3ff;
        border-color: #3498db;
    }
    QPushButton:pressed {
        background-color: #cce7ff;
    }
    QLineEdit {
        background-color: #ffffff;
        border: 1px solid #bdc3c7;
        border-radius: 3px;
        padding: 6px 8px;
        color: #2c3e50;
        font-size: 12px;
    }
    QLineEdit:focus {
        border-color: #3498db;
        background-color: #ffffff;
    }
    QLabel {
        color: #2c3e50;
        font-size: 12px;
        font-weight: bold;
    }
    QListWidget {
        background-color: #ffffff;
        border: 1px solid #bdc3c7;
        color: #2c3e50;
        font-size: 12px;
        selection-background-color: #3498db;
        selection-color: white;
    }
    QListWidget::item {
        padding: 8px;
        border-bottom: 1px solid #ecf0f1;
    }
    QListWidget::item:hover {
        background-color: #f8f9fa;
    }
    QListWidget::item:selected {
        background-color: #3498db;
        color: white;
    }
    QGroupBox {
        border: 1px solid #bdc3c7;
        border-radius: 5px;
        margin-top: 12px;
        padding-top: 12px;
        color: #2c3e50;
        font-weight: bold;
        background-color: #ffffff;
    }
    QGroupBox::title {
        subcontrol-origin: margin;
        left: 10px;
        padding: 0 8px 0 8px;
        color: #34495e;
        font-size: 13px;
        font-weight: bold;
    }
   </string>
  </property>
  <layout class="QGridLayout" name="gridLayout_3">
   <property name="leftMargin">
    <number>10</number>
   </property>
   <property name="topMargin">
    <number>10</number>
   </property>
   <property name="rightMargin">
    <number>10</number>
   </property>
   <property name="bottomMargin">
    <number>10</number>
   </property>
   <property name="spacing">
    <number>15</number>
   </property>
   <item row="0" column="1">
    <widget class="QGroupBox" name="program_group">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
       <horstretch>2</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="title">
      <string>程序</string>
     </property>
     <layout class="QGridLayout" name="gridLayout_2">
      <item row="0" column="0">
       <layout class="QVBoxLayout" name="verticalLayout" stretch="1">
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout" stretch="2,1">
          <item>
           <widget class="QListWidget" name="program_list">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>0</height>
             </size>
            </property>
            <item>
             <property name="text">
              <string>1    机器人配置</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>2    PLC配置</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>3    视觉定位配置</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>4    3D扫描配置</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>5    接头上料配置</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>6    机器人关节运动</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>7    PLC: 发送数字信号给台架A</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>8    PLC: 发送数字信号给台架A</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>9    PLC: 接收数字信号给台架A</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>10   PLC: 接收数字信号给台架A</string>
             </property>
            </item>
           </widget>
          </item>
          <item>
           <widget class="QGroupBox" name="position_group">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>300</width>
              <height>0</height>
             </size>
            </property>
            <property name="title">
             <string>位置控制</string>
            </property>
            <layout class="QVBoxLayout" name="position_layout">
             <item>
              <widget class="QGroupBox" name="speed_group">
               <property name="title">
                <string/>
               </property>
               <layout class="QVBoxLayout" name="verticalLayout_3">
                <property name="spacing">
                 <number>8</number>
                </property>
                <item>
                 <layout class="QHBoxLayout" name="tcp_step_layout">
                  <item>
                   <widget class="QLabel" name="tcp_step_label">
                    <property name="minimumSize">
                     <size>
                      <width>80</width>
                      <height>35</height>
                     </size>
                    </property>
                    <property name="maximumSize">
                     <size>
                      <width>80</width>
                      <height>35</height>
                     </size>
                    </property>
                    <property name="text">
                     <string>TCP步长:</string>
                    </property>
                    <property name="alignment">
                     <set>Qt::AlignCenter</set>
                    </property>
                   </widget>
                  </item>
                  <item>
                   <widget class="QLineEdit" name="tcp_step_value">
                    <property name="minimumSize">
                     <size>
                      <width>100</width>
                      <height>35</height>
                     </size>
                    </property>
                    <property name="maximumSize">
                     <size>
                      <width>150</width>
                      <height>35</height>
                     </size>
                    </property>
                    <property name="text">
                     <string>1.0</string>
                    </property>
                    <property name="alignment">
                     <set>Qt::AlignCenter</set>
                    </property>
                   </widget>
                  </item>
                 </layout>
                </item>
                <item>
                 <layout class="QHBoxLayout" name="joint_step_layout">
                  <item>
                   <widget class="QLabel" name="joint_step_label">
                    <property name="minimumSize">
                     <size>
                      <width>80</width>
                      <height>35</height>
                     </size>
                    </property>
                    <property name="maximumSize">
                     <size>
                      <width>80</width>
                      <height>35</height>
                     </size>
                    </property>
                    <property name="text">
                     <string>关节步长:</string>
                    </property>
                    <property name="alignment">
                     <set>Qt::AlignCenter</set>
                    </property>
                   </widget>
                  </item>
                  <item>
                   <widget class="QLineEdit" name="joint_step_value">
                    <property name="minimumSize">
                     <size>
                      <width>100</width>
                      <height>35</height>
                     </size>
                    </property>
                    <property name="maximumSize">
                     <size>
                      <width>150</width>
                      <height>35</height>
                     </size>
                    </property>
                    <property name="text">
                     <string>0.25</string>
                    </property>
                    <property name="alignment">
                     <set>Qt::AlignCenter</set>
                    </property>
                   </widget>
                  </item>
                 </layout>
                </item>
                <item>
                 <layout class="QHBoxLayout" name="linear_speed_layout">
                  <item>
                   <widget class="QLabel" name="linear_speed_label">
                    <property name="minimumSize">
                     <size>
                      <width>80</width>
                      <height>35</height>
                     </size>
                    </property>
                    <property name="maximumSize">
                     <size>
                      <width>80</width>
                      <height>35</height>
                     </size>
                    </property>
                    <property name="text">
                     <string>平移速率:</string>
                    </property>
                    <property name="alignment">
                     <set>Qt::AlignCenter</set>
                    </property>
                   </widget>
                  </item>
                  <item>
                   <widget class="QLineEdit" name="linear_speed_value">
                    <property name="minimumSize">
                     <size>
                      <width>100</width>
                      <height>35</height>
                     </size>
                    </property>
                    <property name="maximumSize">
                     <size>
                      <width>150</width>
                      <height>35</height>
                     </size>
                    </property>
                    <property name="text">
                     <string>10.0</string>
                    </property>
                    <property name="alignment">
                     <set>Qt::AlignCenter</set>
                    </property>
                   </widget>
                  </item>
                 </layout>
                </item>

               </layout>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </item>
      <item row="2" column="0">
       <widget class="QGroupBox" name="step_group">
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>380</height>
         </size>
        </property>
        <property name="title">
         <string>步骤</string>
        </property>
        <layout class="QGridLayout" name="gridLayout">
         <item row="0" column="0">
          <layout class="QHBoxLayout" name="step_display_layout" stretch="1,1,1">
           <item>
            <widget class="QGroupBox" name="prev_step_group">
             <property name="title">
              <string>上一步骤:</string>
             </property>
             <layout class="QVBoxLayout" name="verticalLayout_2">
              <item>
               <widget class="QLabel" name="prev_step_label">
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>50</height>
                 </size>
                </property>
                <property name="text">
                 <string>Unknown:</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QListWidget" name="prev_step_list">
                <property name="minimumSize">
                 <size>
                  <width>0</width>
                  <height>200</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>16777215</height>
                 </size>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="current_step_group">
             <property name="title">
              <string>当前步骤:</string>
             </property>
             <layout class="QVBoxLayout" name="current_step_layout">
              <item>
               <widget class="QLabel" name="current_step_label">
                <property name="text">
                 <string>Unknown:</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QListWidget" name="current_step_list">
                <property name="minimumSize">
                 <size>
                  <width>0</width>
                  <height>200</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>16777215</height>
                 </size>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="next_step_group">
             <property name="title">
              <string>下一步骤:</string>
             </property>
             <layout class="QVBoxLayout" name="next_step_layout">
              <item>
               <widget class="QLabel" name="next_step_label">
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>50</height>
                 </size>
                </property>
                <property name="text">
                 <string>Unknown:</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QListWidget" name="next_step_list">
                <property name="minimumSize">
                 <size>
                  <width>0</width>
                  <height>200</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>16777215</height>
                 </size>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
          </layout>
         </item>
        </layout>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QProgressBar" name="progressBar">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>25</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>25</height>
         </size>
        </property>
        <property name="value">
         <number>24</number>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item row="1" column="0" colspan="2">
    <layout class="QHBoxLayout" name="control_layout">
     <property name="spacing">
      <number>10</number>
     </property>
     <property name="leftMargin">
      <number>10</number>
     </property>
     <property name="topMargin">
      <number>10</number>
     </property>
     <property name="rightMargin">
      <number>10</number>
     </property>
     <property name="bottomMargin">
      <number>10</number>
     </property>
     <item>
      <widget class="QPushButton" name="absolute_mode_btn">
       <property name="text">
        <string>绝对模式</string>
       </property>
       <property name="checkable">
        <bool>true</bool>
       </property>
       <property name="checked">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="record_btn">
       <property name="text">
        <string>标记基准</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="insert_btn">
       <property name="text">
        <string>插入</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="modify_btn">
       <property name="text">
        <string>修改</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="prev_step_btn">
       <property name="text">
        <string>《上一步</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="next_step_btn">
       <property name="text">
        <string>下一步》</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="execute_btn">
       <property name="styleSheet">
        <string notr="true"/>
       </property>
       <property name="text">
        <string>执行</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="save_btn">
       <property name="text">
        <string>保存</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="delete_btn">
       <property name="text">
        <string>删除</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="exit_btn">
       <property name="text">
        <string>退出</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item row="0" column="0">
    <widget class="QGroupBox" name="coordinate_group">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>300</width>
       <height>0</height>
      </size>
     </property>
     <property name="title">
      <string>坐标控制</string>
     </property>
     <layout class="QGridLayout" name="gridLayout_4">
      <item row="0" column="0">
       <layout class="QVBoxLayout" name="verticalLayout_6">
        <item>
         <layout class="QVBoxLayout" name="verticalLayout_4">
          <property name="spacing">
           <number>8</number>
          </property>
          <item>
           <layout class="QHBoxLayout" name="x_layout" stretch="0,0,1,0">
            <item>
             <widget class="QLabel" name="x_label">
              <property name="minimumSize">
               <size>
                <width>30</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>30</width>
                <height>40</height>
               </size>
              </property>
              <property name="text">
               <string>X</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="x_minus">
              <property name="minimumSize">
               <size>
                <width>50</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>50</width>
                <height>40</height>
               </size>
              </property>
              <property name="text">
               <string>-</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLineEdit" name="x_value">
              <property name="minimumSize">
               <size>
                <width>100</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>150</width>
                <height>40</height>
               </size>
              </property>
              <property name="text">
               <string>0.0</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
              <property name="readOnly">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="x_plus">
              <property name="minimumSize">
               <size>
                <width>50</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>50</width>
                <height>40</height>
               </size>
              </property>
              <property name="text">
               <string>+</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QHBoxLayout" name="y_layout" stretch="0,0,1,0">
            <item>
             <widget class="QLabel" name="y_label">
              <property name="minimumSize">
               <size>
                <width>30</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>30</width>
                <height>40</height>
               </size>
              </property>
              <property name="text">
               <string>Y</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="y_minus">
              <property name="minimumSize">
               <size>
                <width>50</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>50</width>
                <height>40</height>
               </size>
              </property>
              <property name="text">
               <string>-</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLineEdit" name="y_value">
              <property name="minimumSize">
               <size>
                <width>100</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>150</width>
                <height>40</height>
               </size>
              </property>
              <property name="text">
               <string>0.0</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
              <property name="readOnly">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="y_plus">
              <property name="minimumSize">
               <size>
                <width>50</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>50</width>
                <height>40</height>
               </size>
              </property>
              <property name="text">
               <string>+</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QHBoxLayout" name="z_layout" stretch="0,0,1,0">
            <item>
             <widget class="QLabel" name="z_label">
              <property name="minimumSize">
               <size>
                <width>30</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>30</width>
                <height>40</height>
               </size>
              </property>
              <property name="text">
               <string>Z</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="z_minus">
              <property name="minimumSize">
               <size>
                <width>50</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>50</width>
                <height>40</height>
               </size>
              </property>
              <property name="text">
               <string>-</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLineEdit" name="z_value">
              <property name="minimumSize">
               <size>
                <width>100</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>150</width>
                <height>40</height>
               </size>
              </property>
              <property name="text">
               <string>0.0</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
              <property name="readOnly">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="z_plus">
              <property name="minimumSize">
               <size>
                <width>50</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>50</width>
                <height>40</height>
               </size>
              </property>
              <property name="text">
               <string>+</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QHBoxLayout" name="rx_layout" stretch="0,0,1,0">
            <item>
             <widget class="QLabel" name="rx_label">
              <property name="minimumSize">
               <size>
                <width>30</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>30</width>
                <height>40</height>
               </size>
              </property>
              <property name="text">
               <string>RX</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="rx_minus">
              <property name="minimumSize">
               <size>
                <width>50</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>50</width>
                <height>40</height>
               </size>
              </property>
              <property name="text">
               <string>-</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLineEdit" name="rx_value">
              <property name="minimumSize">
               <size>
                <width>100</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>150</width>
                <height>40</height>
               </size>
              </property>
              <property name="text">
               <string>0.0</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
              <property name="readOnly">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="rx_plus">
              <property name="minimumSize">
               <size>
                <width>50</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>50</width>
                <height>40</height>
               </size>
              </property>
              <property name="text">
               <string>+</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QHBoxLayout" name="ry_layout" stretch="0,0,1,0">
            <item>
             <widget class="QLabel" name="ry_label">
              <property name="minimumSize">
               <size>
                <width>30</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>30</width>
                <height>40</height>
               </size>
              </property>
              <property name="text">
               <string>RY</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="ry_minus">
              <property name="minimumSize">
               <size>
                <width>50</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>50</width>
                <height>40</height>
               </size>
              </property>
              <property name="text">
               <string>-</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLineEdit" name="ry_value">
              <property name="minimumSize">
               <size>
                <width>100</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>150</width>
                <height>40</height>
               </size>
              </property>
              <property name="text">
               <string>0.0</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
              <property name="readOnly">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="ry_plus">
              <property name="minimumSize">
               <size>
                <width>50</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>50</width>
                <height>40</height>
               </size>
              </property>
              <property name="text">
               <string>+</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QHBoxLayout" name="rz_layout" stretch="0,0,1,0">
            <item>
             <widget class="QLabel" name="rz_label">
              <property name="minimumSize">
               <size>
                <width>30</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>30</width>
                <height>40</height>
               </size>
              </property>
              <property name="text">
               <string>RZ</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="rz_minus">
              <property name="minimumSize">
               <size>
                <width>50</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>50</width>
                <height>40</height>
               </size>
              </property>
              <property name="text">
               <string>-</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLineEdit" name="rz_value">
              <property name="minimumSize">
               <size>
                <width>100</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>150</width>
                <height>40</height>
               </size>
              </property>
              <property name="text">
               <string>0.0</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
              <property name="readOnly">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="rz_plus">
              <property name="minimumSize">
               <size>
                <width>50</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>50</width>
                <height>40</height>
               </size>
              </property>
              <property name="text">
               <string>+</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </item>
        <item>
         <spacer name="verticalSpacer_1">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Minimum" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>1</verstretch>
           </sizepolicy>
          </property>
          <property name="orientation">
           <enum>Qt::Vertical</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>40</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <layout class="QVBoxLayout" name="verticalLayout_5">
          <property name="spacing">
           <number>5</number>
          </property>
          <item>
           <layout class="QHBoxLayout" name="a1_layout" stretch="0,0,1,0">
            <item>
             <widget class="QLabel" name="a1_label">
              <property name="minimumSize">
               <size>
                <width>30</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>30</width>
                <height>40</height>
               </size>
              </property>
              <property name="text">
               <string>A1</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="a1_minus">
              <property name="minimumSize">
               <size>
                <width>50</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>50</width>
                <height>40</height>
               </size>
              </property>
              <property name="text">
               <string>-</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLineEdit" name="a1_value">
              <property name="minimumSize">
               <size>
                <width>100</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>150</width>
                <height>40</height>
               </size>
              </property>
              <property name="text">
               <string>0.0</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
              <property name="readOnly">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="a1_plus">
              <property name="minimumSize">
               <size>
                <width>50</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>50</width>
                <height>40</height>
               </size>
              </property>
              <property name="text">
               <string>+</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QHBoxLayout" name="a2_layout" stretch="0,0,1,0">
            <item>
             <widget class="QLabel" name="a2_label">
              <property name="minimumSize">
               <size>
                <width>30</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>30</width>
                <height>40</height>
               </size>
              </property>
              <property name="text">
               <string>A2</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="a2_minus">
              <property name="minimumSize">
               <size>
                <width>50</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>50</width>
                <height>40</height>
               </size>
              </property>
              <property name="text">
               <string>-</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLineEdit" name="a2_value">
              <property name="minimumSize">
               <size>
                <width>100</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>150</width>
                <height>40</height>
               </size>
              </property>
              <property name="text">
               <string>0.0</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
              <property name="readOnly">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="a2_plus">
              <property name="minimumSize">
               <size>
                <width>50</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>50</width>
                <height>40</height>
               </size>
              </property>
              <property name="text">
               <string>+</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QHBoxLayout" name="a3_layout" stretch="0,0,1,0">
            <item>
             <widget class="QLabel" name="a3_label">
              <property name="minimumSize">
               <size>
                <width>30</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>30</width>
                <height>40</height>
               </size>
              </property>
              <property name="text">
               <string>A3</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="a3_minus">
              <property name="minimumSize">
               <size>
                <width>50</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>50</width>
                <height>40</height>
               </size>
              </property>
              <property name="text">
               <string>-</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLineEdit" name="a3_value">
              <property name="minimumSize">
               <size>
                <width>100</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>150</width>
                <height>40</height>
               </size>
              </property>
              <property name="text">
               <string>0.0</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
              <property name="readOnly">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="a3_plus">
              <property name="minimumSize">
               <size>
                <width>50</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>50</width>
                <height>40</height>
               </size>
              </property>
              <property name="text">
               <string>+</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QHBoxLayout" name="a4_layout" stretch="0,0,1,0">
            <item>
             <widget class="QLabel" name="a4_label">
              <property name="minimumSize">
               <size>
                <width>30</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>30</width>
                <height>40</height>
               </size>
              </property>
              <property name="text">
               <string>A4</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="a4_minus">
              <property name="minimumSize">
               <size>
                <width>50</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>50</width>
                <height>40</height>
               </size>
              </property>
              <property name="text">
               <string>-</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLineEdit" name="a4_value">
              <property name="minimumSize">
               <size>
                <width>100</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>150</width>
                <height>40</height>
               </size>
              </property>
              <property name="text">
               <string>0.0</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
              <property name="readOnly">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="a4_plus">
              <property name="minimumSize">
               <size>
                <width>50</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>50</width>
                <height>40</height>
               </size>
              </property>
              <property name="text">
               <string>+</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QHBoxLayout" name="a5_layout" stretch="0,0,1,0">
            <item>
             <widget class="QLabel" name="a5_label">
              <property name="minimumSize">
               <size>
                <width>30</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>30</width>
                <height>40</height>
               </size>
              </property>
              <property name="text">
               <string>A5</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="a5_minus">
              <property name="minimumSize">
               <size>
                <width>50</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>50</width>
                <height>40</height>
               </size>
              </property>
              <property name="text">
               <string>-</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLineEdit" name="a5_value">
              <property name="minimumSize">
               <size>
                <width>100</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>150</width>
                <height>40</height>
               </size>
              </property>
              <property name="text">
               <string>0.0</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
              <property name="readOnly">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="a5_plus">
              <property name="minimumSize">
               <size>
                <width>50</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>50</width>
                <height>40</height>
               </size>
              </property>
              <property name="text">
               <string>+</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QHBoxLayout" name="a6_layout" stretch="0,0,1,0">
            <item>
             <widget class="QLabel" name="a6_label">
              <property name="minimumSize">
               <size>
                <width>30</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>30</width>
                <height>40</height>
               </size>
              </property>
              <property name="text">
               <string>A6</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="a6_minus">
              <property name="minimumSize">
               <size>
                <width>50</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>50</width>
                <height>40</height>
               </size>
              </property>
              <property name="text">
               <string>-</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLineEdit" name="a6_value">
              <property name="minimumSize">
               <size>
                <width>100</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>150</width>
                <height>40</height>
               </size>
              </property>
              <property name="text">
               <string>0.0</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
              <property name="readOnly">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="a6_plus">
              <property name="minimumSize">
               <size>
                <width>50</width>
                <height>40</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>50</width>
                <height>40</height>
               </size>
              </property>
              <property name="text">
               <string>+</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </item>
       </layout>
      </item>
     </layout>
     <widget class="QLabel" name="aux_label">
      <property name="geometry">
       <rect>
        <x>10</x>
        <y>516</y>
        <width>36</width>
        <height>16</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>-1</pointsize>
        <bold>true</bold>
       </font>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
