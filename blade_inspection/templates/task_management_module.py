#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import os
import json
import time
from datetime import datetime
from PyQt5.QtWidgets import (QMessageBox, QTableWidgetItem, QPushButton,
                             QProgressBar, QLabel, QWidget, QHBoxLayout,
                             QVBoxLayout, QDialog, QLineEdit, QComboBox,
                             QTextEdit, QGroupBox, QGridLayout, QFileDialog)
from PyQt5.QtCore import Qt, pyqtSignal, QObject, QTimer
from PyQt5.QtGui import QPixmap
from PyQt5 import QtWidgets, uic
from blade_inspection.templates.task_detail_widget import FunctionalTaskDetailWidget


class TaskManagementModule(QObject):
    """检测任务管理功能模块"""
    
    # 定义信号
    task_created = pyqtSignal(dict)  # 任务创建信号
    task_started = pyqtSignal(str)  # 任务启动信号
    task_completed = pyqtSignal(str)  # 任务完成信号
    task_deleted = pyqtSignal(str)  # 任务删除信号
    task_progress_updated = pyqtSignal(str, int)  # 任务进度更新信号
    
    def __init__(self, main_window):
        """初始化任务管理模块"""
        super().__init__()
        self.main_window = main_window

        # 任务相关属性
        self.task_data_file = "task_data.json"  # 任务数据持久化文件
        self.task_queue_data = []  # 任务队列数据
        self.current_running_task = None  # 当前运行的任务

        # 任务状态映射（只有两种状态：运行中、完成）
        self.task_status = {
            "running": "运行中",
            "completed": "完成"
        }

        # 进度更新定时器
        self.progress_timer = QTimer()
        self.progress_timer.timeout.connect(self.update_task_progress)

        # 当前选中的任务文件夹信息
        self.current_task_info = None  # 当前加载的task_info.json数据
        self.current_folder_path = None  # 当前选择的文件夹路径
        self.selected_task_folder = None
        self.selected_task_info = None

        # 初始化InspectService
        self.inspect_service = None
        self.inspect_service_available = False
        try:
            from blade_inspection.core.inspect_service import InspectService
            from hdmtv.core.cdi import get_instance
            self.inspect_service = get_instance(clazz=InspectService)
            self.inspect_service_available = True
            print("✅ InspectService初始化成功")
        except Exception as e:
            print(f"⚠️ InspectService初始化失败: {e}")
            self.inspect_service = None
            self.inspect_service_available = False

        # 加载任务数据
        self.load_task_data()

        # 设置任务数量输入框验证
        self.setup_task_quantity_validation()

        # 设置任务参数控件
        self.setup_task_params_controls()

    def init_task_queue_table(self):
        """初始化任务队列表格"""
        try:
            if not self.main_window.task_queue_table:
                print("任务队列表格组件未找到")
                return

            # 设置表格属性
            self.main_window.task_queue_table.setAlternatingRowColors(True)
            self.main_window.task_queue_table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
            self.main_window.task_queue_table.verticalHeader().setVisible(False)
            self.main_window.task_queue_table.setEditTriggers(QtWidgets.QAbstractItemView.NoEditTriggers)

            # 设置表格列数和标题
            if self.main_window.task_queue_table.columnCount() != 6:
                self.main_window.task_queue_table.setColumnCount(6)
                headers = ["任务ID", "零件编号", "配方", "创建时间", "状态", "操作"]
                self.main_window.task_queue_table.setHorizontalHeaderLabels(headers)

            # 设置列宽
            header = self.main_window.task_queue_table.horizontalHeader()
            if header:
                # 设置前5列为拉伸模式
                for i in range(5):
                    header.setSectionResizeMode(i, QtWidgets.QHeaderView.Stretch)

                # 最后一列（操作列）设置为固定宽度
                header.setSectionResizeMode(5, QtWidgets.QHeaderView.Fixed)
                self.main_window.task_queue_table.setColumnWidth(5, 280)  # 增加操作栏宽度，让按钮不拥挤

            # 更新任务队列显示
            self.update_task_queue_table()
            
            print("任务队列表格初始化完成")

        except Exception as e:
            print(f"任务队列表格初始化失败: {e}")
            import traceback
            traceback.print_exc()

    def load_task_data(self):
        """加载任务数据"""
        try:
            if not os.path.exists(self.task_data_file):
                print("任务数据文件不存在，使用默认数据")
                self.create_default_tasks()
                return

            with open(self.task_data_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            self.task_queue_data = data.get('tasks', [])
            last_updated = data.get('last_updated', '未知')

            print(f"已加载 {len(self.task_queue_data)} 个任务，最后更新时间: {last_updated}")

        except Exception as e:
            print(f"加载任务数据失败: {e}")
            self.create_default_tasks()

    def create_default_tasks(self):
        """创建默认任务数据（只有运行中和完成两种状态）"""
        self.task_queue_data = [
            {
                "task_id": "TASK_1234567890",
                "part_number": "BL-001",
                "program_name": "配方A",
                "created_time": "2023-05-15 09:30",
                "progress": 75,
                "status": "running",  # 唯一运行中的任务
                "task_info": {
                    "operator": "操作员A",
                    "batch_number": "BATCH_001",
                    "estimated_time": "30分钟"
                }
            },
            {
                "task_id": "TASK_1234567891",
                "part_number": "BL-002",
                "program_name": "配方B",
                "created_time": "2023-05-15 10:15",
                "progress": 100,
                "status": "completed",
                "task_info": {
                    "operator": "操作员B",
                    "batch_number": "BATCH_002",
                    "estimated_time": "45分钟"
                }
            },
            {
                "task_id": "TASK_1234567892",
                "part_number": "BL-003",
                "program_name": "配方C",
                "created_time": "2023-05-15 11:00",
                "progress": 100,
                "status": "completed",
                "task_info": {
                    "operator": "操作员C",
                    "batch_number": "BATCH_003",
                    "estimated_time": "25分钟"
                }
            },
            {
                "task_id": "TASK_1234567893",
                "part_number": "BL-004",
                "program_name": "配方D",
                "created_time": "2023-05-15 12:30",
                "progress": 100,
                "status": "completed",
                "task_info": {
                    "operator": "操作员D",
                    "batch_number": "BATCH_004",
                    "estimated_time": "35分钟"
                }
            }
        ]
        self.save_task_data()

    def save_task_data(self):
        """保存任务数据到文件"""
        try:
            data = {
                'tasks': self.task_queue_data,
                'last_updated': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

            with open(self.task_data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

            print("✅ 任务数据已保存")

        except Exception as e:
            print(f"保存任务数据失败: {e}")

    def update_task_queue_table(self):
        """更新任务队列表格显示"""
        try:
            if not self.main_window.task_queue_table:
                return

            # 设置行数
            self.main_window.task_queue_table.setRowCount(len(self.task_queue_data))

            # 填充数据
            for row, task_data in enumerate(self.task_queue_data):
                # 设置行高
                self.main_window.task_queue_table.setRowHeight(row, 80)

                # 填充基本数据
                basic_data = [
                    task_data.get("task_id", f"TASK_{row + 1:03d}"),
                    task_data.get("part_number", "未知零件"),
                    task_data.get("program_name", "未知配方"),
                    task_data.get("create_time", "未知时间")  # 修正字段名
                ]

                for col, value in enumerate(basic_data):
                    item = QTableWidgetItem(str(value))
                    item.setTextAlignment(Qt.AlignCenter)
                    item.setFlags(item.flags() & ~Qt.ItemIsEditable)
                    self.main_window.task_queue_table.setItem(row, col, item)

                # 添加状态指示器
                status = task_data.get("status", "waiting")
                status_text = self.task_status.get(status, "未知")
                self.add_status_indicator(row, 4, status_text)

                # 添加操作按钮
                self.add_task_action_buttons(row)

            print(f"任务队列表格已更新，显示 {len(self.task_queue_data)} 个任务")

        except Exception as e:
            print(f"更新任务队列表格失败: {e}")
            import traceback
            traceback.print_exc()

    def add_progress_bar(self, row, col, progress):
        """添加进度条 - 已禁用"""
        # 进度条显示功能已被移除
        pass

    def add_status_indicator(self, row, col, status_text):
        """添加状态指示器"""
        try:
            widget = QWidget()
            layout = QHBoxLayout(widget)
            layout.setContentsMargins(5, 5, 5, 5)
            
            status_label = QLabel(status_text)
            status_label.setAlignment(Qt.AlignCenter)
            
            # 根据状态设置颜色（只有运行中和完成两种状态）
            if status_text == "运行中":
                status_label.setStyleSheet("""
                    QLabel {
                        background-color: #3498db;
                        color: white;
                        border-radius: 10px;
                        padding: 4px 8px;
                        font-size: 10px;
                    }
                """)
            elif status_text == "完成":
                status_label.setStyleSheet("""
                    QLabel {
                        background-color: #27ae60;
                        color: white;
                        border-radius: 10px;
                        padding: 4px 8px;
                        font-size: 10px;
                    }
                """)
            else:
                # 默认样式
                status_label.setStyleSheet("""
                    QLabel {
                        background-color: #95a5a6;
                        color: white;
                        border-radius: 10px;
                        padding: 4px 8px;
                        font-size: 10px;
                    }
                """)
            
            layout.addWidget(status_label)
            self.main_window.task_queue_table.setCellWidget(row, col, widget)
            
        except Exception as e:
            print(f"添加状态指示器失败: {e}")

    def add_task_action_buttons(self, row):
        """添加任务操作按钮"""
        try:
            # 创建按钮容器
            widget = QWidget()
            layout = QHBoxLayout(widget)
            layout.setContentsMargins(8, 8, 8, 8)
            layout.setSpacing(5)

            # 删除按钮
            delete_btn = QPushButton("删除")

            # 查看按钮
            view_btn = QPushButton("查看")

            # 设置按钮样式（参照test_blade_ui.py）
            button_style = """
                QPushButton {
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 8px 12px;
                    font-size: 11px;
                    font-weight: bold;
                    min-width: 45px;
                    min-height: 25px;
                }
                QPushButton:hover {
                    opacity: 0.8;
                }
            """

            delete_btn.setStyleSheet(button_style + "QPushButton { background-color: #e74c3c; } QPushButton:hover { background-color: #c0392b; }")
            view_btn.setStyleSheet(button_style + "QPushButton { background-color: #27ae60; } QPushButton:hover { background-color: #229954; }")

            # 连接信号
            delete_btn.clicked.connect(lambda: self.delete_task(row))
            view_btn.clicked.connect(lambda: self.view_task_detail(row))

            layout.addWidget(delete_btn)
            layout.addWidget(view_btn)

            # 设置到表格的最后一列
            self.main_window.task_queue_table.setCellWidget(row, 5, widget)

        except Exception as e:
            print(f"添加任务操作按钮失败: {e}")

    def select_task_folder(self):
        """选择任务文件夹并读取task_info.json"""
        try:
            print(f"🔍 select_task_folder 被调用")
            print(f"   - 实例ID: {id(self)}")
            print(f"   - 主窗口: {self.main_window}")

            from PyQt5.QtWidgets import QFileDialog
            import json
            import os

            # 默认打开指定目录
            default_path = r"D:\blade_inspect\task"
            if not os.path.exists(default_path):
                default_path = os.getcwd()

            print(f"🔍 打开文件夹选择对话框，默认路径: {default_path}")

            folder_path = QFileDialog.getExistingDirectory(
                self.main_window,
                "选择任务文件夹",
                default_path,
                QFileDialog.ShowDirsOnly | QFileDialog.DontResolveSymlinks
            )

            if folder_path:
                task_info_path = os.path.join(folder_path, "task_info.json")
                if not os.path.exists(task_info_path):
                    QMessageBox.warning(self.main_window, "文件不存在",
                        f"在选择的文件夹中未找到 task_info.json 文件\n\n路径: {task_info_path}")
                    return
                with open(task_info_path, 'r', encoding='utf-8') as f:
                    task_info_data = json.load(f)
                print("task_info.json 加载成功")

                self.update_ui_from_task_info(task_info_data, folder_path)
                QMessageBox.information(self.main_window, "加载成功",
                    f"任务信息加载成功！\n\n产品名称: {task_info_data.get('ProductName', '未知')}\n文件夹: {folder_path}")
        except json.JSONDecodeError as e:
            QMessageBox.critical(self.main_window, "文件格式错误", f"task_info.json 文件格式不正确:\n\n{str(e)}")
        except Exception as e:
            print(f"选择文件夹失败: {e}")
            QMessageBox.warning(self.main_window, "错误", f"选择文件夹失败: {e}")

    def update_ui_from_task_info(self, task_info_data, folder_path):
        """根据task_info.json更新界面显示"""
        try:
            if hasattr(self.main_window, 'part_number_edit') and self.main_window.part_number_edit:
                product_name = task_info_data.get('ProductName', '')
                self.main_window.part_number_edit.setText(product_name)
            if hasattr(self.main_window, 'task_info_edit') and self.main_window.task_info_edit:
                info_text = "【任务详情】\n"
                info_text += f"产线名称: {task_info_data.get('ProductLineName', '未知')}\n"
                info_text += f"任务名称: {task_info_data.get('MOTaskName', '未知')}\n"
                info_text += f"工单名称: {task_info_data.get('MOName', '未知')}\n"
                info_text += f"工序名称: {task_info_data.get('SpecificationName', '未知')}\n"
                info_text += f"产品名称: {task_info_data.get('ProductName', '未知')}\n"
                info_text += f"批号: {task_info_data.get('LotSN', '未知')}\n"
                info_text += f"任务数量: {task_info_data.get('TaskQty', '未知')}\n"
                info_text += f"创建时间: {task_info_data.get('create_time', '未知')}\n"
                info_text += f"操作员: {task_info_data.get('operator', '未知')}\n"
                info_text += f"文件夹路径: {folder_path}"
                self.main_window.task_info_edit.setPlainText(info_text)
            # 存储任务信息供后续使用
            print(f"🔄 准备保存任务信息...")
            print(f"   - task_info_data类型: {type(task_info_data)}")
            print(f"   - task_info_data内容: {task_info_data}")
            print(f"   - folder_path: {folder_path}")

            self.current_task_info = task_info_data
            self.current_folder_path = folder_path

            print(f"✅ 任务信息已保存:")
            print(f"   - self.current_task_info: {self.current_task_info}")
            print(f"   - self.current_folder_path: {self.current_folder_path}")
            print(f"   - current_task_info is not None: {self.current_task_info is not None}")
            print(f"   - ProductName: {task_info_data.get('ProductName', '未知')}")

        except Exception as e:
            print(f"更新界面失败: {e}")
            import traceback
            traceback.print_exc()

    def start_task(self):
        """启动任务 - 检查文件夹选择并切换到任务详情界面"""
        try:
            print(f"🔍 start_task 被调用")
            print(f"🔍 开始检查任务信息...")
            print(f"   - hasattr current_task_info: {hasattr(self, 'current_task_info')}")
            print(f"   - current_task_info: {getattr(self, 'current_task_info', 'NOT_SET')}")
            print(f"   - hasattr current_folder_path: {hasattr(self, 'current_folder_path')}")
            print(f"   - current_folder_path: {getattr(self, 'current_folder_path', 'NOT_SET')}")
            print(f"   - hasattr selected_task_info: {hasattr(self, 'selected_task_info')}")
            print(f"   - selected_task_info: {getattr(self, 'selected_task_info', 'NOT_SET')}")
            print(f"   - hasattr selected_task_folder: {hasattr(self, 'selected_task_folder')}")
            print(f"   - selected_task_folder: {getattr(self, 'selected_task_folder', 'NOT_SET')}")

            # 检查是否已经选择了文件夹 - 检查两组变量
            task_info = None
            folder_path = None

            if hasattr(self, 'current_task_info') and self.current_task_info:
                task_info = self.current_task_info
                folder_path = getattr(self, 'current_folder_path', None)
                print("✅ 使用 current_task_info")
            elif hasattr(self, 'selected_task_info') and self.selected_task_info:
                task_info = self.selected_task_info
                folder_path = getattr(self, 'selected_task_folder', None)
                print("✅ 使用 selected_task_info")

            if not task_info:
                print("❌ 未找到任务信息")
                QMessageBox.warning(self.main_window, "未选择任务",
                    "请先点击'选择文件夹'按钮选择任务文件夹！")
                return

            if not folder_path:
                print("❌ 未找到文件夹路径")
                QMessageBox.warning(self.main_window, "未选择文件夹",
                    "请先点击'选择文件夹'按钮选择任务文件夹！")
                return

            # 获取程序号
            program_name = task_info.get('ProductName', '未知程序')

            print(f"启动任务: {program_name}")
            print(f"文件夹路径: {folder_path}")

            # 检查是否有正在运行的任务
            if self.check_running_tasks():
                QMessageBox.warning(self.main_window, "任务冲突",
                    "已有正在运行的任务，请等待当前任务完成后再启动新任务！")
                return

            # 将当前任务添加到任务队列
            task_added = self.add_task_to_queue(program_name, task_info, folder_path)
            if not task_added:
                QMessageBox.warning(self.main_window, "添加失败",
                    "无法将任务添加到队列中，请稍后重试！")
                return

            # 切换到检测任务页面
            if hasattr(self.main_window, 'switch_page'):
                self.main_window.switch_page(3)  # 切换到检测任务页面

            # 切换到任务详情标签页
            if hasattr(self.main_window, 'task_tab_widget') and self.main_window.task_tab_widget:
                self.main_window.task_tab_widget.setCurrentIndex(1)  # 切换到任务详情标签页

            # 加载任务详情界面，显示对应程序号的windows01.ui
            self.load_task_detail_ui_for_program(program_name, folder_path)

            # 显示任务启动对话框
            msg_box = QMessageBox(self.main_window)
            msg_box.setWindowTitle("任务启动")
            msg_box.setText(f"任务 '{program_name}' 已启动！\n已添加到任务队列并正在显示任务详情界面...")
            msg_box.setStandardButtons(QMessageBox.Ok)
            msg_box.exec_()

            # 对话框显示完全后，调用inspect_service的load_request函数
            if self.inspect_service_available and self.inspect_service:
                try:
                    # 从文件夹路径提取任务名称
                    task_name = os.path.basename(folder_path)
                    print(f"🔄 调用load_request，任务名称: {task_name}")

                    result_code, json_data = self.inspect_service.load_request(task_name)

                    if result_code == 0:
                        print(f"✅ load_request调用成功")

                        # 获取任务数量 - 从用户输入的数量框获取
                        batch_num = 1  # 默认值
                        try:
                            if hasattr(self.main_window, 'task_quantity_edit') and self.main_window.task_quantity_edit:
                                quantity_text = self.main_window.task_quantity_edit.text().strip()
                                if quantity_text:
                                    batch_num = int(quantity_text)
                                    print(f"✅ 从用户输入框获取任务数量: {batch_num}")
                                else:
                                    print(f"⚠️ 用户输入框为空，使用默认值: {batch_num}")
                            else:
                                print(f"⚠️ 未找到任务数量输入框，使用默认值: {batch_num}")
                        except ValueError:
                            print(f"⚠️ 用户输入的数量格式无效，使用默认值: {batch_num}")
                        except Exception as e:
                            print(f"⚠️ 获取用户输入数量时出错: {e}，使用默认值: {batch_num}")

                        print(f"🔄 调用run_batch，批次数量: {batch_num}")

                        # 调用run_batch函数
                        run_result = self.inspect_service.run_batch(batch_num=batch_num)

                        if run_result == 0:
                            print(f"✅ run_batch调用成功")
                        else:
                            print(f"❌ run_batch调用失败，错误码: {run_result}")
                    else:
                        print(f"❌ load_request调用失败，错误码: {result_code}")

                except Exception as e:
                    print(f"❌ 调用inspect_service失败: {e}")
                    import traceback
                    traceback.print_exc()
            else:
                print("⚠️ InspectService不可用，跳过load_request和run_batch调用")

        except Exception as e:
            QMessageBox.critical(self.main_window, "启动失败", f"任务启动失败：{str(e)}")
            print(f"任务启动失败: {e}")
            import traceback
            traceback.print_exc()

    def check_running_tasks(self):
        """检查是否有正在运行的任务"""
        try:
            print("🔍 检查正在运行的任务...")

            if not hasattr(self.main_window, 'task_queue_table') or not self.main_window.task_queue_table:
                print("❌ 未找到任务队列表格")
                return False

            # 遍历任务队列表格，检查状态列
            for row in range(self.main_window.task_queue_table.rowCount()):
                status_widget = self.main_window.task_queue_table.cellWidget(row, 4)  # 状态列
                if status_widget:
                    # 查找状态标签
                    for i in range(status_widget.layout().count()):
                        item = status_widget.layout().itemAt(i)
                        if item and item.widget():
                            widget = item.widget()
                            if isinstance(widget, QtWidgets.QLabel) and hasattr(widget, 'text'):
                                status_text = widget.text()
                                if status_text == "运行中":
                                    task_name = self.main_window.task_queue_table.item(row, 1).text() if self.main_window.task_queue_table.item(row, 1) else f"任务{row+1}"
                                    print(f"❌ 发现正在运行的任务: {task_name}")
                                    return True

            print("✅ 没有正在运行的任务")
            return False

        except Exception as e:
            print(f"检查运行任务失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def add_task_to_queue(self, program_name, task_info, folder_path):
        """将任务添加到任务队列"""
        try:
            print(f"📝 添加任务到队列: {program_name}")

            # 生成新的任务ID
            import datetime
            task_id = f"TASK_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"

            # 构建任务数据
            new_task = {
                "task_id": task_id,
                "task_name": program_name,
                "program_name": program_name,
                "part_number": task_info.get('ProductName', program_name),
                "task_count": task_info.get('TaskQty', 1),
                "status": "running",
                "progress": 0,
                "create_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "operator": task_info.get('operator', '系统'),
                "folder_path": folder_path,
                "task_info": task_info
            }

            # 添加到任务数据列表
            if not hasattr(self, 'task_queue_data'):
                self.task_queue_data = []

            self.task_queue_data.append(new_task)

            # 更新任务队列表格显示
            self.update_task_queue_table()

            print(f"✅ 任务已添加到队列: {task_id}")
            return True

        except Exception as e:
            print(f"添加任务到队列失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def load_task_detail_ui_for_program(self, program_name, folder_path=None):
        """为指定程序加载任务详情界面"""
        try:
            # 查找任务详情tab页面
            task_detail_tab = self.main_window.findChild(QtWidgets.QWidget, "task_detail_tab")
            if not task_detail_tab:
                QMessageBox.warning(self.main_window, "错误", "找不到任务详情tab页面")
                return

            # 清空任务详情tab的现有内容
            layout = task_detail_tab.layout()
            if layout:
                # 清除所有子控件
                while layout.count():
                    item = layout.takeAt(0)
                    if item.widget():
                        item.widget().deleteLater()
            else:
                # 创建新布局
                layout = QtWidgets.QVBoxLayout(task_detail_tab)
                layout.setContentsMargins(20, 20, 20, 20)
                layout.setSpacing(0)

            # 加载blade.ui界面 - 仿照查看功能的实现
            detail_ui = self.load_blade_ui_for_task_start(layout, program_name, folder_path)

            # 设置工具栏元素（在UI加载之后，传入detail_ui）
            if detail_ui:
                self.setup_toolbar_elements(program_name, folder_path, detail_ui)

        except Exception as e:
            print(f"加载任务详情界面失败: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.warning(self.main_window, "错误", f"加载任务详情界面失败: {e}")

    def setup_toolbar_elements(self, program_name, folder_path, detail_ui=None):
        """设置工具栏元素（从UI文件中查找并更新内容）"""
        try:
            # 确定在哪个widget中查找元素
            search_widget = detail_ui if detail_ui else self.main_window

            # 查找UI文件中的工具栏元素
            task_back_button = search_widget.findChild(QPushButton, "task_back_button")
            task_program_label = search_widget.findChild(QtWidgets.QLabel, "task_program_label")
            task_folder_label = search_widget.findChild(QtWidgets.QLabel, "task_folder_label")

            # 检查元素是否找到
            elements_found = []
            if task_back_button:
                elements_found.append("返回按钮")
            if task_program_label:
                elements_found.append("程序信息标签")
            if task_folder_label:
                elements_found.append("文件夹信息标签")

            if elements_found:
                print(f"🔍 找到工具栏元素: {', '.join(elements_found)}")
            else:
                print("❌ 未在UI文件中找到任何工具栏元素")
                print("请在UI文件中确保存在以下元素:")
                print("- task_back_button (返回按钮)")
                print("- task_program_label (程序信息标签)")
                print("- task_folder_label (文件夹信息标签)")
                return

            # 设置返回按钮
            if task_back_button:
                task_back_button.clicked.connect(self.return_to_task_list)
                print("✅ 返回按钮信号已连接")

            # 设置程序信息标签
            if task_program_label:
                task_program_label.setText(f"任务详情 - 程序: {program_name}")

            # 设置文件夹信息标签
            if task_folder_label:
                folder_display = folder_path or getattr(self, 'current_folder_path', None) or getattr(self, 'selected_task_folder', '未知路径')
                task_folder_label.setText(f"文件夹: {folder_display}")

            print("✅ 工具栏元素设置完成")

        except Exception as e:
            print(f"❌ 设置工具栏元素失败: {e}")
            import traceback
            traceback.print_exc()

    def load_blade_ui_for_task_start(self, parent_layout, program_name, folder_path):
        """为任务启动加载blade.ui界面 - 仿照查看功能"""
        try:
            from PyQt5 import uic
            import os

            # 查找UI文件路径 - 完全仿照查看功能的路径查找
            ui_file_paths = [
                os.path.join("blade", "static", "ui", "windows01.ui"),
                os.path.join("blade", "static", "ui", "11window.ui"),
                os.path.join("templates", "windows01.ui"),
                os.path.join("templates", "11window.ui"),
                "windows01.ui",
                "11window.ui"
            ]

            ui_file_path = None
            for path in ui_file_paths:
                if os.path.exists(path):
                    ui_file_path = path
                    break

            if not ui_file_path:
                QMessageBox.warning(self.main_window, "文件不存在",
                    f"找不到UI文件，已尝试路径：\n" + "\n".join(ui_file_paths))
                return

            # 创建容器用于放置blade.ui
            detail_container = QtWidgets.QWidget()

            # 创建功能性任务详情控件 - 仿照查看功能
            task_data = {
                "task_id": program_name,
                "program_name": program_name,
                "folder_path": folder_path
            }
            detail_ui = self.create_functional_task_detail_widget(ui_file_path, program_name, task_data)

            # 设置容器布局
            container_layout = QtWidgets.QVBoxLayout(detail_container)
            container_layout.setContentsMargins(0, 0, 0, 0)
            container_layout.addWidget(detail_ui)

            # 添加到父布局
            parent_layout.addWidget(detail_container)

            print(f"任务启动blade.ui界面加载成功，程序: {program_name}")

            # 返回创建的detail_ui，以便后续设置工具栏元素
            return detail_ui

        except Exception as e:
            print(f"任务启动加载blade.ui界面失败: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.warning(self.main_window, "错误", f"任务启动加载blade.ui界面失败: {e}")
            return None

    def load_blade_ui_in_detail_tab(self, parent_layout, program_name):
        """在任务详情tab中加载blade.ui界面"""
        try:
            # 查找blade.ui文件
            ui_file_paths = [
                "blade.ui",
                "blade_inspection/blade.ui",
                "blade_inspection/templates/blade.ui",
                "templates/blade.ui"
            ]

            ui_file_path = None
            for path in ui_file_paths:
                if os.path.exists(path):
                    ui_file_path = path
                    break

            if not ui_file_path:
                QMessageBox.warning(self.main_window, "文件不存在",
                    f"找不到blade.ui文件，已尝试路径：\n" + "\n".join(ui_file_paths))
                return

            # 创建容器widget
            detail_container = QtWidgets.QWidget()

            # 创建一个临时的QMainWindow来加载blade.ui
            temp_main_window = QtWidgets.QMainWindow()
            from PyQt5 import uic
            uic.loadUi(ui_file_path, temp_main_window)

            # 获取中央组件并重新设置父级
            central_widget = temp_main_window.centralwidget
            if central_widget:
                # 将中央组件从临时窗口中移除
                temp_main_window.setCentralWidget(QtWidgets.QWidget())

                # 设置新的父级
                central_widget.setParent(detail_container)

                # 创建布局并添加central_widget
                container_layout = QtWidgets.QVBoxLayout(detail_container)
                container_layout.setContentsMargins(0, 0, 0, 0)
                container_layout.addWidget(central_widget)

                # 设置合适的大小策略
                detail_container.setSizePolicy(
                    QtWidgets.QSizePolicy.Expanding,
                    QtWidgets.QSizePolicy.Expanding
                )

                # 添加到父布局
                parent_layout.addWidget(detail_container)

                print(f"blade.ui界面加载成功，程序: {program_name}")

                # 可以在这里添加特定于程序的初始化逻辑
                self.customize_blade_ui_for_program(central_widget, program_name)

            else:
                QMessageBox.warning(self.main_window, "加载失败", "blade.ui中未找到centralwidget")

        except Exception as e:
            print(f"加载blade.ui界面失败: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.warning(self.main_window, "错误", f"加载blade.ui界面失败: {e}")

    def customize_blade_ui_for_program(self, blade_widget, program_name):
        """为特定程序定制blade.ui界面"""
        try:
            # 这里可以根据程序名称进行特定的界面定制
            print(f"为程序 {program_name} 定制界面")

            # 例如：可以设置特定的标题、隐藏某些控件、设置默认值等
            # 根据需要添加具体的定制逻辑

        except Exception as e:
            print(f"定制界面失败: {e}")

    def return_to_task_list(self):
        """返回任务列表"""
        try:
            # 切换回任务状态标签页
            if hasattr(self.main_window, 'task_tab_widget') and self.main_window.task_tab_widget:
                self.main_window.task_tab_widget.setCurrentIndex(2)  # 切换到任务状态标签页
            print("已返回任务列表")
        except Exception as e:
            print(f"返回任务列表失败: {e}")

    def on_task_created(self, task_data):
        """处理任务创建事件"""
        try:
            part_number = task_data.get('part_number', '')
            program_name = task_data.get('program_name', '')

            # 添加到任务队列
            self.add_task_to_queue_with_info(task_data)

            # 发送信号
            self.task_created.emit(task_data)

            print(f"任务创建成功: {part_number} - {program_name}")

        except Exception as e:
            QMessageBox.critical(self.main_window, "错误", f"处理任务创建失败：{str(e)}")
            print(f"处理任务创建失败: {e}")
            import traceback
            traceback.print_exc()

    def add_task_to_queue_with_info(self, task_data):
        """添加带有详细信息的任务到队列"""
        try:
            # 获取任务信息
            part_number = task_data.get('part_number', '')
            program_name = task_data.get('program_name', '')
            task_info = task_data.get('task_info', {})
            created_time = task_data.get('created_time', datetime.now().strftime("%Y-%m-%d %H:%M:%S"))

            # 生成任务ID
            task_id = f"TASK_{int(time.time())}"

            # 检查是否已有运行中的任务，决定新任务的状态
            current_running_row = self.get_running_task_row()
            if current_running_row != -1:
                # 已有运行中任务，新任务设置为完成状态
                status = "completed"
                progress = 100
            else:
                # 没有运行中任务，新任务设置为运行中
                status = "running"
                progress = 0

            # 创建任务数据
            new_task = {
                "task_id": task_id,
                "part_number": part_number,
                "program_name": program_name,
                "created_time": created_time,
                "progress": progress,
                "status": status,
                "task_info": task_info
            }

            # 添加到队列数据
            self.task_queue_data.append(new_task)

            # 保存数据
            self.save_task_data()

            # 更新表格显示
            self.update_task_queue_table()

            print(f"任务添加到队列: {task_id} - {part_number}")

        except Exception as e:
            print(f"添加任务到队列失败: {e}")
            import traceback
            traceback.print_exc()

    def select_task_folder(self):
        """选择任务文件夹并读取task_info.json"""
        try:
            # 设置默认路径为D:\blade_inspect\task
            default_path = r"D:\blade_inspect\task"
            if not os.path.exists(default_path):
                default_path = os.getcwd()

            # 打开文件夹选择对话框
            folder_path = QFileDialog.getExistingDirectory(
                self.main_window,
                "选择任务文件夹",
                default_path,
                QFileDialog.ShowDirsOnly | QFileDialog.DontResolveSymlinks
            )

            if folder_path:
                print(f"🎯 用户选择了任务文件夹: {folder_path}")

                # 检查文件夹中是否有task_info.json
                task_info_path = os.path.join(folder_path, "task_info.json")
                if os.path.exists(task_info_path):
                    # 读取task_info.json
                    self.load_task_info_from_folder(folder_path, task_info_path)
                else:
                    QMessageBox.warning(
                        self.main_window,
                        "文件不存在",
                        f"选择的文件夹中没有找到 task_info.json 文件\n\n文件夹路径: {folder_path}"
                    )
                    print(f"❌ 文件夹中没有找到task_info.json: {folder_path}")
            else:
                print("用户取消了文件夹选择")

        except Exception as e:
            print(f"选择任务文件夹失败: {e}")
            QMessageBox.critical(
                self.main_window,
                "错误",
                f"选择任务文件夹时发生错误：\n{str(e)}"
            )
            import traceback
            traceback.print_exc()

    def load_task_info_from_folder(self, folder_path, task_info_path):
        """从文件夹中加载task_info.json并更新界面"""
        try:
            print(f"📖 开始读取task_info.json: {task_info_path}")

            # 读取JSON文件
            with open(task_info_path, 'r', encoding='utf-8') as f:
                task_info_data = json.load(f)

            # 保存选中的文件夹和任务信息
            self.selected_task_folder = folder_path
            self.selected_task_info = task_info_data

            # 提取文件夹名称作为件号
            folder_name = os.path.basename(folder_path)
            part_number = folder_name.replace("任务-", "") if folder_name.startswith("任务-") else folder_name

            # 从task_info.json中提取信息
            task_name = task_info_data.get("MOTaskName", "未知任务")

            # 从ProgramFiles中提取程序号
            program_files = task_info_data.get("ProgramFiles", [])
            if program_files and len(program_files) > 0:
                program_file_url = program_files[0].get("ProgramFileURL", "未知程序")
                program_name = program_file_url  # 直接使用文件名作为程序号
            else:
                program_name = "未知程序"

            # 提取其他基本信息
            product_name = task_info_data.get("ProductName", "未知产品")
            lot_sn = task_info_data.get("LotSN", "未知批号")
            mo_name = task_info_data.get("MOName", "未知工单")
            product_line_name = task_info_data.get("ProductLineName", "未知产线")
            order_name = task_info_data.get("OrderName", "未知工序")
            specification_name = task_info_data.get("SpecificationName", "未知工序名称")
            task_qty = task_info_data.get("TaskQty", 0)
            create_time = task_info_data.get("create_time", "未知时间")
            operator = task_info_data.get("operator", "未知操作员")

            # 更新界面组件
            if hasattr(self.main_window, 'part_number_edit') and self.main_window.part_number_edit:
                self.main_window.part_number_edit.setText(part_number)
                print(f"✅ 已更新件号: {part_number}")

            if hasattr(self.main_window, 'task_number_edit') and self.main_window.task_number_edit:
                self.main_window.task_number_edit.setText(task_name)
                print(f"✅ 已更新任务号: {task_name}")

            if hasattr(self.main_window, 'program_number_label_display') and self.main_window.program_number_label_display:
                # 直接设置label文本显示程序文件名
                self.main_window.program_number_label_display.setText(program_name)
                print(f"✅ 已更新程序号: {program_name}")

            if hasattr(self.main_window, 'task_info_edit') and self.main_window.task_info_edit:
                # 构建完整的任务信息显示文本
                info_text = f"【基本信息】\n"
                info_text += f"产线名称: {product_line_name}\n"
                info_text += f"任务名称: {task_name}\n"
                info_text += f"工单名称: {mo_name}\n"
                info_text += f"工序名称: {order_name}\n"
                info_text += f"产品名称: {product_name}\n"
                info_text += f"批号: {lot_sn}\n"
                info_text += f"规格名称: {specification_name}\n"
                info_text += f"任务数量: {task_qty}\n"
                info_text += f"创建时间: {create_time}\n"
                info_text += f"操作员: {operator}\n\n"

                # 添加物料信息
                materials_bom = task_info_data.get("MaterialsBOM", [])
                if materials_bom:
                    info_text += f"【物料信息】\n"
                    for i, material in enumerate(materials_bom):
                        info_text += f"物料{i+1}:\n"
                        info_text += f"  产品名称: {material.get('ProductName', '未知')}\n"
                        info_text += f"  产品描述: {material.get('ProductDescription', '未知')}\n"
                        info_text += f"  材质: {material.get('Material', '未知')}\n"
                        info_text += f"  厚度: {material.get('Thickness', '未知')}\n"
                        info_text += f"  直径: {material.get('Diameter', '未知')}\n"
                    info_text += "\n"

                # 添加程序文件信息
                if program_files:
                    info_text += f"【程序文件】\n"
                    for i, prog_file in enumerate(program_files):
                        info_text += f"程序{i+1}:\n"
                        info_text += f"  程序名称: {prog_file.get('ProgramName', '未知')}\n"
                        info_text += f"  设备名称: {prog_file.get('EquipmentName', '未知')}\n"
                        info_text += f"  程序文件: {prog_file.get('ProgramFileURL', '未知')}\n"

                self.main_window.task_info_edit.setPlainText(info_text)
                print(f"✅ 已更新完整任务信息显示")

            # 设置默认任务数量和速度
            if hasattr(self.main_window, 'task_quantity_edit') and self.main_window.task_quantity_edit:
                task_qty = self.selected_task_info.get('TaskQty', 1)
                self.main_window.task_quantity_edit.setText(str(task_qty))
                print(f"✅ 已设置任务数量: {task_qty}")

            if hasattr(self.main_window, 'task_speed_edit') and self.main_window.task_speed_edit:
                speed = self.selected_task_info.get('Speed', 100)
                self.main_window.task_speed_edit.setText(str(speed))
                print(f"✅ 已设置速度: {speed}")

            print(f"🎉 成功加载任务信息:")
            print(f"   文件夹: {folder_path}")
            print(f"   件号: {part_number}")
            print(f"   任务号: {task_name}")
            print(f"   程序号: {program_name}")
            print(f"   产品名称: {product_name}")

        except Exception as e:
            print(f"加载task_info.json失败: {e}")
            QMessageBox.critical(
                self.main_window,
                "读取失败",
                f"读取task_info.json文件时发生错误：\n{str(e)}\n\n文件路径: {task_info_path}"
            )
            import traceback
            traceback.print_exc()

    def setup_task_quantity_validation(self):
        """设置任务数量输入框验证"""
        try:
            if hasattr(self.main_window, 'task_quantity_edit') and self.main_window.task_quantity_edit:
                # 连接文本变化信号
                self.main_window.task_quantity_edit.textChanged.connect(self.validate_task_quantity)
                print("✅ 已设置任务数量输入框验证")
        except Exception as e:
            print(f"设置任务数量验证失败: {e}")

    def setup_task_params_controls(self):
        """设置任务参数控件"""
        try:
            # 延迟查找控件，确保UI已经完全加载
            from PyQt5.QtCore import QTimer
            QTimer.singleShot(1000, self._delayed_setup_task_params_controls)

        except Exception as e:
            print(f"设置任务参数控件失败: {e}")

    def _delayed_setup_task_params_controls(self):
        """延迟设置任务参数控件"""
        try:
            # 查找速度输入框
            self.main_window.task_speed_edit = self.main_window.findChild(QtWidgets.QLineEdit, "task_speed_edit")

            # 查找应用按钮
            self.main_window.apply_params_btn = self.main_window.findChild(QtWidgets.QPushButton, "apply_params_btn")

            # 连接应用按钮信号
            if self.main_window.apply_params_btn:
                self.main_window.apply_params_btn.clicked.connect(self.apply_task_params)
                print("✅ 已连接任务参数应用按钮")
            else:
                print("❌ 未找到任务参数应用按钮")

            # 设置速度输入框验证
            if self.main_window.task_speed_edit:
                self.main_window.task_speed_edit.textChanged.connect(self.validate_task_speed)
                print("✅ 已设置速度输入框验证")
            else:
                print("❌ 未找到速度输入框")

        except Exception as e:
            print(f"延迟设置任务参数控件失败: {e}")

    def apply_task_params(self):
        """应用任务参数"""
        try:
            # 获取任务数量
            quantity_text = self.main_window.task_quantity_edit.text().strip() if self.main_window.task_quantity_edit else "1"
            try:
                quantity = int(quantity_text) if quantity_text else 1
                if quantity <= 0 or quantity > 72:
                    QMessageBox.warning(self.main_window, "参数错误", "任务数量必须在1-72之间！")
                    return
            except ValueError:
                QMessageBox.warning(self.main_window, "参数错误", "任务数量必须是有效的数字！")
                return

            # 获取速度设置
            speed_text = self.main_window.task_speed_edit.text().strip() if self.main_window.task_speed_edit else "100"
            try:
                speed = int(speed_text) if speed_text else 100
                if speed <= 0 or speed > 200:
                    QMessageBox.warning(self.main_window, "参数错误", "速度必须在1-200之间！")
                    return
            except ValueError:
                QMessageBox.warning(self.main_window, "参数错误", "速度必须是有效的数字！")
                return

            # 更新当前任务信息
            if hasattr(self, 'selected_task_info') and self.selected_task_info:
                self.selected_task_info['TaskQty'] = quantity
                self.selected_task_info['Speed'] = speed

                # 更新任务信息显示
                self.update_task_info_display()

                QMessageBox.information(self.main_window, "应用成功",
                    f"任务参数已更新：\n任务数量: {quantity}\n速度: {speed}")
                print(f"✅ 任务参数已更新: 数量={quantity}, 速度={speed}")
            else:
                QMessageBox.warning(self.main_window, "错误", "请先选择任务文件夹！")

        except Exception as e:
            print(f"应用任务参数失败: {e}")
            QMessageBox.warning(self.main_window, "错误", f"应用任务参数失败: {e}")

    def validate_task_speed(self, text):
        """验证速度输入"""
        try:
            if text.strip():
                try:
                    speed = int(text)
                    if speed < 1 or speed > 200:
                        # 如果超出范围，恢复到有效范围
                        if speed < 1:
                            self.main_window.task_speed_edit.setText("1")
                        elif speed > 200:
                            self.main_window.task_speed_edit.setText("200")

                        QMessageBox.warning(
                            self.main_window,
                            "输入范围错误",
                            "速度必须在 1-200 之间"
                        )
                except ValueError:
                    # 如果不是数字，清空输入
                    self.main_window.task_speed_edit.setText("")
                    QMessageBox.warning(
                        self.main_window,
                        "输入格式错误",
                        "请输入有效的数字 (1-200)"
                    )
        except Exception as e:
            print(f"验证速度输入失败: {e}")

    def update_task_info_display(self):
        """更新任务信息显示"""
        try:
            if hasattr(self, 'selected_task_info') and self.selected_task_info and hasattr(self.main_window, 'task_info_edit') and self.main_window.task_info_edit:
                info_text = "【任务详情】\n"
                info_text += f"产线名称: {self.selected_task_info.get('ProductLineName', '未知')}\n"
                info_text += f"任务名称: {self.selected_task_info.get('MOTaskName', '未知')}\n"
                info_text += f"工单名称: {self.selected_task_info.get('MOName', '未知')}\n"
                info_text += f"工序名称: {self.selected_task_info.get('SpecificationName', '未知')}\n"
                info_text += f"产品名称: {self.selected_task_info.get('ProductName', '未知')}\n"
                info_text += f"批号: {self.selected_task_info.get('LotSN', '未知')}\n"
                info_text += f"任务数量: {self.selected_task_info.get('TaskQty', '未知')}\n"
                info_text += f"速度设置: {self.selected_task_info.get('Speed', '100')}\n"
                info_text += f"创建时间: {self.selected_task_info.get('create_time', '未知')}\n"
                info_text += f"操作员: {self.selected_task_info.get('operator', '未知')}\n"
                info_text += f"文件夹路径: {getattr(self, 'selected_task_folder', '未知')}"

                self.main_window.task_info_edit.setPlainText(info_text)
                print(f"✅ 已更新任务信息显示")
        except Exception as e:
            print(f"更新任务信息显示失败: {e}")

    def validate_task_quantity(self, text):
        """验证任务数量输入（0-72）"""
        try:
            if not text:
                return

            # 尝试转换为整数
            try:
                quantity = int(text)
                if quantity < 0 or quantity > 72:
                    # 如果超出范围，恢复到有效范围
                    if quantity < 0:
                        self.main_window.task_quantity_edit.setText("0")
                    elif quantity > 72:
                        self.main_window.task_quantity_edit.setText("72")

                    QMessageBox.warning(
                        self.main_window,
                        "输入范围错误",
                        "任务数量必须在 0-72 之间"
                    )
            except ValueError:
                # 如果不是数字，清空输入
                self.main_window.task_quantity_edit.setText("")
                QMessageBox.warning(
                    self.main_window,
                    "输入格式错误",
                    "请输入有效的数字 (0-72)"
                )
        except Exception as e:
            print(f"验证任务数量失败: {e}")



    def delete_task(self, row):
        """删除任务"""
        try:
            if row >= len(self.task_queue_data):
                QMessageBox.warning(self.main_window, "错误", "任务索引超出范围")
                return

            task_data = self.task_queue_data[row]
            task_id = task_data.get("task_id", f"任务{row + 1}")

            # 显示删除确认对话框
            reply = QMessageBox.question(
                self.main_window,
                "确认删除",
                f"确定要删除任务 '{task_id}' 吗？\n\n此操作不可撤销！",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # 从列表中删除
                deleted_task = self.task_queue_data.pop(row)

                # 保存数据
                self.save_task_data()

                # 更新表格
                self.update_task_queue_table()

                # 发送信号
                self.task_deleted.emit(task_id)

                QMessageBox.information(self.main_window, "删除成功", f"任务 '{task_id}' 已删除")
                print(f"已删除任务: {task_id}")

        except Exception as e:
            print(f"删除任务失败: {e}")
            QMessageBox.critical(self.main_window, "删除失败", f"删除任务时发生错误：\n{str(e)}")

    def view_task_detail(self, row):
        """查看任务详情"""
        try:
            # 检查任务状态，只允许查看"运行中"的任务
            status_widget = self.main_window.task_queue_table.cellWidget(row, 4)  # 状态列
            if not status_widget:
                QMessageBox.warning(self.main_window, "提示", "无法获取任务状态")
                return

            # 获取状态文本
            status_label = None
            for i in range(status_widget.layout().count()):
                item = status_widget.layout().itemAt(i)
                if item and item.widget():
                    widget = item.widget()
                    if isinstance(widget, QtWidgets.QLabel) and hasattr(widget, 'text'):
                        if widget.text() in ["运行中", "完成"]:
                            status_label = widget
                            break

            if not status_label or status_label.text() != "运行中":
                QMessageBox.information(self.main_window, "提示", "只能查看正在运行中的任务详情")
                return

            # 获取任务ID
            task_id = self.main_window.task_queue_table.item(row, 0).text() if self.main_window.task_queue_table.item(row, 0) else f"任务{row + 1}"

            # 先切换到检测任务页面
            if hasattr(self.main_window, 'switch_page'):
                self.main_window.switch_page(3)

            # 切换到任务详情标签页
            if hasattr(self.main_window, 'task_tab_widget') and self.main_window.task_tab_widget:
                self.main_window.task_tab_widget.setCurrentIndex(1)

            # 加载任务详情界面
            self.load_task_detail_ui(task_id)

        except Exception as e:
            print(f"查看任务详情失败: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.warning(self.main_window, "错误", f"查看任务详情失败: {e}")

    def load_task_detail_ui(self, task_id):
        """加载任务详情界面"""
        try:
            # 在任务详情tab中显示详情界面
            self.show_task_detail_in_tab(task_id)

        except Exception as e:
            print(f"加载任务详情界面失败: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.warning(self.main_window, "错误", f"加载任务详情界面失败: {e}")

    def show_task_detail_in_tab(self, task_id):
        """在标签页中显示任务详情"""
        try:
            # 查找任务数据
            task_data = None
            for task in self.task_queue_data:
                if task.get("task_id") == task_id:
                    task_data = task
                    break

            if not task_data:
                QMessageBox.warning(self.main_window, "错误", f"未找到任务: {task_id}")
                return

            # 加载blade.ui文件到任务详情tab
            self.load_blade_ui_in_task_detail(task_id, task_data)

            print(f"任务详情已加载: {task_id}")

        except Exception as e:
            print(f"显示任务详情失败: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self.main_window, "错误", f"显示任务详情失败:\n{str(e)}")

    def load_blade_ui_in_task_detail(self, task_id, task_data):
        """在任务详情tab中加载windows01.ui"""
        try:
            from PyQt5 import uic
            import os

            # windows01.ui文件路径（参照test_blade_ui.py）
            ui_file_path = os.path.join("blade", "static", "ui", "windows01.ui")

            # 检查文件是否存在
            if not os.path.exists(ui_file_path):
                # 尝试其他可能的路径
                alternative_paths = [
                    os.path.join("blade", "static", "ui", "11window.ui"),
                    os.path.join("templates", "windows01.ui"),
                    os.path.join("templates", "11window.ui"),
                    "windows01.ui",
                    "11window.ui"
                ]

                for alt_path in alternative_paths:
                    if os.path.exists(alt_path):
                        ui_file_path = alt_path
                        break
                else:
                    QMessageBox.warning(self.main_window, "文件不存在", f"找不到UI文件：\n{ui_file_path}")
                    return

            # 找到任务详情tab页面
            task_detail_tab = self.main_window.findChild(QtWidgets.QWidget, "task_detail_tab")
            if not task_detail_tab:
                QMessageBox.warning(self.main_window, "错误", "找不到任务详情tab页面")
                return

            # 清空任务详情tab的现有内容
            layout = task_detail_tab.layout()
            if layout:
                # 清除所有子控件
                while layout.count():
                    item = layout.takeAt(0)
                    if item.widget():
                        item.widget().deleteLater()
            else:
                # 创建新布局
                layout = QtWidgets.QVBoxLayout(task_detail_tab)
                layout.setContentsMargins(20, 20, 20, 20)
                layout.setSpacing(0)

            # 创建顶部工具栏（包含回退按钮）
            toolbar_widget = QtWidgets.QWidget()
            toolbar_layout = QtWidgets.QHBoxLayout(toolbar_widget)
            toolbar_layout.setContentsMargins(10, 10, 10, 10)

            # 回退按钮
            back_btn = QPushButton("← 返回任务列表")
            back_btn.setStyleSheet("""
                QPushButton {
                    background-color: #3498db;
                    color: white;
                    border: none;
                    border-radius: 5px;
                    padding: 8px 16px;
                    font-size: 12px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #2980b9;
                }
            """)
            back_btn.clicked.connect(self.return_to_task_list)

            # 任务标题
            task_label = QtWidgets.QLabel(f"任务详情 - {task_id}")
            task_label.setStyleSheet("""
                QLabel {
                    font-size: 16px;
                    font-weight: bold;
                    color: #2c3e50;
                    padding: 8px;
                }
            """)

            toolbar_layout.addWidget(back_btn)
            toolbar_layout.addWidget(task_label)
            toolbar_layout.addStretch()

            # 创建容器用于放置blade.ui
            detail_container = QtWidgets.QWidget()

            # 创建功能性任务详情控件
            detail_ui = self.create_functional_task_detail_widget(ui_file_path, task_id, task_data)

            # 设置容器布局
            container_layout = QtWidgets.QVBoxLayout(detail_container)
            container_layout.setContentsMargins(0, 0, 0, 0)
            container_layout.addWidget(detail_ui)

            # 添加到主布局
            layout.addWidget(toolbar_widget)
            layout.addWidget(detail_container)

            # 切换到任务详情标签页
            if hasattr(self.main_window, 'task_tab_widget') and self.main_window.task_tab_widget:
                self.main_window.task_tab_widget.setCurrentIndex(1)

            # 如果有动态标签页，添加到标签页中
            if hasattr(self.main_window, 'dynamic_tab_widget') and self.main_window.dynamic_tab_widget:
                tab_id = f"task_detail_{task_id}"
                self.main_window.dynamic_tab_widget.add_tab(tab_id, f"详情: {task_id}")

            print(f"任务详情已加载到tab中: {task_id}")

        except Exception as e:
            print(f"加载blade.ui失败: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self.main_window, "错误", f"加载任务详情失败:\n{str(e)}")

    def return_to_task_list(self):
        """返回任务列表"""
        try:
            if hasattr(self.main_window, 'task_tab_widget') and self.main_window.task_tab_widget:
                self.main_window.task_tab_widget.setCurrentIndex(2)
            print("已返回任务列表")
        except Exception as e:
            print(f"返回任务列表失败: {e}")
            QMessageBox.warning(self.main_window, "错误", f"返回任务列表失败: {e}")

    def create_task_detail_widget(self, task_data):
        """创建任务详情界面"""
        try:
            widget = QWidget()
            layout = QVBoxLayout(widget)

            # 任务基本信息
            info_group = QGroupBox("任务信息")
            info_layout = QGridLayout(info_group)

            info_layout.addWidget(QLabel("任务ID:"), 0, 0)
            info_layout.addWidget(QLabel(task_data.get("task_id", "未知")), 0, 1)

            info_layout.addWidget(QLabel("零件编号:"), 1, 0)
            info_layout.addWidget(QLabel(task_data.get("part_number", "未知")), 1, 1)

            info_layout.addWidget(QLabel("配方名称:"), 2, 0)
            info_layout.addWidget(QLabel(task_data.get("program_name", "未知")), 2, 1)

            info_layout.addWidget(QLabel("创建时间:"), 3, 0)
            info_layout.addWidget(QLabel(task_data.get("created_time", "未知")), 3, 1)

            info_layout.addWidget(QLabel("当前状态:"), 4, 0)
            status = task_data.get("status", "waiting")
            status_text = self.task_status.get(status, "未知")
            info_layout.addWidget(QLabel(status_text), 4, 1)

            layout.addWidget(info_group)
            # 扩展信息
            task_info = task_data.get("task_info", {})
            if task_info:
                extended_group = QGroupBox("详细信息")
                extended_layout = QGridLayout(extended_group)

                row = 0
                for key, value in task_info.items():
                    extended_layout.addWidget(QLabel(f"{key}:"), row, 0)
                    extended_layout.addWidget(QLabel(str(value)), row, 1)
                    row += 1

                layout.addWidget(extended_group)

            return widget

        except Exception as e:
            print(f"创建任务详情界面失败: {e}")
            return QWidget()

    def get_running_task_row(self):
        """获取当前运行中任务的行号"""
        try:
            if not self.main_window.task_queue_table:
                return -1

            for row in range(self.main_window.task_queue_table.rowCount()):
                # 查找状态列中的运行中任务
                status_widget = self.main_window.task_queue_table.cellWidget(row, 4)  # 状态列是第4列
                if status_widget:
                    # 查找状态标签
                    for child in status_widget.findChildren(QtWidgets.QLabel):
                        if hasattr(child, 'text') and child.text() == "运行中":
                            return row
            return -1
        except Exception as e:
            print(f"获取运行中任务失败: {e}")
            return -1

    def complete_running_task(self):
        """将当前运行中的任务状态更新为完成"""
        try:
            # 获取运行中任务的行号
            running_row = self.get_running_task_row()
            if running_row == -1:
                print("❌ 没有找到运行中的任务")
                return False

            # 更新任务队列数据中的状态
            if running_row < len(self.task_queue_data):
                self.task_queue_data[running_row]["status"] = "completed"
                print(f"✅ 更新任务数据状态为完成: 行 {running_row}")

            # 更新UI中的状态指示器
            self.add_status_indicator(running_row, 4, "完成")
            print(f"✅ 更新UI状态指示器为绿色: 行 {running_row}")

            # 保存任务数据
            self.save_task_data()

            # 发送任务完成信号
            if running_row < len(self.task_queue_data):
                task_id = self.task_queue_data[running_row].get("task_id", "")
                self.task_completed.emit(task_id)
                print(f"📡 发送任务完成信号: {task_id}")

            return True

        except Exception as e:
            print(f"❌ 完成任务状态更新失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def update_task_progress(self):
        """更新任务进度（定时器调用）- 已禁用进度显示"""
        try:
            # 查找运行中的任务，但不再更新进度显示
            for i, task in enumerate(self.task_queue_data):
                if task.get("status") == "running":
                    # 可以在这里添加其他任务状态检查逻辑
                    # 但不再更新进度条
                    pass

        except Exception as e:
            print(f"更新任务状态失败: {e}")

    def start_progress_timer(self):
        """启动进度更新定时器"""
        if not self.progress_timer.isActive():
            self.progress_timer.start(1000)  # 每秒更新一次
            print("任务进度更新定时器已启动")

    def stop_progress_timer(self):
        """停止进度更新定时器"""
        if self.progress_timer.isActive():
            self.progress_timer.stop()
            print("任务进度更新定时器已停止")

    def show_task_status(self):
        """显示任务状态"""
        try:
            if hasattr(self.main_window, 'task_tab_widget') and self.main_window.task_tab_widget:
                self.main_window.task_tab_widget.setCurrentIndex(2)  # 切换到任务状态标签页

                # 如果任务队列为空，添加示例任务
                if len(self.task_queue_data) == 0:
                    self.create_default_tasks()
                    self.update_task_queue_table()

        except Exception as e:
            print(f"显示任务状态失败: {e}")

    def on_program_selected(self, program_name):
        """当选择程序时更新任务信息"""
        try:
            if program_name and hasattr(self.main_window, 'task_info_edit') and self.main_window.task_info_edit:
                task_info = f"配方名称: {program_name}\n"
                task_info += f"配方类型: 标准检测\n"
                task_info += f"预计时间: 30分钟\n"
                task_info += f"检测项目: 外观、尺寸、缺陷"
                self.main_window.task_info_edit.setPlainText(task_info)

                # 自动生成任务号
                if hasattr(self.main_window, 'task_number_edit') and self.main_window.task_number_edit:
                    task_id = f"TASK_{int(time.time())}"
                    self.main_window.task_number_edit.setText(task_id)

        except Exception as e:
            print(f"程序选择处理失败: {e}")

    def get_task_list(self):
        """获取任务列表"""
        return self.task_queue_data.copy()

    def get_task_by_id(self, task_id):
        """根据ID获取任务"""
        for task in self.task_queue_data:
            if task.get("task_id") == task_id:
                return task.copy()
        return None

    def search_tasks(self, keyword):
        """搜索任务"""
        results = []
        keyword = keyword.lower()

        for task in self.task_queue_data:
            if (keyword in task.get("task_id", "").lower() or
                keyword in task.get("part_number", "").lower() or
                keyword in task.get("program_name", "").lower()):
                results.append(task.copy())

        return results

    def create_functional_task_detail_widget(self, ui_file_path, task_id, task_data):
        """创建功能性任务详情控件"""
        try:
            # 创建并返回控件实例，传递self作为task_management_module以便访问inspect_service
            detail_ui = FunctionalTaskDetailWidget(ui_file_path, task_id, task_data, task_management_module=self)
            return detail_ui

        except Exception as e:
            print(f"创建功能性任务详情控件失败: {e}")
            import traceback
            traceback.print_exc()
            # 返回一个简单的错误提示控件
            error_widget = QtWidgets.QLabel(f"加载任务详情失败: {str(e)}")
            error_widget.setStyleSheet("color: red; font-size: 14px; padding: 20px;")
            return error_widget

    def cleanup(self):
        """清理模块资源"""
        try:
            print("🧹 清理任务管理模块资源...")

            # 停止进度更新定时器
            if hasattr(self, 'progress_timer') and self.progress_timer:
                if self.progress_timer.isActive():
                    self.progress_timer.stop()
                    print("✅ 已停止任务进度更新定时器")

            # 保存任务数据
            self.save_task_data()

            print("✅ 任务管理模块资源清理完成")

        except Exception as e:
            print(f"❌ 清理任务管理模块资源失败: {e}")
            import traceback
            traceback.print_exc()
