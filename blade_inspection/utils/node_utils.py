#!/usr/bin/python
# -*- coding: utf-8 -*-

import typing as t
from hd_robotics.robotics import Robotics
from hdmtv.core.cdi import get_instance
from blade_inspection.core.inspect_service import InspectService


def get_robot_node(node_name: str = "robot_1") -> t.Optional[t.Any]:
    """
    从hd_robotics中获取robot_node实例
    
    Args:
        node_name: 节点名称，默认为"robot_1"
        
    Returns:
        robot_node实例，如果获取失败则返回None
        
    Example:
        # 获取默认的robot_1节点
        robot_node = get_robot_node()
        
        # 获取指定名称的节点
        robot_node = get_robot_node("robot_1")
        
        # 使用节点
        if robot_node:
            # 调用节点的方法
            response = robot_node.some_method(request)
    """
    try:
        # 方法1: 通过InspectService获取Robotics实例
        try:
            inspect_service = get_instance(clazz=InspectService)
            robotics = inspect_service._robotics
            print(f"✅ 通过InspectService获取Robotics实例成功")
        except Exception as e:
            print(f"⚠️ 通过InspectService获取失败: {e}")
            # 方法2: 直接获取Robotics实例
            try:
                robotics = get_instance(clazz=Robotics)
                print(f"✅ 直接获取Robotics实例成功")
            except Exception as e2:
                print(f"❌ 直接获取Robotics实例也失败: {e2}")
                return None
        
        # 获取节点实例
        if hasattr(robotics, 'get_node'):
            robot_node = robotics.get_node(node_name)
            if robot_node:
                print(f"✅ 成功获取节点: {node_name}")
                return robot_node
            else:
                print(f"⚠️ 节点 {node_name} 不存在或未初始化")
                return None
        else:
            print(f"❌ Robotics实例没有get_node方法")
            return None
            
    except Exception as e:
        print(f"❌ 获取robot_node失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def get_robot_node_by_prefix(prefix: str = "/localhost/ns/robot_1") -> t.Optional[t.Any]:
    """
    通过节点前缀获取robot_node实例
    
    Args:
        prefix: 节点前缀，默认为"/localhost/ns/robot_1"
        
    Returns:
        robot_node实例，如果获取失败则返回None
    """
    try:
        # 获取Robotics实例
        try:
            inspect_service = get_instance(clazz=InspectService)
            robotics = inspect_service._robotics
        except Exception:
            robotics = get_instance(clazz=Robotics)
        
        # 通过前缀获取节点
        if hasattr(robotics, 'get_node_by_prefix'):
            robot_node = robotics.get_node_by_prefix(prefix)
            if robot_node:
                print(f"✅ 通过前缀成功获取节点: {prefix}")
                return robot_node
            else:
                print(f"⚠️ 前缀 {prefix} 对应的节点不存在")
                return None
        else:
            print(f"❌ Robotics实例没有get_node_by_prefix方法")
            return None
            
    except Exception as e:
        print(f"❌ 通过前缀获取robot_node失败: {e}")
        return None


def get_all_nodes() -> t.Dict[str, t.Any]:
    """
    获取所有节点实例
    
    Returns:
        包含所有节点的字典，键为节点名称，值为节点实例
    """
    try:
        # 获取Robotics实例
        try:
            inspect_service = get_instance(clazz=InspectService)
            robotics = inspect_service._robotics
        except Exception:
            robotics = get_instance(clazz=Robotics)
        
        # 获取所有节点
        if hasattr(robotics, 'get_all_nodes'):
            nodes = robotics.get_all_nodes()
            print(f"✅ 成功获取所有节点，共 {len(nodes)} 个")
            return nodes
        elif hasattr(robotics, '_nodes'):
            nodes = robotics._nodes
            print(f"✅ 通过_nodes属性获取所有节点，共 {len(nodes)} 个")
            return nodes
        else:
            print(f"❌ 无法获取所有节点")
            return {}
            
    except Exception as e:
        print(f"❌ 获取所有节点失败: {e}")
        return {}


def check_robot_node_status(node_name: str = "robot_1") -> t.Dict[str, t.Any]:
    """
    检查robot_node的状态
    
    Args:
        node_name: 节点名称
        
    Returns:
        包含节点状态信息的字典
    """
    status = {
        "exists": False,
        "connected": False,
        "node_type": None,
        "error": None
    }
    
    try:
        robot_node = get_robot_node(node_name)
        if robot_node:
            status["exists"] = True
            status["node_type"] = type(robot_node).__name__
            
            # 检查连接状态
            if hasattr(robot_node, 'is_connected'):
                status["connected"] = robot_node.is_connected
            elif hasattr(robot_node, '_robot') and hasattr(robot_node._robot, 'is_connected'):
                status["connected"] = robot_node._robot.is_connected
            
            print(f"✅ 节点 {node_name} 状态检查完成")
        else:
            status["error"] = f"节点 {node_name} 不存在"
            
    except Exception as e:
        status["error"] = str(e)
        print(f"❌ 检查节点状态失败: {e}")
    
    return status


def get_robot_node_simple() -> t.Optional[t.Any]:
    """
    简化版本：快速获取robot_node实例

    这是最简单的获取方式，适合快速使用

    Returns:
        robot_node实例，如果获取失败则返回None

    Example:
        robot_node = get_robot_node_simple()
        if robot_node:
            # 使用robot_node
            pass
    """
    return get_robot_node("robot_1")


def get_robot_tcp_position(node_name: str = "robot_1") -> t.Optional[t.Dict[str, t.Any]]:
    """
    获取机器人TCP位置

    Args:
        node_name: 节点名称，默认为"robot_1"

    Returns:
        包含TCP位置信息的字典，如果获取失败则返回None

    Example:
        tcp_pos = get_robot_tcp_position()
        if tcp_pos:
            print(f"TCP位置: {tcp_pos}")
    """
    try:
        # 获取robot_node实例
        robot_node = get_robot_node(node_name)
        if not robot_node:
            print(f"❌ 无法获取节点: {node_name}")
            return None

        print(f"🔍 检查节点类型: {type(robot_node).__name__}")

        # 检查是否有get_tcp_position方法
        if not hasattr(robot_node, 'get_tcp_position'):
            print(f"❌ 节点 {node_name} 没有get_tcp_position方法")

            # 尝试查找替代方法
            alternative_methods = []
            for attr in dir(robot_node):
                if 'tcp' in attr.lower() or 'position' in attr.lower():
                    if callable(getattr(robot_node, attr, None)):
                        alternative_methods.append(attr)

            if alternative_methods:
                print(f"🔍 发现可能的替代方法: {alternative_methods}")

            # 检查是否有_robot属性
            if hasattr(robot_node, '_robot'):
                robot = robot_node._robot
                print(f"🔍 检查_robot属性: {type(robot).__name__}")
                if hasattr(robot, 'get_tcp_position'):
                    print(f"✅ _robot对象有get_tcp_position方法，尝试直接调用")
                    try:
                        result = robot.get_tcp_position()
                        print(f"✅ 直接调用_robot.get_tcp_position()成功: {result}")
                        return {
                            "code": 0,
                            "message": "通过_robot对象获取成功",
                            "data": {"tcp_position": result if isinstance(result, list) else []},
                            "tcp_position": result if isinstance(result, list) else []
                        }
                    except Exception as e:
                        print(f"❌ 直接调用_robot.get_tcp_position()失败: {e}")

            return None

        print(f"✅ 节点 {node_name} 有get_tcp_position方法")

        # 创建请求并调用get_tcp_position
        from hdmtv.core.node import Request
        request = Request(data={})
        print(f"🔄 调用robot_node.get_tcp_position(request)...")
        response = robot_node.get_tcp_position(request)
        print(f"📊 响应类型: {type(response)}")
        print(f"📊 响应内容: {response}")

        if response and hasattr(response, 'code'):
            if response.code == 0:  # 成功
                print(f"✅ 成功获取TCP位置")
                return {
                    "code": response.code,
                    "message": getattr(response, 'message', ''),
                    "data": getattr(response, 'data', {}),
                    "tcp_position": getattr(response, 'data', {}).get('tcp_position', [])
                }
            else:
                print(f"⚠️ 获取TCP位置失败，错误码: {response.code}")
                return {
                    "code": response.code,
                    "message": getattr(response, 'message', '获取失败'),
                    "data": {},
                    "tcp_position": []
                }
        else:
            print(f"⚠️ 响应格式异常: {response}")
            # 如果响应不是标准格式，尝试直接解析
            if isinstance(response, (list, tuple)) and len(response) >= 6:
                print(f"🔄 尝试直接解析响应为TCP位置")
                return {
                    "code": 0,
                    "message": "直接解析响应成功",
                    "data": {"tcp_position": list(response)},
                    "tcp_position": list(response)
                }
            return None

    except Exception as e:
        print(f"❌ 获取TCP位置时发生异常: {e}")
        import traceback
        traceback.print_exc()
        return None


def get_robot_tcp_position_simple() -> t.Optional[t.List[float]]:
    """
    简化版本：直接获取TCP位置坐标列表

    Returns:
        TCP位置坐标列表 [x, y, z, rx, ry, rz]，如果获取失败则返回None

    Example:
        tcp_pos = get_robot_tcp_position_simple()
        if tcp_pos:
            x, y, z, rx, ry, rz = tcp_pos
            print(f"位置: x={x}, y={y}, z={z}")
    """
    try:
        result = get_robot_tcp_position()
        if result and result.get("code") == 0:
            tcp_position = result.get("tcp_position", [])
            if tcp_position and len(tcp_position) >= 6:
                return tcp_position
        return None
    except Exception as e:
        print(f"❌ 简化获取TCP位置失败: {e}")
        return None


def debug_robot_node(node_name: str = "robot_1") -> t.Dict[str, t.Any]:
    """
    调试robot_node，检查其类型和可用方法

    Args:
        node_name: 节点名称

    Returns:
        包含调试信息的字典
    """
    debug_info = {
        "node_exists": False,
        "node_type": None,
        "node_class": None,
        "available_methods": [],
        "has_get_tcp_position": False,
        "all_attributes": [],
        "error": None
    }

    try:
        # 获取robot_node实例
        robot_node = get_robot_node(node_name)
        if not robot_node:
            debug_info["error"] = f"无法获取节点: {node_name}"
            return debug_info

        debug_info["node_exists"] = True
        debug_info["node_type"] = type(robot_node).__name__
        debug_info["node_class"] = str(type(robot_node))

        # 获取所有属性和方法
        all_attrs = dir(robot_node)
        debug_info["all_attributes"] = [attr for attr in all_attrs if not attr.startswith('_')]

        # 检查可调用的方法
        methods = []
        for attr in all_attrs:
            if not attr.startswith('_'):
                try:
                    obj = getattr(robot_node, attr)
                    if callable(obj):
                        methods.append(attr)
                except:
                    pass
        debug_info["available_methods"] = methods

        # 检查是否有get_tcp_position方法
        debug_info["has_get_tcp_position"] = hasattr(robot_node, 'get_tcp_position')

        # 检查其他常见的机器人方法
        common_methods = [
            'get_tcp_position', 'get_joint_position', 'joint_move', 'linear_move',
            'connect', 'disconnect', 'get_info', 'is_connected'
        ]

        available_common_methods = {}
        for method in common_methods:
            available_common_methods[method] = hasattr(robot_node, method)
        debug_info["common_methods"] = available_common_methods

        print(f"🔍 调试信息 - 节点: {node_name}")
        print(f"   类型: {debug_info['node_type']}")
        print(f"   类: {debug_info['node_class']}")
        print(f"   有get_tcp_position: {debug_info['has_get_tcp_position']}")
        print(f"   可用方法数量: {len(debug_info['available_methods'])}")
        print(f"   常见方法: {available_common_methods}")

        return debug_info

    except Exception as e:
        debug_info["error"] = str(e)
        print(f"❌ 调试robot_node失败: {e}")
        import traceback
        traceback.print_exc()
        return debug_info


# 使用示例
if __name__ == "__main__":
    # 示例1: 获取默认robot_node
    print("=== 示例1: 获取默认robot_node ===")
    robot_node = get_robot_node()
    if robot_node:
        print(f"成功获取robot_node: {type(robot_node)}")

    # 示例2: 获取指定名称的节点
    print("\n=== 示例2: 获取指定名称的节点 ===")
    robot_node = get_robot_node("robot_1")

    # 示例3: 通过前缀获取节点
    print("\n=== 示例3: 通过前缀获取节点 ===")
    robot_node = get_robot_node_by_prefix("/localhost/ns/robot_1")

    # 示例4: 获取所有节点
    print("\n=== 示例4: 获取所有节点 ===")
    all_nodes = get_all_nodes()
    for name, node in all_nodes.items():
        print(f"节点: {name}, 类型: {type(node)}")

    # 示例5: 检查节点状态
    print("\n=== 示例5: 检查节点状态 ===")
    status = check_robot_node_status("robot_1")
    print(f"节点状态: {status}")

    # 示例6: 简化版本
    print("\n=== 示例6: 简化版本 ===")
    robot_node = get_robot_node_simple()
    if robot_node:
        print(f"简化版本成功获取robot_node: {type(robot_node)}")
