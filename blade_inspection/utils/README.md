# Robot Node 获取工具

这个模块提供了在 blade_inspection 工程中获取和使用 hd_robotics 里面的 robot_node 实例的工具函数。

## 快速开始

### 最简单的使用方式

```python
from blade_inspection.utils import get_robot_node_simple, get_robot_tcp_position_simple

# 获取robot_node实例
robot_node = get_robot_node_simple()
if robot_node:
    print("成功获取robot_node实例")
    # 使用robot_node进行操作
else:
    print("获取robot_node失败")

# 快速获取TCP位置
tcp_pos = get_robot_tcp_position_simple()
if tcp_pos:
    x, y, z, rx, ry, rz = tcp_pos
    print(f"TCP位置: x={x:.3f}, y={y:.3f}, z={z:.3f}")
    print(f"TCP姿态: rx={rx:.3f}, ry={ry:.3f}, rz={rz:.3f}")
```

### 完整的使用方式

```python
from blade_inspection.utils import (
    get_robot_node,
    get_robot_node_by_prefix,
    get_all_nodes,
    check_robot_node_status,
    get_robot_tcp_position,
    get_robot_tcp_position_simple
)
from hdmtv.core.node import Request

# 方法1: 通过节点名称获取
robot_node = get_robot_node("robot_1")

# 方法2: 通过节点前缀获取
robot_node = get_robot_node_by_prefix("/localhost/ns/robot_1")

# 方法3: 获取所有节点
all_nodes = get_all_nodes()
robot_node = all_nodes.get("robot_1")

# 检查节点状态
status = check_robot_node_status("robot_1")
print(f"节点状态: {status}")

# 获取TCP位置 - 方法1: 完整版本
tcp_result = get_robot_tcp_position("robot_1")
if tcp_result and tcp_result.get("code") == 0:
    tcp_pos = tcp_result.get("tcp_position", [])
    print(f"TCP位置: {tcp_pos}")

# 获取TCP位置 - 方法2: 简化版本
tcp_pos = get_robot_tcp_position_simple()
if tcp_pos:
    x, y, z, rx, ry, rz = tcp_pos
    print(f"位置: x={x:.3f}, y={y:.3f}, z={z:.3f}")
    print(f"姿态: rx={rx:.3f}, ry={ry:.3f}, rz={rz:.3f}")

# 使用robot_node进行其他操作
if robot_node:
    # 手动调用get_tcp_position
    request = Request(data={})
    response = robot_node.get_tcp_position(request)
    print(f"手动获取位置响应: {response}")

    # 关节运动
    request = Request(data={
        "joint_pos": [0, 0, 0, 0, 0, 0],
        "speed": 50.0,
        "is_block": False,
        "move_mode": 0,
        "radian": False
    })
    response = robot_node.joint_move(request)
    print(f"运动响应: {response}")
```

## 函数说明

### get_robot_node_simple()
最简单的获取方式，返回默认的 robot_1 节点实例。

### get_robot_node(node_name)
通过节点名称获取节点实例。
- `node_name`: 节点名称，默认为 "robot_1"

### get_robot_node_by_prefix(prefix)
通过节点前缀获取节点实例。
- `prefix`: 节点前缀，默认为 "/localhost/ns/robot_1"

### get_all_nodes()
获取所有节点实例，返回字典格式。

### check_robot_node_status(node_name)
检查节点状态，返回包含状态信息的字典。

### get_robot_tcp_position(node_name)
获取机器人TCP位置的完整信息。
- `node_name`: 节点名称，默认为 "robot_1"
- 返回包含完整响应信息的字典，包括状态码、消息和TCP位置数据

### get_robot_tcp_position_simple()
简化版本，直接返回TCP位置坐标列表 [x, y, z, rx, ry, rz]。

## 配置说明

robot_node 的配置在 `blade_inspection/configs/config.yaml` 文件中：

```yaml
NODES:
  - node_name: "robot_1"
    endpoint: "localhost"
    namespace: "ns"
    node_class: "RobotNode"
    robot_class: "FakeRobot"
    description: "本地机器人通讯节点"
    ip_addr: "*************"
    # ... 其他配置
```

## 注意事项

1. 确保 Robotics 已经正确初始化
2. 确保 InspectService 已经启动
3. 节点名称必须与配置文件中的 node_name 一致
4. 使用前建议先检查节点状态

## 示例代码

完整的使用示例请参考 `blade_inspection/examples/robot_node_example.py` 文件。

## 错误处理

所有函数都包含了完善的错误处理机制：
- 如果获取失败，会返回 None
- 错误信息会打印到控制台
- 可以通过检查返回值来判断是否成功

## 依赖

- hd_robotics
- hdmtv
- blade_inspection.core.inspect_service
