# RobotNode参数传递完整指南

## 问题描述

在使用`@singleton`装饰器的RobotNode时，无法通过`get_instance(clazz=RobotNode)`直接传递参数，因为单例模式的实例在第一次创建时就确定了所有参数。

## 解决方案总览

### 方案1：通过配置文件传递参数（推荐）

**原理**：RobotNode实例是在Robotics系统初始化时根据`config.yaml`文件创建的，所有参数都通过配置文件传递。

**步骤**：
1. 修改`blade_inspection/configs/config.yaml`文件
2. 在NODES配置中添加自定义参数
3. 重新启动应用程序

**示例配置**：
```yaml
NODES:
  - node_name: "robot_1"
    endpoint: "localhost"
    namespace: "ns"
    node_class: "RobotNode"
    robot_class: "FakeRobot"
    
    # 标准参数
    ip_addr: "*************"
    rapid_rate: 0.8
    max_speed: 30
    
    # 自定义参数
    custom_param1: "value1"
    custom_param2: 123
    custom_param3: true
    collision_detection: true
    force_limit: 100.0
```

### 方案2：修改获取RobotNode的方式

**错误方式**：
```python
# ❌ 这种方式无法传递参数
self._robot_node = get_instance(clazz=RobotNode)
```

**正确方式**：
```python
# ✅ 通过Robotics系统获取已配置的实例
from blade_inspection.utils.node_utils import get_robot_node

self._robot_node = get_robot_node("robot_1")
```

或者：
```python
# ✅ 通过InspectService获取
from blade_inspection.core.inspect_service import InspectService
from hdmtv.core.cdi import get_instance

inspect_service = get_instance(clazz=InspectService)
robotics = inspect_service._robotics
self._robot_node = robotics.get_node("robot_1")
```

## 详细实现步骤

### 步骤1：修改配置文件

编辑`blade_inspection/configs/config.yaml`：

```yaml
NODES:
  - node_name: "robot_1"
    endpoint: "localhost"
    namespace: "ns"
    node_class: "RobotNode"
    robot_class: "FakeRobot"
    description: "本地机器人通讯节点"
    
    # 基本参数
    ip_addr: "*************"
    rapid_rate: 0.8
    validate_rapid_rate: 0.5
    max_speed: 30
    gripper_delay: 1.0
    alt_gripper_delay: 2.0
    tool_coordinate: [0.0, 0.0, 0.0, 0.0, 0.0, 0.0]
    
    # 添加你的自定义参数
    your_custom_param: "your_value"
    another_param: 123
```

### 步骤2：修改代码获取方式

在`robot_select_dialog.py`中：

```python
from blade_inspection.utils.node_utils import get_robot_node

class RobotSelectDialog(QDialog):
    def __init__(self, parent=None, program_module=None):
        super().__init__(parent=parent)
        
        # 正确的获取方式
        self._robot_node = get_robot_node("robot_1")
        if self._robot_node is None:
            print("⚠️ 无法获取robot_1节点，请检查配置文件")
```

### 步骤3：访问参数

```python
# 访问标准参数
if hasattr(self._robot_node, '_robot') and self._robot_node._robot:
    robot_device = self._robot_node._robot
    max_speed = getattr(robot_device, '_max_speed', 30)
    rapid_rate = getattr(robot_device, '_rapid_rate', 0.8)

# 访问自定义参数
custom_param = getattr(self._robot_node, '_your_custom_param', 'default_value')
```

## 参数类型支持

配置文件支持以下参数类型：

```yaml
# 字符串
string_param: "hello world"

# 数字
int_param: 123
float_param: 3.14

# 布尔值
bool_param: true

# 列表
list_param: [1, 2, 3]
coordinate_param: [0.0, 0.0, 0.0, 0.0, 0.0, 0.0]

# 字典
dict_param:
  key1: value1
  key2: value2
```

## 最佳实践

### 1. 参数命名规范
- 使用下划线分隔：`my_custom_param`
- 避免与系统参数冲突
- 使用有意义的名称

### 2. 参数验证
在RobotNode子类中添加参数验证：

```python
class CustomRobotNode(RobotNode):
    def __init__(self, node_name, endpoint, namespace, **options):
        super().__init__(node_name, endpoint, namespace, **options)
        
        # 获取并验证自定义参数
        self._force_limit = options.get('force_limit', 100.0)
        if self._force_limit <= 0:
            raise ValueError("force_limit must be positive")
```

### 3. 默认值设置
始终为自定义参数提供合理的默认值：

```python
self._custom_param = options.get('custom_param', 'default_value')
```

### 4. 参数文档化
在配置文件中添加注释说明参数用途：

```yaml
# 自定义参数
force_limit: 100.0        # 力限制 (N)
collision_detection: true # 是否启用碰撞检测
```

## 故障排除

### 问题1：参数不生效
**原因**：修改配置文件后没有重启程序
**解决**：重新启动应用程序或重新初始化Robotics系统

### 问题2：获取不到RobotNode
**原因**：InspectService未正确初始化
**解决**：确保InspectService已经启动并正确加载配置

### 问题3：参数访问失败
**原因**：参数名称不匹配或类型错误
**解决**：检查配置文件中的参数名称和类型

## 示例文件

- `blade_inspection/examples/robot_node_parameter_example.py` - 完整示例代码
- `blade_inspection/examples/config_with_custom_params.yaml` - 配置文件示例
- `blade_inspection/templates/robot_select_dialog.py` - 修改后的对话框代码

## 总结

1. **RobotNode使用单例模式**，参数必须通过配置文件传递
2. **修改config.yaml文件**中的NODES配置来添加参数
3. **使用正确的方式获取RobotNode实例**：`get_robot_node("robot_1")`
4. **重新启动程序**使参数生效
5. **通过属性访问**或配置对象访问参数

这种方式确保了参数的一致性和系统的稳定性，是处理单例模式参数传递的最佳实践。
