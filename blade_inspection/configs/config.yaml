INSPECT:
  cache_path: "D:/blade_inspect"
NODES:
  - node_name: "robot_1"
    endpoint: "localhost"
    namespace: "ns"
    node_class: "RobotNode"
    robot_class: "FakeRobot"
    description: "本地机器人通讯节点"
    ip_addr: "*************"
    rapid_rate: 0.8
    validate_rapid_rate: 0.5
    max_speed: 30
    gripper_delay: 1.0
    alt_gripper_delay: 2.0
    tool_coordinate:
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.0
    - 0.0
  - node_name: "plc_1"
    endpoint: "localhost"
    namespace: "ns"
    node_class: "PLCNode"
    plc_class: "FakePLC"
    description: "本地总控PLC通讯节点"
    ip_addr: "*************"
    con_type: 1
  - node_name: "visual_1"
    endpoint: "localhost"
    namespace: "ns"
    node_class: "VisualRecognizerNode"
    recognizer_class: "GeneralRecognizer"
    description: "视觉识别节点"
    model_path: "detection_model.engine"
    input_shape: [640, 640, 3]
    engine_type: "paddle"
    label_map: ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"]
    camera_config:
      device_index: 0
      exposure_time: 10000
      gain: 1.0
    luminance_config:
      port: "COM1"
      baudrate: 9600
      num_channels: 4
      intensities:
        level: [100, 150, 200]
      wait: 0.2
ACTION:
  exception_code:
    - "ERROR"
    - "LICENSE_EXPIRED"
    - "ROBOT_ERROR"
    - "PLC_ERROR"
TOPIC_ENDPOINTS:
    localhost: "opc.tcp://127.0.0.1:4840"
    remote: "opc.tcp://127.0.0.1:4840"
SERVICE_ENDPOINTS:
    localhost: "127.0.0.1:4851"
SERVICE:
    max_workers: 8
CLIENT:
    max_workers: 8
TIMER:
    max_workers: 8


