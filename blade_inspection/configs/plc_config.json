{"info_map": {"system_alarm": {"command": "read_bool", "kwargs": {"area": "DB", "db_num": 301, "offset": 8, "bool_index": 0}}, "is_auto_mode": {"command": "read_bool", "kwargs": {"area": "DB", "db_num": 301, "offset": 8, "bool_index": 4}}, "origin_ok": {"command": "read_bool", "kwargs": {"area": "DB", "db_num": 301, "offset": 9, "bool_index": 0}}, "load_conveyor_running": {"command": "read_bool", "kwargs": {"area": "DB", "db_num": 301, "offset": 10, "bool_index": 2}}, "unload_conveyor_running": {"command": "read_bool", "kwargs": {"area": "DB", "db_num": 301, "offset": 10, "bool_index": 4}}, "welder_ready": {"command": "read_bool", "kwargs": {"area": "DB", "db_num": 301, "offset": 10, "bool_index": 6}}, "welder_running": {"command": "read_bool", "kwargs": {"area": "DB", "db_num": 301, "offset": 10, "bool_index": 7}}, "welder_gun_closed": {"command": "read_bool", "kwargs": {"area": "DB", "db_num": 301, "offset": 11, "bool_index": 0}}, "welder_gun_opened": {"command": "read_bool", "kwargs": {"area": "DB", "db_num": 301, "offset": 11, "bool_index": 1}}, "emergency_stop": {"command": "read_bool", "kwargs": {"area": "DB", "db_num": 301, "offset": 14, "bool_index": 0}}, "servo_lift_position": {"command": "read_real", "kwargs": {"area": "DB", "db_num": 301, "offset": 4}}}, "parse_map": {"check blade unload": [{"command": "compare_bool_until", "kwargs": {"area": "DB", "db_num": 300, "offset": 2, "bool_index": 0, "value": true}}], "start blowing air": [{"command": "write_bool", "kwargs": {"area": "DB", "db_num": 300, "offset": 2, "bool_index": 0, "value": false}}], "check blow air completed": [{"command": "compare_bool_until", "kwargs": {"area": "DB", "db_num": 300, "offset": 0, "bool_index": 4, "value": true}}], "part loading": [{"command": "compare_bool_until", "kwargs": {"area": "DB", "db_num": 301, "offset": 11, "bool_index": 4, "value": true}}], "start load conveyor": [{"command": "write_and_reset_bool", "kwargs": {"area": "DB", "db_num": 300, "offset": 0, "bool_index": 0, "value": true, "delay": 0.5}}], "reset load conveyor": [{"command": "write_and_reset_bool", "kwargs": {"area": "DB", "db_num": 300, "offset": 0, "bool_index": 6, "value": true, "delay": 0.5}}], "reset unload conveyor": [{"command": "write_and_reset_bool", "kwargs": {"area": "DB", "db_num": 300, "offset": 0, "bool_index": 7, "value": true, "delay": 0.5}}], "localize request": [{"command": "write_bool", "kwargs": {"area": "DB", "db_num": 300, "offset": 0, "bool_index": 2, "value": true}}, {"command": "write_and_reset_bool", "kwargs": {"area": "DB", "db_num": 300, "offset": 0, "bool_index": 6, "value": true, "delay": 0.5}}, {"command": "compare_bool_until", "kwargs": {"area": "DB", "db_num": 301, "offset": 0, "bool_index": 2, "value": true}}, {"command": "write_bool", "kwargs": {"area": "DB", "db_num": 300, "offset": 0, "bool_index": 2, "value": false}}], "weld a": [{"command": "write_bool", "kwargs": {"area": "DB", "db_num": 300, "offset": 8, "bool_index": 0, "value": true}}, {"command": "write_bool", "kwargs": {"area": "DB", "db_num": 300, "offset": 0, "bool_index": 3, "value": true}}, {"command": "compare_bool_until", "kwargs": {"area": "DB", "db_num": 301, "offset": 0, "bool_index": 3, "value": true}}, {"command": "write_bool", "kwargs": {"area": "DB", "db_num": 300, "offset": 0, "bool_index": 3, "value": false}}], "weld start": [{"command": "write_bool", "kwargs": {"area": "DB", "db_num": 300, "offset": 1, "bool_index": 0, "value": true}}], "weld complete": [{"command": "write_bool", "kwargs": {"area": "DB", "db_num": 300, "offset": 1, "bool_index": 0, "value": false}}, {"command": "compare_bool_until", "kwargs": {"area": "DB", "db_num": 301, "offset": 0, "bool_index": 3, "value": true}}, {"command": "write_bool", "kwargs": {"area": "DB", "db_num": 300, "offset": 0, "bool_index": 3, "value": false}}], "weld error": [{"command": "write_bool", "kwargs": {"area": "DB", "db_num": 300, "offset": 1, "bool_index": 1, "value": true}}], "weld b": [{"command": "write_bool", "kwargs": {"area": "DB", "db_num": 300, "offset": 8, "bool_index": 0, "value": true}}, {"command": "write_bool", "kwargs": {"area": "DB", "db_num": 300, "offset": 0, "bool_index": 3, "value": true}}, {"command": "compare_bool_until", "kwargs": {"area": "DB", "db_num": 301, "offset": 0, "bool_index": 3, "value": true}}, {"command": "write_bool", "kwargs": {"area": "DB", "db_num": 300, "offset": 0, "bool_index": 3, "value": false}}], "start exhaust": [{"command": "write_bool", "kwargs": {"area": "DB", "db_num": 300, "offset": 12, "bool_index": 1, "value": true}, "comment": "焊枪开始排气"}, {"command": "write_bool", "kwargs": {"area": "DB", "db_num": 300, "offset": 12, "bool_index": 2, "value": false}, "comment": "焊枪开始排气"}], "stop exhaust": [{"command": "write_bool", "kwargs": {"area": "DB", "db_num": 300, "offset": 12, "bool_index": 1, "value": false}, "comment": "焊枪停止排气"}, {"command": "write_bool", "kwargs": {"area": "DB", "db_num": 300, "offset": 12, "bool_index": 2, "value": true}, "comment": "焊枪停止排气"}], "pin use times": [{"command": "read_int", "kwargs": {"area": "DB", "db_num": 301, "offset": 18}, "comment": "钨针使用次数"}], "reset pin use times": [{"command": "write_and_reset_bool", "kwargs": {"area": "DB", "db_num": 300, "offset": 18, "bool_index": 0, "value": true, "delay": 0.5}, "comment": "钨针使用次数复位"}]}, "op_map": {"set_welder_pos": {"command": "write_real", "kwargs": {"area": "DB", "db_num": 300, "offset": 4}}}}