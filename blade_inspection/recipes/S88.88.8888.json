{"name": "S88.88.8888", "validated": true, "process": [{"prefix": "/localhost/ns/robot_1", "action": "RobotMoveAction", "commands_list": [{"command": "joint_move", "kwargs": {"joint_pos": [-132.955902099609, -30.9456787109375, 53.33203125, 49.2403564453125, 83.480224609375, 144.491455078125], "is_block": false, "move_mode": 0, "speed": 100.0, "radian": false}}], "post_tasks": [2], "pre_tasks": [], "task_id": 1}, {"prefix": "/localhost/ns/plc_1", "action": "PLCAction", "commands_list": [{"command": "parse_plc_command", "kwargs": {"command_name": "check blade unload"}}], "post_tasks": [3], "pre_tasks": [1], "task_id": 2}, {"prefix": "/localhost/ns/robot_1", "action": "RobotMoveAction", "commands_list": [{"command": "joint_move", "kwargs": {"joint_pos": [-132.955902099609, -30.9456787109375, 53.33203125, 49.2403564453125, 83.480224609375, 144.491455078125], "is_block": false, "move_mode": 0, "speed": 100.0, "radian": false}}], "post_tasks": [4], "pre_tasks": [2], "task_id": 3}, {"prefix": "/localhost/ns/robot_1", "action": "BoxGraspAction", "commands_list": [{"command": "get_process", "kwargs": {"first_pos": [1145.23816, -6.68270636, 844.937439, 2.0943949627668554, 1.3548984996752644e-07, 0.7853980970749367], "row_space": [44.91477714285712, 0.39306233714285715, 0.0], "col_space": [-0.5307600000001003, 40.09241368, 0.0], "row_num": 8, "col_num": 3, "row_first": true}}], "post_tasks": [5], "pre_tasks": [3], "task_id": 4}, {"prefix": "/localhost/ns/robot_1", "action": "RobotMoveAction", "commands_list": [{"command": "joint_move", "kwargs": {"joint_pos": [-132.955902099609, -30.9456787109375, 53.33203125, 49.2403564453125, 83.480224609375, 144.491455078125], "is_block": false, "move_mode": 0, "speed": 100.0, "radian": false}}], "post_tasks": [6], "pre_tasks": [4], "task_id": 5}, {"prefix": "/localhost/ns/robot_1", "action": "RobotMoveAction", "commands_list": [{"command": "joint_move", "kwargs": {"joint_pos": [-132.955902099609, -30.9456787109375, 53.33203125, 49.2403564453125, 83.480224609375, 144.491455078125], "is_block": false, "move_mode": 0, "speed": 100.0, "radian": false}}], "post_tasks": [7], "pre_tasks": [5], "task_id": 6}, {"prefix": "/localhost/ns/robot_1", "action": "RobotMoveAction", "commands_list": [{"command": "linear_move", "kwargs": {"end_pos": [0.0, 0.0, 100.0, 0.0, 0.0, 0.0], "is_block": false, "move_mode": 1, "speed": 30.0}}], "post_tasks": [8], "pre_tasks": [6], "task_id": 7}, {"prefix": "/localhost/ns/robot_1", "action": "RobotMoveAction", "commands_list": [{"command": "joint_move", "kwargs": {"joint_pos": [-132.955902099609, -30.9456787109375, 53.33203125, 49.2403564453125, 83.480224609375, 144.491455078125], "is_block": false, "move_mode": 0, "speed": 100.0, "radian": false}}], "post_tasks": [9], "pre_tasks": [7], "task_id": 8}, {"prefix": "/localhost/ns/plc_1", "action": "PLCAction", "commands_list": [{"command": "parse_plc_command", "kwargs": {"command_name": "start blowing air"}}], "post_tasks": [10], "pre_tasks": [8], "task_id": 9}, {"prefix": "/localhost/ns/plc_1", "action": "PLCAction", "commands_list": [{"command": "parse_plc_command", "kwargs": {"command_name": "check blow air completed"}}], "post_tasks": [11], "pre_tasks": [9], "task_id": 10}, {"prefix": "/localhost/ns/robot_1", "action": "RobotMoveAction", "commands_list": [{"command": "linear_move", "kwargs": {"end_pos": [0.0, 0.0, 100.0, 0.0, 0.0, 0.0], "is_block": false, "move_mode": 1, "speed": 30.0}}], "post_tasks": [12], "pre_tasks": [10], "task_id": 11}, {"prefix": "/localhost/ns/robot_1", "action": "RobotMoveAction", "commands_list": [{"command": "joint_move", "kwargs": {"joint_pos": [-132.955902099609, -30.9456787109375, 53.33203125, 49.2403564453125, 83.480224609375, 144.491455078125], "is_block": false, "move_mode": 0, "speed": 100.0, "radian": false}}], "post_tasks": [13], "pre_tasks": [11], "task_id": 12}, {"prefix": "/localhost/ns/robot_1", "action": "RobotMoveAction", "commands_list": [{"command": "joint_move", "kwargs": {"joint_pos": [-132.955902099609, -30.9456787109375, 53.33203125, 49.2403564453125, 83.480224609375, 144.491455078125], "is_block": false, "move_mode": 0, "speed": 100.0, "radian": false}}], "post_tasks": [14], "pre_tasks": [12], "task_id": 13}, {"prefix": "/localhost/ns/robot_1", "action": "RobotMoveAction", "commands_list": [{"command": "joint_move", "kwargs": {"joint_pos": [-132.955902099609, -30.9456787109375, 53.33203125, 49.2403564453125, 83.480224609375, 144.491455078125], "is_block": false, "move_mode": 0, "speed": 100.0, "radian": false}}], "post_tasks": [15], "pre_tasks": [13], "task_id": 14}, {"prefix": "/localhost/ns/visual_1", "action": "VisualAction", "commands_list": [{"command": "identify_furnace_batch_number", "kwargs": {}}], "post_tasks": [16], "pre_tasks": [14], "task_id": 15}, {"prefix": "/localhost/ns/robot_1", "action": "RobotMoveAction", "commands_list": [{"command": "joint_move", "kwargs": {"joint_pos": [-132.955902099609, -30.9456787109375, 53.33203125, 49.2403564453125, 83.480224609375, 144.491455078125], "is_block": false, "move_mode": 0, "speed": 100.0, "radian": false}}], "post_tasks": [17], "pre_tasks": [15], "task_id": 16}, {"prefix": "/localhost/ns/visual_1", "action": "VisualAction", "commands_list": [{"command": "visual_inspection_one", "kwargs": {}}], "post_tasks": [18], "pre_tasks": [16], "task_id": 17}, {"prefix": "/localhost/ns/robot_1", "action": "RobotMoveAction", "commands_list": [{"command": "joint_move", "kwargs": {"joint_pos": [-132.955902099609, -30.9456787109375, 53.33203125, 49.2403564453125, 83.480224609375, 144.491455078125], "is_block": false, "move_mode": 0, "speed": 100.0, "radian": false}}], "post_tasks": [19], "pre_tasks": [17], "task_id": 18}, {"prefix": "/localhost/ns/visual_1", "action": "VisualAction", "commands_list": [{"command": "visual_inspection_two", "kwargs": {}}], "post_tasks": [20], "pre_tasks": [18], "task_id": 19}, {"prefix": "/localhost/ns/robot_1", "action": "RobotMoveAction", "commands_list": [{"command": "joint_move", "kwargs": {"joint_pos": [-132.955902099609, -30.9456787109375, 53.33203125, 49.2403564453125, 83.480224609375, 144.491455078125], "is_block": false, "move_mode": 0, "speed": 100.0, "radian": false}}], "post_tasks": [21], "pre_tasks": [19], "task_id": 20}, {"prefix": "/localhost/ns/visual_1", "action": "VisualAction", "commands_list": [{"command": "visual_inspection_three", "kwargs": {}}], "post_tasks": [22], "pre_tasks": [20], "task_id": 21}, {"prefix": "/localhost/ns/robot_1", "action": "RobotMoveAction", "commands_list": [{"command": "joint_move", "kwargs": {"joint_pos": [-132.955902099609, -30.9456787109375, 53.33203125, 49.2403564453125, 83.480224609375, 144.491455078125], "is_block": false, "move_mode": 0, "speed": 100.0, "radian": false}}], "post_tasks": [23], "pre_tasks": [21], "task_id": 22}, {"prefix": "/localhost/ns/robot_1", "action": "RobotMoveAction", "commands_list": [{"command": "joint_move", "kwargs": {"joint_pos": [-132.955902099609, -30.9456787109375, 53.33203125, 49.2403564453125, 83.480224609375, 144.491455078125], "is_block": false, "move_mode": 0, "speed": 100.0, "radian": false}}], "post_tasks": [24], "pre_tasks": [22], "task_id": 23}, {"prefix": "/localhost/ns/robot_1", "action": "BoxReleaseAction", "commands_list": [{"command": "put_process", "kwargs": {"first_pos": [1145.23816, -6.68270636, 844.937439, 2.0943949627668554, 1.3548984996752644e-07, 0.7853980970749367], "row_space": [44.91477714285712, 0.39306233714285715, 0.0], "col_space": [-0.5307600000001003, 40.09241368, 0.0], "row_num": 8, "col_num": 3, "row_first": true}}], "post_tasks": [25], "pre_tasks": [23], "task_id": 24}, {"prefix": "/localhost/ns/robot_1", "action": "RobotMoveAction", "commands_list": [{"command": "joint_move", "kwargs": {"joint_pos": [-132.955902099609, -30.9456787109375, 53.33203125, 49.2403564453125, 83.480224609375, 144.491455078125], "is_block": false, "move_mode": 0, "speed": 100.0, "radian": false}}], "post_tasks": [26], "pre_tasks": [24], "task_id": 25}, {"prefix": "/localhost/ns/robot_1", "action": "RobotMoveAction", "commands_list": [{"command": "joint_move", "kwargs": {"joint_pos": [-132.955902099609, -30.9456787109375, 53.33203125, 49.2403564453125, 83.480224609375, 144.491455078125], "is_block": false, "move_mode": 0, "speed": 100.0, "radian": false}}], "post_tasks": [], "pre_tasks": [25], "task_id": 26}]}